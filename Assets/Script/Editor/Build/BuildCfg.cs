using System.Collections.Generic;
using UnityEngine;

public class BuildCfg
{
    public static string IOSLibi2CppPath = Application.dataPath + "/../HybridCLRData/iOSBuild/build/libil2cpp.a";
    public static string BASE_PATH = Application.dataPath + "/../ZW_OutPut/";
    public static string ANDROID_PROJECT_NAME = BASE_PATH + "app";
    public static string ANDROID_PROJECT_AABNAME = BASE_PATH + "app/aab";
    public static string XCODE_PROJECT_NAME = BASE_PATH + "XcodeProject";
    public static string ANDROID_PROJECT_PATH = BASE_PATH + "AndroidProj";
    public static string ANDROID_BUILDGRADLE_PATH = ANDROID_PROJECT_PATH + "/launcher/build.gradle";
    public static string ANDROID_Mode_Name = "onemt_android_mode";
    public static string ANDROID_MODE_PATH = Application.dataPath + "/../BuildTools/Android/"+ ANDROID_Mode_Name;
    public static string XCODE_PROJECT_LIBIL2CPP = XCODE_PROJECT_NAME + "/Libraries/libil2cpp.a";

    public static Dictionary<string, string> AotDllMap = new Dictionary<string, string>()
                {
                    { "mscorlib.dll" ,"WWW1.bytes"},
                    { "System.dll","WWW2.bytes"},
                    { "System.Core.dll" ,"WWW3.bytes"},
                    { "LitJson.dll","WWW4.bytes"},
                    { "Google.Protobuf.dll","WWW5.bytes"},
                };

    public const string META_EXTENSION = ".meta";
    // bundle 生成目录
    public static string BundleEditorResPath = Application.dataPath + "/../AssetBundles/";
    public static string BundleEditorAOTPath = Application.dataPath + "/../HybridCLRData/AssembliesPostIl2CppStrip/";
    public static string StrippedAOTDllsTempProj = Application.dataPath + "/../HybridCLRData/StrippedAOTDllsTempProj/";
    public static string BundleEditorGameDLLPath = Application.dataPath + "/../HybridCLRData/HotUpdateDlls/";
    public static string VersionModePath = Application.dataPath + "/ResUpdate/VersionMode";
    // 拷贝的目录
    public static string BundleStreamAssetsPath = Application.dataPath + "/StreamingAssets/bundle/";

}
