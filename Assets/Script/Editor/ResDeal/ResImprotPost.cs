using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.U2D;
using UnityEngine;
using UnityEngine.U2D;

public class ResImprotPost : AssetPostprocessor
{
    public bool CustomRes(string path)
    {
        if (this.assetPath.Contains("custom.") || this.assetPath.Contains("Custom.")
         || this.assetPath.Contains("_ct.") || this.assetPath.Contains("SplatAlpha"))
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    public void OnPreprocessModel()
    {
        if (!CustomRes(this.assetPath))
        {
            OnPreprocessAssets.OnPreprocessModel(assetImporter, this.assetPath);
        }
    }
    public void OnPreprocessTexture()
    {
        if (!CustomRes(this.assetPath))
        {
            OnPreprocessAssets.OnPreprocessTexture(assetImporter, this.assetPath);
        }
    }
    public void OnPreprocessAudio()
    {
        AudioImporter impor = assetImporter as AudioImporter;
        impor.forceToMono = true;
        impor.preloadAudioData = false;

        AudioImporterSampleSettings sampleSettings = impor.GetOverrideSampleSettings(OnPreprocessAssets.Android);

        sampleSettings.loadType = AudioClipLoadType.CompressedInMemory;
        sampleSettings.compressionFormat = AudioCompressionFormat.Vorbis;
        sampleSettings.sampleRateSetting = AudioSampleRateSetting.OptimizeSampleRate;
        impor.SetOverrideSampleSettings(OnPreprocessAssets.Android, sampleSettings);

        sampleSettings = impor.GetOverrideSampleSettings(OnPreprocessAssets.IOS);
        sampleSettings.loadType = AudioClipLoadType.CompressedInMemory;
        sampleSettings.compressionFormat = AudioCompressionFormat.Vorbis;
        sampleSettings.sampleRateSetting = AudioSampleRateSetting.OptimizeSampleRate;

        impor.SetOverrideSampleSettings(OnPreprocessAssets.IOS, sampleSettings);
        UnityEditor.SerializedObject serializedObject = new SerializedObject(impor);
        SerializedProperty normalize = serializedObject.FindProperty("m_Normalize");
        normalize.boolValue = false;
        serializedObject.ApplyModifiedProperties();
    }
}