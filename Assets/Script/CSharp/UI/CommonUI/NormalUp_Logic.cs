using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class NormalUp
    {
        private float m_Time = 2.5f;
        protected override void OnInitImp()
        {
            m_Time = 2.5f;
        }
        public override void OnFreshUI()
        {
        }
        private void Update()
        {
            m_Time -= Time.deltaTime;
            if (m_Time <= 0)
            {
                m_Time = 1000;
                TouchClose();
            }
        }

        protected override void OnCloseImp()
        {
        }


    }
}
