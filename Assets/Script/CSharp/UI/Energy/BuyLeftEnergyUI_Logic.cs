using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class BuyLeftEnergyUI
    {
        private LDNetEnergyMgr m_EnergyMgr;
        private long m_Price;
        private int m_CurNum = 1;

        protected override void OnInitImp()
        {
            m_EnergyMgr = Global.gApp.gSystemMgr.gEnergyMgr;
            m_btn_close.button.AddListener(TouchClose);
            m_AddBtn.button.AddListener(OnAdd);
            m_ReduceBtn.button.AddListener(OnReduce);
            m_buyBtn.button.AddListener(OnBuy);

            OnFreshUI();
        }

        public override void OnFreshUI()
        {
            int maxLeftTiems = GlobalCfg.Data.Get(80011).valueInt;
            int buyEnergyNum = GlobalCfg.Data.Get(80007).valueInt;

            m_LeftEnergy.text.SetText(UiTools.Localize(95511, $"{m_EnergyMgr.Data.StorageTimes}/{maxLeftTiems}"));
            m_LeftEnergyNum.text.SetText(UiTools.Localize(95512, buyEnergyNum));

            LDCommonTools.DestoryChildren(m_BuyItem.rectTransform);
            LDCommonItem energyItemData = new LDCommonItem(LDCommonType.Item, LDSpecialItemId.Energy, buyEnergyNum);
            LDUIPrefabTools.GetKnapsackItemUI(energyItemData, m_BuyItem.rectTransform);

            UpdateNum();
        }

        private void UpdateNum()
        {
            string buyEnergyCost = GlobalCfg.Data.Get(80012).valueString;
            m_CurNum = Mathf.Min(m_CurNum, m_EnergyMgr.Data.StorageTimes);
            m_Num.text.SetText(m_CurNum);
            LDCommonItem itemData = new LDCommonItem(buyEnergyCost);
            m_Price = itemData.Num;
            m_Cost.text.SetText(m_Price * m_CurNum);
        }

        private void OnAdd()
        {
            m_CurNum++;
            m_CurNum = Mathf.Min(m_CurNum, m_EnergyMgr.Data.StorageTimes);
            UpdateNum();
        }

        private void OnReduce()
        {
            m_CurNum--;
            m_CurNum = Mathf.Max(m_CurNum, 0);
            UpdateNum();
        }

        private void OnBuy()
        {
            if(m_CurNum <= 0)
            {
                return;
            }

            if (!UiTools.TryOpenBuyDiamondUI(m_Price * m_CurNum))
            {
                m_EnergyMgr.SendBuyStorgeEnergy(m_CurNum);
            }
        }

        protected override void OnCloseImp()
        {

        }
    }
}