using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class MechaSpinPopUI
    {
        private LDNetMechaSpinMgr m_SpinMgr;
        private MechaSpinExchangeShopItem m_Cfg;
        private Action<int> m_OnBuy;
        private int m_Count;
        private int m_MaxCount;
        private int m_Price;
        private long m_OwnedCount;
        protected override void OnInitImp()
        {
            m_SpinMgr = Global.gApp.gSystemMgr.gMechaSpinMgr;
            m_ClickTips.button.AddListener(TouchClose);
            m_Reduce_Button.button.AddListener(OnReduce);
            m_Add_Button.button.AddListener(OnAdd);
            m_Max_Button.button.AddListener(OnMax);
            m_buyBTN.button.AddListener(OnBuy);
        }

        public override void OnFreshUI() { }

        public void RefreshUI(MechaSpinExchangeShopItem cfg, Action<int> onBuy)
        {
            m_Cfg = cfg;
            m_OnBuy = onBuy;

            RefreshReward();
            RefreshNum();
        }

        private void RefreshReward()
        {
            // 道具
            LDCommonItem rewardItem = new LDCommonItem(m_Cfg.item);
            RectTransform rt = m_IconNode.rectTransform;
            LDCommonTools.DestoryChildren(rt);
            LDUIPrefabTools.GetKnapsackItemUI(rewardItem, rt);

            // 名称
            m_Item_Name.text.SetTips(rewardItem.Name);
            m_Item_Name.text.SetQuaColor(rewardItem.Quality);

            // 描述
            m_Item_Tips.text.SetText(rewardItem.Desc);

            // 库存
            m_SpinMgr.GetShopSurplusNumInfo(m_Cfg.id, out int surplusNum);
            m_numNode.gameObject.SetActive(m_Cfg.packTotal > 0);
            m_Item_Num.text.SetText(surplusNum);

            // 消耗
            LDCommonItem commonItem = new LDCommonItem(m_Cfg.cost);
            m_Price = (int)commonItem.Num;
            m_OwnedCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(commonItem.Id);
            m_MaxCount = Mathf.Min(Mathf.FloorToInt((float)m_OwnedCount / m_Price), surplusNum);
            m_Count = Math.Min(1, m_MaxCount);
        }

        private void RefreshNum()
        {
            m_num.text.SetText(m_Count);
            m_cost.text.SetCostText(m_OwnedCount, m_Price * m_Count);
        }

        private void OnAdd()
        {
            if (m_Count < m_MaxCount)
            {
                m_Count++;
                RefreshNum();
            }
        }

        private void OnReduce()
        {
            if (m_Count > 0)
            {
                m_Count--;
                RefreshNum();
            }
        }

        private void OnMax()
        {
            m_Count = m_MaxCount;
            RefreshNum();
        }

        private void OnBuy()
        {
            m_OnBuy?.Invoke(m_Count);
            TouchClose();
        }

        protected override void OnCloseImp()
        {

        }
    }
}