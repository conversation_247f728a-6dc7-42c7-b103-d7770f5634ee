using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class GuildLogoUI
    {
        private LDGuildBriefInfo m_GuildInfo;
        public void RefreshUI(LDGuildBriefInfo guildInfo)
        {
            m_GuildInfo = guildInfo;

            if (guildInfo == null)
            {
                m_empty.gameObject.SetActive(true);
                Global.LogError($"公会徽章 : 数据为空");
                return;
            }


            SetUI(guildInfo.Level, guildInfo.LogoId);
        }

        public void RefreshUI(int level, int logoId)
        {
            SetUI(level, logoId);
        }

        private void SetUI(int level, int logoId)
        {
            m_empty.gameObject.SetActive(false);
            if (GuildLevel.Data.TryGet(level, out GuildLevelItem lvCfg))
            {
                m_frame.gameObject.SetActive(true);
                LoadSprite(m_frame.image, lvCfg.guildFlag);
            }
            if (GuildLogo.Data.TryGet(logoId, out GuildLogoItem logoCfg))
            {
                m_icon.gameObject.SetActive(true);
                LoadSprite(m_icon.image, logoCfg.guildLogo);
            }
        }
    }
}