using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class UAVMainUI_LineItem2_item_hecheng2
    {
        private KnapsackItem m_KnapsackItem;
        private LDUAVDataContent m_UAVItem;
        private UAVMainUI m_UAVMainUI;
        public override void ItemInit()
        {
            if (m_KnapsackItem == null)
            {
                m_KnapsackItem = LDUIPrefabTools.GetKnapsackItemUI(m_scalenode.rectTransform);
                m_KnapsackItem.transform.SetAsFirstSibling();
                m_KnapsackItem.SetClickCallback(OnClickBack);
            }
        }

        public void InitData(UAVMainUI uAVMainUI, LDUAVDataContent commonItem)
        {
            ItemInit();
            m_UAVMainUI = uAVMainUI;
            m_UAVItem = commonItem;
            LDUIPrefabTools.InitKnapsackItem(m_KnapsackItem, m_UAVItem.CommonItem);
            FreshHeChengState();
        }
        private void OnClickBack()
        {
            if (m_UAVItem != null)
            {
                if (m_UAVMainUI.CanSelectUAVItemMat(m_UAVItem.CommonItem.Id))
                {
                   m_UAVMainUI.SetSelectHeChengItem(m_UAVItem);
                }
                else
                {
                    Global.gApp.gUiMgr.OpenUIAsync<Item_Tips>(LDUICfg.Item_Tips).SetLoadedCall(baseUI => { baseUI.InitData(m_UAVItem.CommonItem); });
                }
            }
        }
        public void FreshHeChengState()
        {
            m_UAVMainUI.CalcHeChengItemUIInfo(this, m_UAVItem);
        }
    }
}
