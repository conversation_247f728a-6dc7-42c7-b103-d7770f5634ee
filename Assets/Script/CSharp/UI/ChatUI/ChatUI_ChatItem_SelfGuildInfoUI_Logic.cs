using LD.Protocol;

namespace LD
{
    public partial class ChatUI_ChatItem_SelfGuildInfoUI
    {
        protected override float m_TextBgWidthOffset => 38f;
        protected override float m_TextBgHeightOffset => 24f;
        protected override float m_MaxItemHeight => 210f;

        protected override float m_MaxChatTextWidth => 538f; //638;

        public override void RefreshChatItem(LDNetChatInfo chatInfo)
        {
            m_Chat_Txt.text.raycastTarget = true;
            LDUIPrefabTools.InitOtherRoleHead(m_RoleHead.gameObject, chatInfo.Sender.HeadInfo);
            m_Name_Txt.text.SetText(chatInfo.Sender.Name);
            m_Level_Txt.text.SetText(chatInfo.Sender.Level);

            long senderId = chatInfo.Sender.PlayerId;
            var guildMemberInfo = Global.gApp.gSystemMgr.gGuildMgr.GetMemberInfo(senderId);
            if (guildMemberInfo == null)
            {
                m_Leader_Dec.gameObject.SetActive(false);
                m_ViceLeader_Dec.gameObject.SetActive(false);
            }
            else
            {
                m_Leader_Dec.gameObject.SetActive(guildMemberInfo.JobInfo.JobType == GuildJobType.JobChairman);
                m_ViceLeader_Dec.gameObject.SetActive(guildMemberInfo.JobInfo.JobType == GuildJobType.JobViceChairman);
            }

            if (chatInfo.Content.Media == MediaType.MediaText.GetHashCode() || chatInfo.Content.Media == MediaType.MediaLink.GetHashCode())
            {
                ShowChatText(chatInfo);
            }

            if (chatInfo.Content.Media == MediaType.MediaEmoji.GetHashCode())
            {
                RefreshChatEmoji(chatInfo);
            }
        }

        private void ShowChatText(LDNetChatInfo chatInfo)
        {
            m_Chat_Txt.gameObject.SetActive(true);
            m_Chat_BG.gameObject.SetActive(true);
            m_Chat_Emoji.gameObject.SetActive(false);

            m_Chat_Txt.text.SetText(chatInfo.GetShowContent());
            RefreshItemSize(m_Chat_Txt, m_Chat_BG);
        }

        private void RefreshChatEmoji(LDNetChatInfo chatInfo)
        {
            var content = chatInfo.GetShowContent();
            int emojiId = LDParseTools.IntParse(content);
            if (emojiId > 0)
            {
                m_Chat_Txt.gameObject.SetActive(false);
                m_Chat_BG.gameObject.SetActive(false);
                m_Chat_Emoji.gameObject.SetActive(true);
                CreateEmoji(m_Chat_Emoji.rectTransform, emojiId);
                // SetChatBgSize(m_Chat_BG, 260f, 210f);
                SetChatContentSize(300);
            }
        }
    }
}