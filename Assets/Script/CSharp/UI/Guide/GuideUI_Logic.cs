using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class GuideUI
    {
        private MRGuideItem m_GuideItem;//引导的数据
        public MRGuideItem GuideItem { get => m_GuideItem; }

        private Transform m_GuideTransform;
        private Transform m_OldParent;
        private Vector3 m_OldPos;
        private Vector3 m_OldScale;
        private float m_CurTime;
        private float m_WeakCheckTime = 5;

        private float m_StrongCheckTime = 20;
        private int m_StrongClickCount = 10;


        private int m_Index;


        protected override void OnInitImp()
        {
            m_RayCastMask.button.onClick.AddListener(OnClickMask);
            //Global.gApp.gUiMgr.TryRefreshUIOrder(LDUICfg.RoleLevelUpUI);
        }

        public override void OnFreshUI() { }

        private void Update()
        {
            if (GuideItem == null)
            {
                return;
            }
            if (GuideItem.guideType == 0)
            {
                m_CurTime += Time.deltaTime;
                if (m_CurTime >= m_WeakCheckTime)
                {
                    if (!Global.gApp.gSystemMgr.gGuideMgr.MatchTopUI(GuideItem))
                    {
                        Global.gApp.gSystemMgr.gGuideMgr.ForceEndGuid((LDGuideType)GuideItem.group);
                    }
                }
            }
            else if (GuideItem.guideType == 1)
            {
                m_StrongCheckTime -= Time.deltaTime;
                if (m_StrongCheckTime <= 0)
                {
                    Global.gApp.gSystemMgr.gGuideMgr.ForceEndGuid((LDGuideType)GuideItem.group);
                }
            }
        }
        public void StartGuide(MRGuideItem guideItem, Transform guideTransform)//开始引导
        {
            Global.Log($"打开引导UI group = {guideItem.group}  id = {guideItem.id}");
            if (guideItem.guideType == 1)
            {
                if (guideTransform == null || guideTransform.parent == null)
                {
                    Global.gApp.gSystemMgr.gGuideMgr.ForceEndGuid((LDGuideType)guideItem.group);
                    Global.Log("引导中断");
                    return;
                }
                m_Index = guideTransform.GetSiblingIndex();
                m_GuideTransform = guideTransform;
                m_OldParent = m_GuideTransform.parent;
            }

            m_GuideItem = guideItem;
            m_WeakCheckTime = guideItem.skipTime;


            PlayUIAni();
            SetMask();
        }

        private void PlayUIAni()
        {
            AddTouchMask();
            for (int i = 0; i < m_Nodes.rectTransform.childCount; i++)
            {
                m_Nodes.rectTransform.GetChild(i).gameObject.SetActive(false);
            }

            AddTimer(0.5f, 1, OnAniOver);
        }

        private void OnAniOver(float arg1, bool arg2)
        {
            RemoveTouchMask();

            if (m_GuideTransform != null)
            {
                m_OldPos = m_GuideTransform.localPosition;
                m_OldScale = m_GuideTransform.localScale;
                m_GuideNode.gameObject.SetActive(true);
                m_GuideTransform.SetParent(m_GuideNode.rectTransform.transform, true);
            }

            SetPointer();
            SetDialog();
            SetAni();

        }

        // 指针
        private void SetPointer()
        {
            int pointer = m_GuideItem.place;
            if (pointer > 0)
            {
                m_Click.gameObject.SetActive(true);
                m_Click.rectTransform.localPosition = GetTargetPoint();
                if (m_GuideItem.placeOffset.Length > 1)
                {
                    m_Click.rectTransform.localPosition += new Vector3(m_GuideItem.placeOffset[0], m_GuideItem.placeOffset[1]);
                }


                m_Shou_Left.gameObject.SetActive(m_GuideItem.place == 1);
                m_Shou_Right.gameObject.SetActive(m_GuideItem.place == 2);
                m_Shou_Up.gameObject.SetActive(m_GuideItem.place == 3);
                m_Shou_Down.gameObject.SetActive(m_GuideItem.place == 4);
            }
        }

        // 对话框
        private void SetDialog()
        {
            if (m_GuideItem.size == 1)
            {
                m_Guid_S.gameObject.SetActive(true);
                m_Guid_S.rectTransform.localPosition = GetTargetPoint(); ;
                if (m_GuideItem.boxOffset.Length > 1)
                {
                    m_Guid_S.rectTransform.localPosition += new Vector3(m_GuideItem.boxOffset[0], m_GuideItem.boxOffset[1]);
                }


                m_Text.text.SetTips(m_GuideItem.boxTxt);
            }

            if (m_GuideItem.size == 2)
            {
                m_Guid_M.gameObject.SetActive(true);
                m_guidTxt.text.SetTips(m_GuideItem.boxTxt);
                if (m_GuideItem.boxOffset.Length > 1)
                {
                    m_Guid_M.rectTransform.localPosition += new Vector3(m_GuideItem.boxOffset[0], m_GuideItem.boxOffset[1]);
                }


                m_RoleImage.gameObject.SetActive(false);
                m_RoleImage_R.gameObject.SetActive(false);
                if (!string.IsNullOrEmpty(m_GuideItem.guideMecha))
                {
                    if (m_GuideItem.NPCOffset == 1)
                    {
                        m_RoleImage.gameObject.SetActive(true);
                        LoadSprite(m_RoleImage.image, m_GuideItem.guideMecha);
                    }
                    if (m_GuideItem.NPCOffset == 2)
                    {
                        m_RoleImage_R.gameObject.SetActive(true);
                        LoadSprite(m_RoleImage_R.image, m_GuideItem.guideMecha);
                    }
                }
            }
        }

        // 动画
        private void SetAni()
        {
            int aniOrder = m_GuideItem.uiAni;
            if (aniOrder > 0)
            {
                m_Ani.gameObject.SetActive(true);
                for (int i = 0; i < m_Ani.rectTransform.childCount; i++)
                {
                    m_Ani.rectTransform.GetChild(i).gameObject.SetActive(i == aniOrder - 1);
                }
            }
        }

        // 遮罩
        private void SetMask()
        {
            m_RayCastMask.gameObject.SetActive(m_GuideItem.maskVal > 0);
            Image image = m_RayCastMask.rectTransform.GetComponent<Image>();
            Color color = image.color;
            color.a = m_GuideItem.maskVal;
            image.color = color;
        }

        private Vector3 GetTargetPoint()
        {
            if (m_GuideTransform != null)
            {
                return m_GuideTransform.localPosition;
            }
            return Vector3.zero;
        }

        private void OnClickMask()
        {
            if (m_GuideItem.guideType != 1)
            {
                return;
            }

            m_StrongClickCount--;
            if (m_StrongClickCount <= 0)
            {
                Global.gApp.gSystemMgr.gGuideMgr.ForceEndGuid((LDGuideType)GuideItem.group);
            }
        }

        protected override void OnCloseImp()
        {
            if (m_GuideTransform != null && m_GuideItem != null)
            {
                Global.gApp.gSystemMgr.gGuideMgr.ForceEndGuid((LDGuideType)m_GuideItem.group);
                Global.Log($"非正常关闭  引导UI id = {m_GuideItem.id}");
            }
        }

        public override void TouchClose()
        {
            ResetGuideTsf();

            Global.Log($"关闭引导UI id = {m_GuideItem.id}");

            base.TouchClose();
        }

        public void ResetGuideTsf()
        {
            if (m_GuideTransform != null)
            {
                if (m_OldParent != null)
                {
                    m_GuideTransform.SetParent(m_OldParent, false);
                    m_GuideTransform.SetSiblingIndex(m_Index);
                    m_GuideTransform.localPosition = m_OldPos;
                    m_GuideTransform.localScale = m_OldScale;
                    m_GuideTransform = null;
                }
                else
                {
                    Global.gApp.gResMgr.DestroyGameObj(m_GuideTransform.gameObject);
                }
            }
        }
    }
}