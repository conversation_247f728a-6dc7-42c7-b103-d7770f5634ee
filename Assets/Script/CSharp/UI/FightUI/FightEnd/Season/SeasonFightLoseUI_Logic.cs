using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class SeasonFightLoseUI
    {
        private LDNetSeasonMgr m_SeasonMgr;
        private LDSeasonFightScene m_FightScene;
        private LDSeasonPassHandler m_Handler;
        private LDPassInfo m_PassInfo;
        private SeasonMissionItem m_MissionCfg;
        private LDSeasonFightEndUIData m_UIData;
        private LDMainRole m_MainRole;
        private bool m_IsTimeout;

        private MainFightResultUI m_ResultUI;

        protected override void OnInitImp()
        {
            Global.gApp.CurFightScene.Pause();

            m_ResultUI = m_MainFightResultUI.gameObject.GetComponent<MainFightResultUI>();

            m_ResultUI.SendBtn.button.AddListener(OnSend);

            m_SeasonMgr = Global.gApp.gSystemMgr.gSeasonMgr;
            m_FightScene = Global.gApp.CurFightScene as LDSeasonFightScene;
            m_Handler = m_FightScene.gPassHandler as LDSeasonPassHandler;
            m_PassInfo = m_FightScene.gPassInfo;
            m_MissionCfg = SeasonMission.Data.Get(m_PassInfo.PassData.MisssionId);
            m_MainRole = Global.gApp.CurFightScene.GetLocalRole();
            m_IsTimeout = m_PassInfo.GetGameEndReason() == LDGameEndReason.TimeLimit;

            m_ResultUI.SendBtn.gameObject.SetActive(true);
            m_ResultUI.root.gameObject.SetActive(false);
            
            int adRewardThree = Global.gApp.gSystemMgr.gVipMgr.GetPrivilegeNum(PrivilegeType.DoubleChangeTriple);
            if (adRewardThree == 0)
            {
                m_ResultUI.fightresult_lable_ad_a.text.SetTips(9517);
            }
            else
            {
                m_ResultUI.fightresult_lable_ad_a.text.SetTips(9547);
            }

            SendFightLose();
        }

        public override void OnFreshUI() { }

        public override void OnFreshUI(int val)
        {
            if (val == 1) // 广告双倍奖励
            {
                int adRewardThree = Global.gApp.gSystemMgr.gVipMgr.GetPrivilegeNum(PrivilegeType.DoubleChangeTriple);
                if (adRewardThree == 0)
                {
                    m_ResultUI?.DoubleDropReward(2);
                }
                else
                {
                    m_ResultUI?.DoubleDropReward(3);
                }
            }
        }

        public override void OnFreshUI(LDUIDataBase uiData)
        {
            m_UIData = uiData as LDSeasonFightEndUIData;

            m_ResultUI.SendBtn.gameObject.SetActive(false);
            m_ResultUI.root.gameObject.SetActive(true);


            RefreshResultInfo();
        }

        private void RefreshResultInfo()
        {
            m_ResultUI.InitUI();
            m_ResultUI.SetWin(false);
            m_ResultUI.SetPassName(m_SeasonMgr.GetSeasonMissionName(m_MissionCfg.id));
            //float progress = m_IsTimeout ? -1 : Global.gApp.CurFightScene.gVictoryMgr.GetProgress();
            m_ResultUI.SetPassProgress(-1); // 不显示进度
            m_ResultUI.SetTimeout(m_IsTimeout);
            m_ResultUI.SetNewRecord(false);
            m_ResultUI.SetDropInfo(m_UIData.Rewards);
            m_ResultUI.SetDamageInfo(m_MainRole.DamageRecord.GetDamageContent());
            int adId = m_SeasonMgr.GetBattleEndDoubleRewardADId();
            m_ResultUI.SetDoubleReward(adId, OnAd);
            m_ResultUI.SetPassTime(m_UIData.Time);
        }

        private void SendFightLose()
        {
            float time = m_PassInfo.GetGameEndReason() == LDGameEndReason.Debug ? 999 : m_PassInfo.GetTime();
            List<LDCommonItem> commonItems = new List<LDCommonItem>();
            foreach (string item in m_Handler.GetGameEndGainItems())
            {
                LDCommonItem data = new LDCommonItem(item);
                commonItems.Add(data);
            }
            int missionEndStatus = m_PassInfo.GetGameEndReason() == LDGameEndReason.GiveUp ? LDNetPassResult.GiveUp: LDNetPassResult.Failure;
            m_SeasonMgr.SendMissionEndReq(m_PassInfo.PassData.MisssionId, time, missionEndStatus, Global.gApp.CurFightScene.gVictoryMgr.GetProgress(), commonItems, m_PassInfo.FightData.UniqueId, Global.gApp.CurFightScene.gWaveMgr.KillAICount);
        }

        private void OnSend()
        {
            SendFightLose();
        }

        private void OnAd()
        {
            int adId = m_SeasonMgr.GetBattleEndDoubleRewardADId();
            Global.gApp.gSdkMgr.gADMgr.Show(adId, LDUICfg.SeasonFightLoseUI, 1);
        }

        protected override void OnCloseImp()
        {

        }
    }
}