using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class UIStartGoldTaskModeUI
    {
        float m_CurTime = 0;

        protected override void OnInitImp()
        {
            m_CurTime = 2f;
            Global.gApp.CurFightScene.Pause();
        }
        private void Update()
        {
            m_CurTime -= Time.deltaTime;
            if (m_CurTime < 0)
            {
                TouchClose();
                m_CurTime = 10000;
            }
        }
        public override void OnFreshUI()
        {
        }
        protected override void OnCloseImp()
        {
            Global.gApp.CurFightScene.Resume();
        }


    }
}
