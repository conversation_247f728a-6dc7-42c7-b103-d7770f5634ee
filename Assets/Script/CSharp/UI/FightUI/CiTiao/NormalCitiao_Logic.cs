using DG.Tweening;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class NormalCitiao
    {
        public LDCiTiaoItemData Data;
        private UIFightCitiaoView m_CitiaoView;
        private UIFightCitiao4View m_CitiaoView4;
        private string m_LvRichTxtMax = "<color=#FFBA00>{0}</color><color=#68FAFF>/{1}</color>";
        private string m_LvRichTxtNormal = "{0}/{1}";
        private int m_Index = 0;
        private int m_MaxIndex = 1;
        private bool m_ShowLine = true;
        private string m_AnimName;

        public void Init(LDCiTiaoItemData data, UIFightCitiaoView fightCitiaoView,int index, int maxIndex)
        {
            Data = data;
            m_CitiaoView = fightCitiaoView;
            m_Index = index;
            m_MaxIndex = maxIndex;
            InitUI(data.CiTiaoGroup.GroupData, data.CitiaoItem.id, data.CiTiaoGroup.CitiaoLv);
        }


        public void Init4(LDCiTiaoItemData data, UIFightCitiao4View fightCitiaoView, int index, int maxIndex,bool showLine)
        {
            m_ShowLine = showLine;
            Data = data;
            m_CitiaoView4 = fightCitiaoView;
            m_Index = index;
            m_MaxIndex = maxIndex;
            InitUI(data.CiTiaoGroup.GroupData, data.CitiaoItem.id, data.CiTiaoGroup.CitiaoLv);
        }

        private ExpeditionCiTiaoView m_ExpeditionCiTiaoView;
        private LDCanSelectCiTiaoData m_ExpeditionData;

        public void Init(LDCanSelectCiTiaoData data, ExpeditionCiTiaoView fightCitiaoView, int index, int maxIndex)
        {
            m_ExpeditionCiTiaoView = fightCitiaoView;
            m_ExpeditionData = data;
            m_Index = index;
            m_MaxIndex = maxIndex;
            InitUI(data.ExpeditionCiTiaoDataItem.CiTiaoGroupData, data.CiTiaoId, data.CiTiaoLv);
        }

        private ExpeditionGiveCiTiaoView m_ExpeditionGiveUpCiTiaoView;

        public void Init(LDCanSelectCiTiaoData data, ExpeditionGiveCiTiaoView fightCitiaoView,int index,int maxIndex)
        {
            m_ExpeditionGiveUpCiTiaoView = fightCitiaoView;
            m_ExpeditionData = data;
            m_Index = index;
            m_MaxIndex = maxIndex;
            InitUI(data.ExpeditionCiTiaoDataItem.CiTiaoGroupData, data.CiTiaoId, data.CiTiaoLv - 1);
        }

        private void InitUI(LDCitiaoGroupData citiaoGroupData, int citiaoId, int citiaoLv)
        {
            citiao_BG_Btn.button.AddListener(OnSelect);
            CitiaoItem citiaoItem = Citiao.Data.Get(citiaoId);
            CitiaoGroupItem groupItem = CitiaoGroup.Data.Get(citiaoGroupData.CiTiaoGroupId);
            m_txt_xi_name.text.SetTips(citiaoItem.citiaoName);
            m_txt_xi_des.text.SetTips(citiaoItem.citiaoDesc);
            int nextLv = citiaoLv + 1;
            int maxLv = citiaoGroupData.MaxLv;
            if (nextLv >= maxLv)
            {
                m_txt_xi_level.text.text = string.Format(m_LvRichTxtMax, nextLv, maxLv);
            }
            else
            {
                m_txt_xi_level.text.text = string.Format(m_LvRichTxtNormal, nextLv, maxLv);
            }

            string icon = LDCiTiaoBaseGroup.GetCitiaoGroupIcon(groupItem);
            LoadSprite(m_img_diypart_icon.image, icon);
            
            if (citiaoItem.citiaoType == LDCiTiaoBaseGroup.NormalType)
            {
                m_PowerBg.gameObject.SetActive(false);
                m_NormalBg.gameObject.SetActive(true);
            }
            else if (citiaoItem.citiaoType == LDCiTiaoBaseGroup.PowerType)
            {
                m_PowerBg.gameObject.SetActive(true);
                m_NormalBg.gameObject.SetActive(false);
            }

            if (citiaoItem.optionalCondition.Length > 0)
            {
                m_related.gameObject.SetActive(true);
                LoadSprite(m_img_relatedIcon.image, citiaoGroupData.GetLinkIcon(citiaoId));
                m_txt_related.text.text = citiaoGroupData.GetLinkDesc2(citiaoId);
            }
            else
            {
                m_related.gameObject.SetActive(false);
            }

            if(m_MaxIndex > 0 && m_ShowLine)
            {
                if(m_MaxIndex == 1)
                {
                    m_citiao_line_2.gameObject.SetActive(true);
                    m_related.rectTransform.SetParent(m_related_2.rectTransform);
                }
                else if(m_MaxIndex == 2)
                {
                    int newIndex = m_Index + 1;
                    if (newIndex == 1)
                    {
                        m_citiao_line_1.gameObject.SetActive(true);
                        m_related.rectTransform.SetParent(m_related_1.rectTransform);
                    }
                    else if(newIndex == m_MaxIndex)
                    {
                        m_citiao_line_3.gameObject.SetActive(true);
                        m_related.rectTransform.SetParent(m_related_3.rectTransform);
                    }
                }
                else if(m_MaxIndex == 3)
                {
                    int newIndex = m_Index + 1;
                    if (newIndex == 1)
                    {
                        m_citiao_line_1.gameObject.SetActive(true);
                        m_related.rectTransform.SetParent(m_related_1.rectTransform);
                    }
                    else if (newIndex == 2)
                    {
                        m_citiao_line_2.gameObject.SetActive(true);
                        m_related.rectTransform.SetParent(m_related_2.rectTransform);
                    }
                    else if (newIndex == m_MaxIndex)
                    {
                        m_citiao_line_3.gameObject.SetActive(true);
                        m_related.rectTransform.SetParent(m_related_3.rectTransform);
                    }
                }
                m_related.rectTransform.localPosition = Vector3.zero;
            }
            if(citiaoItem.citiaoType == LDCiTiaoBaseGroup.PowerType)
            {
                m_AnimName = "fxani_zhandou_citiaobuff_open01";
            }
            else
            {
                m_AnimName = "fxani_zhandou_citiaobuff_open";
            }

            int dstQuality = LDCiTiaoItemData.IsRankCiTiao(citiaoId, true);
            if (dstQuality <= 0)
            {
                dstQuality = LDCiTiaoItemData.IsPowerCiTiao(citiaoId, true);
            }
            if (dstQuality > 0)
            {
                m_rank_1.gameObject.SetActive(true);
                m_rank_2.gameObject.SetActive(true);
                m_rank_3.gameObject.SetActive(true);
                LoadSprite(m_img_rankimg1.image, LDUIResTools.GetQualityMedalPath(dstQuality));
                LoadSprite(m_img_rankimg2.image, LDUIResTools.GetQualityMedalPath(dstQuality));
                LoadSprite(m_img_rankimg3.image, LDUIResTools.GetQualityMedalPath(dstQuality));
            }


            FreshAnim();
            base.ChangeLanguage();
        }
        public void FreshAnim()
        {
            if (m_root.animator.enabled)
            {
                m_root.animator.Play(m_AnimName, -1, 0);
            }
        }
        public void ShowAutoSelectAnim()
        {
            Sequence sequence = DOTween.Sequence();
            Tweener doT = transform.DOShakeScale(0.3f, 0.3f);
            doT.SetEase(Ease.InOutExpo);
            sequence.Append(doT);
            //Tweener doT2 = transform.DOShakeScale(1.3f, 0.2f);
            //doT2.SetEase(Ease.InElastic);
            //sequence.Append(doT2);
            //sequence.Play();
        }
        private void OnSelect()
        {
            if (m_CitiaoView != null)
            {
                m_CitiaoView.OnSelect(Data);
            }

            if (m_ExpeditionCiTiaoView != null)
            {
                m_ExpeditionCiTiaoView.OnSelect(m_ExpeditionData);
            }       
            if (m_CitiaoView4 != null)
            {
                m_CitiaoView4.OnSelect(Data);
            }

            if (m_ExpeditionGiveUpCiTiaoView != null)
            {
                m_ExpeditionGiveUpCiTiaoView.OnSelect(m_ExpeditionData);
            }
        }
    }
}