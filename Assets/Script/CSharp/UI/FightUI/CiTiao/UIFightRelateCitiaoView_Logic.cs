using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class UIFightRelateCitiaoView
    {
        private float m_CurTime = 3;
        protected override void OnInitImp()
        {
            m_AnimBtn.AddListener(TouchClose);
        }
        public void InitData(LDCiTiaoItemData data)
        {
            GameObject normalCiTiao =
                Global.gApp.gResMgr.InstantiateLoadObj(LDUICfg.NormalCitiao, ResSceneType.NormalRes, m_CitiaoPanel.rectTransform);

            NormalCitiao citiao_Normal = normalCiTiao.GetComponent<NormalCitiao>();
            citiao_Normal.gameObject.SetActive(true);
            citiao_Normal.root.animator.enabled = false;
            citiao_Normal.Init(data,null,0,0);
            citiao_Normal.citiao_BG_Btn.image.enabled = false;
            m_AnimatorNode.gameObject.SetActive(true);
            string iconPath = LDCiTiaoBaseGroup.GetCitiaoGroupIcon(data.CiTiaoGroup.CitiaoGroupItem);
            LoadSprite(m_trigger1_Icon.image, iconPath);
            LoadSprite(m_trigger2_Icon.image, data.GetLinkIcon());

            string tips = data.GetLinkDesc();
            m_txt_trigger_info.text.SetText(tips);
        }

        private void Update()
        {
            m_CurTime -= Time.deltaTime;
            if(m_CurTime <= 0)
            {
                m_CurTime = 9999;
                if(Global.gApp.gSystemMgr.AutoSelectOpen())
                {
                    TouchClose();
                }
            }
        }

        public override void OnFreshUI()
        {
        }
        protected override void OnCloseImp()
        {
            Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.UIFightCitiaoView, 1);
            Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.UIFightCitiao4View, 1);
        }
    }
}
