using System;

namespace LD
{
    public partial class CSarenaRankRewardUI_Rank
    {
        private LDNetCSArenaMgr m_CSArenaMgr;
        private int m_RankId;
        private Action<int> m_OnClick;

        public void RefreshUI(int rankId, Action<int> cb)
        {
            m_RankId = rankId;
            m_OnClick = cb;
            m_CSArenaMgr = Global.gApp.gSystemMgr.gCSArenaMgr;
            
            m_Btn.button.AddListener(OnClick);

            KfArenaRankInfoItem rankCfg = KfArenaRankInfo.Data.Get(rankId);
            m_rankName.text.SetTips(rankCfg.rankTitle);
            LoadSprite(m_rankIcon.image, rankCfg.rankIcon);
        }

        public void SetSelect(bool isSelect)
        {
            m_rankName.gameObject.SetActive(isSelect);
            m_Selected.gameObject.SetActive(isSelect);
            m_noSelected.gameObject.SetActive(!isSelect);
        }

        public void OnClick()
        {
            m_OnClick?.Invoke(m_RankId);
        }
    }
}