using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class PVETowerPartUI
    {
        private TowerMissionItem m_MissionCfg;
        private int m_MissionId;

        protected override void OnInitImp()
        {
            Global.gApp.CurFightScene.Pause();
            OnFreshUI();
        }

        public override void OnFreshUI()
        {
            m_MissionId = Global.gApp.CurFightScene.gPassInfo.PassData.MisssionId;
            m_MissionCfg = TowerMission.Data.Get(m_MissionId);

            RefreshBaseInfo();
        }

        private void RefreshBaseInfo()
        {
            int nameTips = 0;
            LDCommonItem itemData = null;
            if (m_MissionCfg.DIYpartsID[0] == LDDIYType.Mecha)
            {
                MechaItem mechaCfg = Mecha.Data.Get(m_MissionCfg.DIYpartsID[1]);
                nameTips = mechaCfg.name;
                itemData = new LDCommonItem(LDCommonType.Mecha, mechaCfg.id, 0);
            }
            else if (m_MissionCfg.DIYpartsID[0] == LDDIYType.Part)
            {
                DIYPartCfgItem parCfg = DIYPartCfg.Data.Get(m_MissionCfg.DIYpartsID[1]);
                nameTips = parCfg.name;
                itemData = new LDCommonItem(LDCommonType.DIYPart, parCfg.id, 0);
            }

            CitiaoItem citiaoCfg = Citiao.Data.Get(m_MissionCfg.AddEntry[0]);

            m_PartName.text.SetTips(itemData.Name);
            m_PartTips.text.SetTips(citiaoCfg.citiaoDesc);
            LoadSprite(m_PartItem.image, itemData.Icon);
        }

        protected override void OnCloseImp()
        {
            Global.gApp.CurFightScene.Resume();
        }
    }
}