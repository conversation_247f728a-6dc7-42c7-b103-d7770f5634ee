using UnityEngine;

namespace LD
{
    public class AssistSkillPreview
    {
        public int Level;
        public int Citiao;
        public int attr;
    }

    public partial class AssistMainUI_skill
    {
        private float m_ShowEffectDelay = -1;
        private float m_ShowEffectTime;

        private int m_ItemLevel = 0;
        private int m_SlotId = 0;

        public void RefreshCitiaoUI(int slotId, AssistSkillPreview citiao, bool playEffect)
        {
            m_SlotId = slotId;
            var slotInfo = Global.gApp.gSystemMgr.gAssistMgr.GetSlotInfo(slotId);
            int lv = slotInfo?.Level ?? 0;

            m_ItemLevel = citiao.Level;

            m_lockState.gameObject.SetActive(lv < citiao.Level);

            m_skill_info.text.SetTips(citiao.Citiao);

            m_LvText.text.SetTips(66306, citiao.Level);
            m_fx_saoguang.gameObject.SetActive(false);
        }

        public void RefreshAttrUI(int slotId, AssistSkillPreview attr, bool playEffect)
        {
            m_SlotId = slotId;
            var slotInfo = Global.gApp.gSystemMgr.gAssistMgr.GetSlotInfo(slotId);
            int lv = slotInfo?.Level ?? 0;

            m_ItemLevel = attr.Level;
            m_lockState.gameObject.SetActive(lv < attr.Level);

            m_skill_info.text.SetTips(66332, attr.attr / 100);

            m_LvText.text.SetTips(66306, attr.Level);
            m_fx_saoguang.gameObject.SetActive(false);
        }

        public void RefreshState()
        {
            var slotInfo = Global.gApp.gSystemMgr.gAssistMgr.GetSlotInfo(m_SlotId);
            int lv = slotInfo?.Level ?? 0;

            m_lockState.gameObject.SetActive(lv < m_ItemLevel);
            if (lv == m_ItemLevel)
            {
                m_fx_saoguang.gameObject.SetActive(true);
            }
        }

        public bool GetEffectState()
        {
            var slotInfo = Global.gApp.gSystemMgr.gAssistMgr.GetSlotInfo(m_SlotId);
            int lv = slotInfo?.Level ?? 0;

            m_lockState.gameObject.SetActive(lv < m_ItemLevel);
            if (lv == m_ItemLevel)
            {
                return true;
            }

            return false;
        }


        public void PlayShowEffect(float delay)
        {
            m_ShowEffectDelay = delay;
            m_ShowEffectTime = 0;
            m_root.gameObject.SetActive(false);
        }

        private void Update()
        {
            if (m_ShowEffectDelay > 0)
            {
                m_ShowEffectTime += Time.deltaTime;
                if (m_ShowEffectTime > m_ShowEffectDelay)
                {
                    m_ShowEffectDelay = -1;
                    m_root.gameObject.SetActive(true);
                    m_ani.animator.Play("fxani_huatiao02", -1, 0);
                }
            }
        }
    }
}