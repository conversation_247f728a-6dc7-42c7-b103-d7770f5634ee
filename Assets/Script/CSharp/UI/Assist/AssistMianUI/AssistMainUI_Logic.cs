using System.Collections.Generic;
using UnityEngine.UI;

namespace LD
{
    public partial class AssistMainUI
    {
        private int m_CurShowType = -1;

        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            m_TabMain_UncheckBtn.AddListener(OnClickMain);
            m_TabUnlock_UncheckBtn.AddListener(OnClickUnlock);
            m_TipsBtn.AddListener(OnTipBtnClick);
            InitConsole();
            InitLevelNode();

            RefreshView();

            AddTimer(0.1f, 1, (_, _) => { TryGuide(); });
        }

        private void TryGuide()
        {
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.Assist, m_AutoBtn.rectTransform, 2)) { return; }
        }

        protected override void OnCloseImp()
        {
            m_ModelLevel?.Dispose();
            foreach (CommonModelUI model in m_CommonModels)
            {
                model.Dispose();
            }
        }

        protected override void RegEventImp(bool addListener)
        {
            base.RegEventImp(addListener);
            Global.gApp.gMsgDispatcher.RegEvent(MsgIds.ItemChangeByBuy, ItemChangeByBuy, addListener);
        }

        private void ItemChangeByBuy()
        {
            if (m_CurShowType == 1)
            {
                RefreshLevelNode(false);
            }
        }

        public override void OnFreshUI()
        {
        }

        public override void OnFreshUI(int val)
        {
            if (val == 0)
            {
                if (m_CurShowType == 0)
                {
                    RefreshConsole(false);
                }
                else
                {
                    RefreshLevelNode(false);
                }
                PlayUIAudioClip(AudioConfig.UI_CommonEquip2);
            }
            else if (val == 1)
            {
                RefreshLevelNode(false);
                PlayUIAudioClip(AudioConfig.UI_CommonLvUp);
            }

            RefreshAttr();
        }

        public void RefreshView()
        {
            m_CurShowType = 0;
            ChangeTabBtnState();
            ResetView(true);
        }

        private void ChangeTabBtnState()
        {
            m_TabMain_CheckBtn.gameObject.SetActive(m_CurShowType == 0);
            m_TabMain_UncheckBtn.gameObject.SetActive(m_CurShowType != 0);

            m_TabUnlock_CheckBtn.gameObject.SetActive(m_CurShowType == 1);
            m_TabUnlock_UncheckBtn.gameObject.SetActive(m_CurShowType != 1);

            m_txt_partTitle.text.SetTips(m_CurShowType == 0 ? 66322 : 66323);
        }

        private void OnClickMain()
        {
            m_CurShowType = 0;
            ChangeTabBtnState();
            ResetView(false);
        }

        private void OnClickUnlock()
        {
            m_CurShowType = 1;
            ChangeTabBtnState();
            ResetView(true);
        }

        private void ResetView(bool needAnim)
        {
            if (m_CurShowType == 0)
            {
                RefreshConsoleView(needAnim);
            }
            else if (m_CurShowType == 1)
            {
                RefreshLevelNodeView(needAnim);
            }

            SetModelShow(m_CurShowType == 0);

            RefreshAttr();
        }

        //属性
        public void RefreshAttr()
        {
            m_upAttrAll.CacheInstanceList();
            var attrList = Global.gApp.gSystemMgr.gAssistMgr.GetAssistAttrAdditions();
            foreach (LDAttrAddition attrAddition in attrList)
            {
                var itemUI = m_upAttrAll.GetInstance(true);

                LoadSprite(itemUI.icon.image, attrAddition.GetIcon());
                // itemUI.attrname.text.SetTips(attrAddition.GetNameTips());
                itemUI.attrval.text.SetText(attrAddition.GetValueStr());
                LayoutRebuilder.ForceRebuildLayoutImmediate(itemUI.node.rectTransform);
            }

            var resonanceLv = Global.gApp.gSystemMgr.gAssistMgr.GetResonanceLevel();
            m_ResonanceLV.text.SetTips(66305, resonanceLv);
        }

        private void OnTipBtnClick()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.AssistSkillPreviewUI);
        }

        public void SetModelShow(bool isSlot)
        {
        }

        //一键上阵
        private void OnEquipAuto()
        {
        }
    }
}