using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class WelfareUI
    {
        private List<DailyAwardItem> m_DailyAwardList = new();
        private List<DailyAwardCumulativeItem> m_TotalAwardList = new();

        private List<WelfareUI_Award_Item> m_DailyAwardItems = new();
        private List<WelfareUI_item> m_TotalItems = new();

        private bool m_IsShowAnim = false;
        private bool m_NeedMoveScrollView = false;
        private float m_MoveScrollViewDelay = 0f;
        private float m_DistancePosY = 0f;

        private void RefreshDailyAwardUI()
        {
            InitDailyAwardCfgList();
            RefreshDaily();
            RefreshTotal();
        }

        public override void OnEndOfFrameCall()
        {
            base.OnEndOfFrameCall();
        }

        private void RefreshDaily()
        {
            m_Award_Item.CacheInstanceList();
            m_DailyAwardItems.Clear();

            float itemHeight = 0f;
            for (int i = 0; i < m_DailyAwardList.Count; i++)
            {
                DailyAwardItem itemData = m_DailyAwardList[i];
                WelfareUI_Award_Item itemUI = m_Award_Item.GetInstance(true);
                itemUI.RefreshUI(itemData, ItemClick, ItemVipClick, i, !m_IsShowAnim);
                m_DailyAwardItems.Add(itemUI);
                if (itemHeight == 0f)
                {
                    itemHeight = itemUI.AwardBG_node.rectTransform.sizeDelta.y;
                }
            }

            m_IsShowAnim = true;

            int curDay = Global.gApp.gSystemMgr.gDailyAwardMgr.Data.GetDay();
            int rowCount = Mathf.CeilToInt(curDay / 5f);

            float height = rowCount * itemHeight;
            int allRow = Mathf.CeilToInt(m_DailyAwardList.Count / 5f);
            float maxHeight = allRow * itemHeight;
            float contentHeight = m_Scroll_View.rectTransform.rect.height;
            if (height > contentHeight)
            {
                float canMoveOffset = maxHeight - contentHeight;
                m_DistancePosY = canMoveOffset;

                int maxSeeRow = Mathf.FloorToInt(contentHeight / itemHeight);
                m_MoveScrollViewDelay = Global.gApp.gSystemMgr.gDailyAwardMgr.ItemShowFirstTime +
                                        (maxSeeRow * 5 * Global.gApp.gSystemMgr.gDailyAwardMgr.itemShowOtherTime);
                m_NeedMoveScrollView = true;
            }
        }

        private void Update()
        {
            if (m_NeedMoveScrollView == false)
                return;
            m_MoveScrollViewDelay -= Time.deltaTime;
            if (m_MoveScrollViewDelay < 0)
            {
                m_NeedMoveScrollView = false;
                Vector2 currentPosition = m_Award_Content.rectTransform.anchoredPosition;
                currentPosition.y = m_DistancePosY;
                m_Award_Content.rectTransform.anchoredPosition = currentPosition;
            }
        }

        private void RefreshTotal()
        {
            m_item.CacheInstanceList();
            m_TotalItems.Clear();
            int signDay = Global.gApp.gSystemMgr.gDailyAwardMgr.Data.GetSignInCount();
            int allDay = m_DailyAwardList.Count;


            int beginIndex = 0;
            int beginDate = 0;
            int endIndex = 0;
            int endDate = 0;

            for (int i = 0; i < m_TotalAwardList.Count; i++)
            {
                DailyAwardCumulativeItem itemData = m_TotalAwardList[i];
                WelfareUI_item itemUI = m_item.GetInstance(true);
                itemUI.RefreshUI(itemData, i);

                if (signDay >= itemData.date)
                {
                    beginIndex = i + 1;
                    beginDate = itemData.date;
                }

                if (endIndex == 0)
                {
                    if (signDay < itemData.date)
                    {
                        endIndex = i + 1;
                        endDate = itemData.date;
                    }
                }
            }

            if (beginIndex > 0 && endIndex == 0)
            {
                endIndex = m_TotalAwardList.Count + 1;
                endDate = m_DailyAwardList.Count;
            }

            if (signDay >= endDate)
            {
                m_PassRewardBar.image.fillAmount = 1;
            }
            else
            {
                float beginValue = TotalProgress(beginIndex);
                float endValue = TotalProgress(endIndex);
                float progressDate = ((signDay - beginDate) / (float)(endDate - beginDate)) * (endValue - beginValue);

                m_PassRewardBar.image.fillAmount = beginValue + progressDate;
            }

            m_ProgressDate_Num.text.text = signDay.ToString(); //$"{signDay}/{m_DailyAwardList.Count}";
        }


        private float TotalProgress(int index)
        {
            switch (index)
            {
                case 0:
                    return 0f;
                case 1:
                    return 0.07f;
                case 2:
                    return 0.3f;
                case 3:
                    return 0.534f;
                case 4:
                    return 0.766f;
                case 5:
                    return 1f;
                default:
                    return 1f;
            }
        }

        //item点击事件
        private void ItemClick(WelfareUI_Award_Item item)
        {
            Global.Log($"item click:{item.GetItemDailyAwardInfo().date}");

            int curDay = Global.gApp.gSystemMgr.gDailyAwardMgr.Data.GetDay();
            if (curDay == item.GetItemDailyAwardInfo().date)
            {
                //当前天签到
                Global.gApp.gSystemMgr.gDailyAwardMgr.SendSignInRequest();
            }
            else
            {
                //不是当前天，补签
                Global.gApp.gSystemMgr.gDailyAwardMgr.SendSignSupplementRequest();
            }
        }

        private void ItemVipClick(WelfareUI_Award_Item item)
        {
            // Global.Log($"item vip click:{item.GetItemDailyAwardInfo().date}");
            Global.gApp.gToastMgr.ShowGameTips(95107);
        }

        //组织表数据
        private void InitDailyAwardCfgList()
        {
            if (m_DailyAwardList.Count > 0) return;

            m_DailyAwardList = Global.gApp.gSystemMgr.gDailyAwardMgr.GetDailyAwardList();
            m_TotalAwardList = Global.gApp.gSystemMgr.gDailyAwardMgr.GetTotalDailyAwardList();
        }
    }
}