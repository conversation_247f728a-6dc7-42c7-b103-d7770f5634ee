using System;
using System.Collections.Generic;
using LD.Protocol;

namespace LD
{
    public partial class ChallengeTaskUI_Date
    {
        private int m_CurIndex;
        private Action<int> m_ClickAction;

        public void RefreshDate(int index, Action<int> action)
        {
            m_CurIndex = index;
            m_ClickAction = action;

            m_Current_BG.AddListener(OnDateClick);
            m_Before_BG.AddListener(OnDateClick);
            m_Future_BG.AddListener(OnUnlockClick);
            m_Current_Num.text.SetText(index + 1);
            m_Future_Num.text.SetText(index + 1);
            m_Before_Num.text.SetText(index + 1);


            long beginTime = Global.gApp.gSystemMgr.gChallengeMgr.Data.BeginTime;
            long dateOpenTime = beginTime + index * 24 * 60 * 60 * 1000;
            long curTime = DateTimeUtil.GetServerTime();
            bool isFuture = curTime < dateOpenTime;
            m_Future.gameObject.SetActive(isFuture);

            bool isRed = false;
            List<LDChallengeTaskInfo> allInfos = Global.gApp.gSystemMgr.gChallengeMgr.Data.GetAllTask();
            foreach (LDChallengeTaskInfo taskInfo in allInfos)
            {
                var cfg = ChallengeMisAttr.Data.Get(taskInfo.Id);
                if (cfg != null)
                {
                    if (!Global.gApp.gSystemMgr.gFilterMgr.Filter(cfg.moduleOpen))
                    {
                        continue;
                    }

                    if (cfg.day == m_CurIndex + 1 && taskInfo.Status == ChallengeTaskStatus.Finish.GetHashCode())
                    {
                        isRed = true;
                        break;
                    }
                }
            }

            m_RedTips.SetActive(isRed && !isFuture);
        }

        private void OnDateClick()
        {
            m_ClickAction.Invoke(m_CurIndex);
        }

        private void OnUnlockClick()
        {
            int itemDay = m_CurIndex + 1;
            long beginTime = Global.gApp.gSystemMgr.gChallengeMgr.Data.BeginTime;
            long curTime = DateTimeUtil.GetServerTime();
            for (int i = 0; i < 7; i++)
            {
                long dateOpenTime = beginTime + i * 24 * 60 * 60 * 1000;
                long dateFinish = beginTime + (i + 1) * 24 * 60 * 60 * 1000;
                if (curTime > dateOpenTime && curTime <= dateFinish)
                {
                    int curShowDay = i + 1;
                    int unlockDay = itemDay - curShowDay;
                    Global.gApp.gToastMgr.ShowGameTips(96132, unlockDay.ToString());
                    break;
                }
            }
        }

        public void SetSelectState(int isSelect)
        {
            m_Current.gameObject.SetActive(isSelect == m_CurIndex);
            m_Before.gameObject.SetActive(isSelect != m_CurIndex);
        }
    }
}