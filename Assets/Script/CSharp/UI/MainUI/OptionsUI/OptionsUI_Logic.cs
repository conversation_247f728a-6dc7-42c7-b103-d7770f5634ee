using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class OptionsUI
    {
        private long m_ExpireTime = 0;
        protected override void OnInitImp()
        {
            m_btn_close.AddListener(TouchClose);
            m_GUID_Copy_Btn.AddListener(OnCoypGUID);

            m_Name_Edit_Btn.AddListener(OnOpenChangeName);
            m_ChangeNameMask_BG.AddListener(OnCloseChangeName);
            m_Change_Btn.AddListener(OnTryChangeName);
            m_FreeChange_Btn.AddListener(OnTryChangeName);
            m_Orange_btn_BindID.AddListener(OnBindAccount);
            m_Change_Name_MessageBox.gameObject.SetActive(false);
            m_SNS_MessageBox.gameObject.SetActive(false);
            m_Name_Edit_Btn.gameObject.SetActive(Global.gApp.gSystemMgr.CheckModuleOpen(LDSystemEnum.Rename));

            m_Button_CDkey.gameObject.SetActive(!RuntimeSettings.IsAudit);
            m_Orange_btn_CDkey.AddListener(OnCDKey);
            m_Orange_btn_Notice.AddListener(OnNotice);
            m_Orange_btn_GameCenter.AddListener(OnGameCenter);
            m_Orange_btn_ChangeID.AddListener(OnChangeUser);
            m_Orange_btn_ChooseServer.AddListener(OnShowServerList);
            m_Orange_btn_TestNotice.AddListener(OnTestNotice);
            m_Orange_btn_TestAD.AddListener(OnTestAD);
            m_Orange_btn_Service.AddListener(OnShowSDKServer);
            m_Orange_btn_Privacy.AddListener(OnShowPrivacy);
            m_Orange_btn_SNS.AddListener(OnOpenSNS);
            m_MessageBoxMask_BG.AddListener(OnCloseSNS);
            m_btn_close_sns.AddListener(OnCloseSNS);
            m_Btn_Discord.AddListener(OnDiscord);
            m_Btn_Twitter.AddListener(OnTwitter);
            m_Btn_Facebook.AddListener(OnFaceBook);
            m_Orange_btn_ContactGM.AddListener(OnGM);
            m_Orange_btn_Survey.AddListener(OnSurvey);
            m_Orange_btn_Lang.AddListener(OnChangeLanguaue);
            m_Button_TestNotice.gameObject.SetActive(!RuntimeSettings.Release);
            m_Button_TestAD.gameObject.SetActive(!RuntimeSettings.Release);
            m_code.text.text = "Code " + RuntimeSettings.g + Global.gApp.gSdkMgr.gOneMTMgr.GetChannel();
            Debug.Log("Code" + RuntimeSettings.country);
            if(string.IsNullOrEmpty(LDZoneCfg.GetTweerURL()))
            {
                m_Twitter.gameObject.SetActive(false);
            }
            else
            {
                m_Twitter.gameObject.SetActive(true);
            }
            if(string.IsNullOrEmpty(LDZoneCfg.GetFaceBookURL()))
            {
                m_Facebook.gameObject.SetActive(false);
            }
            else
            {
                m_Facebook.gameObject.SetActive(true);
            }
            if(string.IsNullOrEmpty(LDZoneCfg.GetDiscordURL()))
            {
                m_DiscordNode.gameObject.SetActive(false);
            }
            else
            {
                m_DiscordNode.gameObject.SetActive(true);
                if (RuntimeSettings.country == "KR")
                {
                    m_Discord.gameObject.SetActive(false);
                    m_Lounge.gameObject.SetActive(true);
                }
                else
                {
                    m_Discord.gameObject.SetActive(true);
                    m_Lounge.gameObject.SetActive(false);
                }
            }

            SetSettingTab();
            SetAvatarPreInfo();

            OnFreshUI();
        }
        public override void OnFreshUI()
        {
            SetSettingUI();
            Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_Setting_Avatar);
        }

        private void SetSettingUI()
        {
            m_SetUp.gameObject.SetActive(true);
            m_Avatar.gameObject.SetActive(false);
            m_ConfigSystem.gameObject.SetActive(false);

            m_Name_Txt.text.text = Global.gApp.gSystemMgr.gRoleMgr.GetRoleName();
            m_GUID_Txt.text.text = Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId().ToString();

            bool freeChangeName = Global.gApp.gSystemMgr.gSettingMgr.Data.RenameTimes ==0;
            m_ChangeName_Free.gameObject.SetActive(freeChangeName);
            m_FreeChange_Btn.gameObject.SetActive(freeChangeName);

            m_ChangeName_CD.gameObject.SetActive(!freeChangeName);
            m_Change_Btn.gameObject.SetActive(!freeChangeName);


            int expireOur = GlobalCfg.Data.Get(LDGlobalConfigId.ChangeNameCD).valueInt;
            m_ExpireTime = Global.gApp.gSystemMgr.gSettingMgr.Data.LastRenameTime + expireOur * DateTimeUtil.Hour_Mills;

            int needDiamondCount = GlobalCfg.Data.Get(LDGlobalConfigId.ChangeNamePrice).valueInt;
            long curCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(LDSpecialItemId.Diamond);
            m_Change_Btn_Txt.text.SetCostText(curCount,needDiamondCount);

            LDUIPrefabTools.InitSelfRoleHead(m_RoleHead.gameObject);


            if (Global.gApp.gSystemMgr.gSettingMgr.BindAccountCanOpen() &&
                !Global.gApp.gSdkMgr.gAccountMgr.getAccountBinded())
            {
                m_Button_BindID.gameObject.SetActive(true);
            }
            else
            {
                m_Button_BindID.gameObject.SetActive(false);
            }
            m_Button_Survey.gameObject.SetActive(Global.gApp.gSdkMgr.gQuerySurveyMgr.CanShowQuery());
            // OnFreshTaggleBtnState();
            FreshSNSDiamondState();
            FreshTweerDiamondState();
            FreshFaceBookDiamondState();
            Update();
        }

        private void Update()
        {
            UpdateAvatar();

            if (Global.gApp.gSystemMgr.gSettingMgr.Data.RenameTimes > 0)
            {
                long longLeftTimeS = DateTimeUtil.GetExpriedLeftTime(m_ExpireTime) / 1000;
                if (longLeftTimeS > 0)
                {
                    m_Change_Btn.image.SetGray(true);
                    m_ChangeName_CD.gameObject.SetActive(true);
                    m_ChangeName_CD.text.SetTips(69869, EZMath.FormateTimeHHMMSS(longLeftTimeS));
                }
                else
                {
                    m_Change_Btn.image.SetGray(false);
                    m_ChangeName_CD.gameObject.SetActive(false);
                }
            }
        }
        //
        // private void OnFreshTaggleBtnState()
        // {
        //     int musicState = Global.gApp.gAudioSource.BGMVolum;
        //     int soundState = Global.gApp.gAudioSource.AudioVolum;
        //     m_Music_ON.gameObject.SetActive(musicState == 1);
        //     m_Music_OFF.gameObject.SetActive(musicState == 0);
        //     m_Sound_ON.gameObject.SetActive(soundState == 1);
        //     m_Sound_OFF.gameObject.SetActive(soundState == 0);
        // }

        private void OnCoypGUID()
        {
            // С����õ���д
            GUIUtility.systemCopyBuffer = Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId().ToString();
            Global.gApp.gToastMgr.ShowGameTips(69877);
        }
        private void OnOpenChangeName()
        {
            m_Change_Name_MessageBox.gameObject.SetActive(true);
        }
        private void OnCloseChangeName()
        {
            m_Change_Name_MessageBox.gameObject.SetActive(false);
        }

        private void OnOpenSNS()
        {
            m_SNS_MessageBox.gameObject.SetActive(true);
        }
        private void OnDiscord()
        {
            bool receiveReward = Global.gApp.gSystemMgr.gSettingMgr.Data.DiscordReceived;
            if(!receiveReward)
            {
                LDSDKEvent.SendDiscordEvent();

            }
            Global.gApp.gSystemMgr.gSettingMgr.TrySendOpenDiscordView();
            Global.gApp.gTimerMgr.AddTimer(0.2f, 1, (float a, bool b) =>
            {
                Global.gApp.gSdkMgr.gOneMTMgr.openLinkInBrowser(LDZoneCfg.GetDiscordURL());
            });

        }
        private void OnTwitter()
        {
            bool receiveReward = Global.gApp.gSystemMgr.gSettingMgr.Data.TwitterReceived;
            if(!receiveReward)
            {
                LDSDKEvent.SendTwitterEvent();

            }
            Global.gApp.gSystemMgr.gSettingMgr.TrySendOpenTwitterView();
            Global.gApp.gTimerMgr.AddTimer(0.2f, 1, (float a, bool b) =>
            {
                Global.gApp.gSdkMgr.gOneMTMgr.openLinkInBrowser(LDZoneCfg.GetTweerURL());
            });

        }
        private void OnFaceBook()
        {
            bool receiveReward = Global.gApp.gSystemMgr.gSettingMgr.Data.FaceBookReceived;
            if(!receiveReward)
            {
                LDSDKEvent.SendFaceBookEvent();
            }
            Global.gApp.gSystemMgr.gSettingMgr.TrySendOpenFaceBookView();
            Global.gApp.gTimerMgr.AddTimer(0.2f, 1, (float a, bool b) =>
            {
                Global.gApp.gSdkMgr.gOneMTMgr.openLinkInBrowser(LDZoneCfg.GetFaceBookURL());
            });

        }
        private void FreshSNSDiamondState()
        {
            bool receiveReward = Global.gApp.gSystemMgr.gSettingMgr.Data.DiscordReceived;
            if (!receiveReward)
            {
                m_DiamondQiPao_Discord.gameObject.SetActive(true);
                GlobalCfgItem globalCfgItem = GlobalCfg.Data.Get(LDGlobalConfigId.SNSDiscordDiamondNum);
                m_Diamond_Num_Discord.text.text = "+" + globalCfgItem.valueInt;
            }
            else
            {
                m_DiamondQiPao_Discord.gameObject.SetActive(false);
            }
        }
        private void FreshTweerDiamondState()
        {
            bool receiveReward = Global.gApp.gSystemMgr.gSettingMgr.Data.TwitterReceived;
            if (!receiveReward)
            {
                m_DiamondQiPao_Twitter.gameObject.SetActive(true);
                GlobalCfgItem globalCfgItem = GlobalCfg.Data.Get(LDGlobalConfigId.SNSDiscordDiamondNum);
                m_Diamond_Num_Twitter.text.text = "+" + globalCfgItem.valueInt;
            }
            else
            {
                m_DiamondQiPao_Twitter.gameObject.SetActive(false);
            }
        }
        private void FreshFaceBookDiamondState()
        {
            bool receiveReward = Global.gApp.gSystemMgr.gSettingMgr.Data.FaceBookReceived;
            if (!receiveReward)
            {
                m_DiamondQiPao_Facebook.gameObject.SetActive(true);
                GlobalCfgItem globalCfgItem = GlobalCfg.Data.Get(LDGlobalConfigId.SNSDiscordDiamondNum);
                m_Diamond_Num_Facebook.text.text = "+" + globalCfgItem.valueInt;
            }
            else
            {
                m_DiamondQiPao_Facebook.gameObject.SetActive(false);
            }
        }
        private void OnCloseSNS()
        {
            m_SNS_MessageBox.gameObject.SetActive(false);
        }

        private void OnBindAccount()
        {
            Global.gApp.gSdkMgr.gAccountMgr.showBindAccountView();

        }
        private void OnTryChangeName()
        {
            string newName = m_Inputchangename.inputField.text;
            if (!string.IsNullOrEmpty(newName))
            {
                if (newName.Equals(Global.gApp.gSystemMgr.gRoleMgr.GetRoleName()))
                {
                    Global.gApp.gToastMgr.ShowGameTips(69870);
                    return;
                }
                if (Global.gApp.gSystemMgr.gSettingMgr.Data.RenameTimes == 0)
                {
                    Global.gApp.gSystemMgr.gSettingMgr.SendChangeName(newName);
                }
                else
                {
                    if(DateTimeUtil.GetExpriedLeftTime(m_ExpireTime) <= 0)
                    {
                        int needDiamond = GlobalCfg.Data.Get(LDGlobalConfigId.ChangeNamePrice).valueInt;
                        if (!UiTools.TryOpenBuyDiamondUI(needDiamond))
                        {
                            Global.gApp.gSystemMgr.gSettingMgr.SendChangeName(newName);
                        }
                    }
                }
            }
            else
            {
                Global.gApp.gToastMgr.ShowGameTips(69866);
            }
        }
        protected override void OnOtherUICloseFresh(string uiName)
        {
            OnFreshUI();
        }
        private void OnTestAD()
        {
           Global.gApp.gSdkMgr.gADMgr.ShoAdInspector();
        }
        private void OnTestNotice()
        {
           Global.gApp.gSdkMgr.gNoticeMgr.ShowMaintainNotice();
        }
        private void OnGM()
        {
            Global.gApp.gSdkMgr.gFAQMgr.showMyIssues();
        }
        private void OnChangeLanguaue()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.LangSettingUI);
        }
        private void OnSurvey()
        {
            Global.gApp.gSdkMgr.gQuerySurveyMgr.showQuestionNaireView();
        }
        private void OnNotice()
        {
            Global.gApp.gSdkMgr.gNoticeMgr.ShowNaviNotice(false);
        }
        private void OnShowPrivacy()
        {
            Global.gApp.gSdkMgr.gOneMTMgr.ShowPrivacy(true);
        }
        private void OnShowSDKServer()
        {
            Global.gApp.gSdkMgr.gOneMTMgr.ShowService(true);
        }
        private void OnGameCenter()
        {
            Global.gApp.gSdkMgr.gAccountMgr.showUserCenter();
        }
        private void OnChangeUser()
        {
            Global.gApp.gSdkMgr.gAccountMgr.showSwitchAccountView();
        }
        private void OnShowServerList()
        {
            Global.gApp.gSystemMgr.gLoginAccountMgr.TryShowServerList();

        }
        private void OnCDKey()
        {
            // TODO: dqx sdk cdkey
            Global.gApp.gSdkMgr.gNoticeMgr.ShowCDKey();
        }
        protected override void OnCloseImp()
        {
            Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_Setting_Avatar);
            Global.gApp.gSystemMgr.gSettingMgr.TrySendGetBindReward();
        }
    }
}