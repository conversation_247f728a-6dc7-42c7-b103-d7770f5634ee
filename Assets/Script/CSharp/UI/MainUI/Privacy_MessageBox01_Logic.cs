namespace LD
{
    public partial class Privacy_MessageBox01
    {
        protected override void OnInitImp()
        {
            m_Orange_btn_Confirm.AddListener(OnConfirm);
            m_Green_btn_Cancel.AddListener(OnCancel);
        }
        private void OnCancel()
        {
            Global.gApp.QuitGame();
        }
        private void OnConfirm()
        {
            TouchClose();
            Global.gApp.gUiMgr.CloseUI(LDUICfg.Privacy_MessageBox02);
            Global.gApp.gSdkMgr.SetAgreePrivacyPolicy();
            LoginFrameCtrl loginFrameCtrl = Global.gApp.gGameCtrl.GetCurFrame() as LoginFrameCtrl;
            if (loginFrameCtrl != null)
            {
                //loginFrameCtrl.StartLogin();
            }
        }
        public override void OnFreshUI()
        {
        }
        protected override void OnCloseImp()
        {
        }

    }
}