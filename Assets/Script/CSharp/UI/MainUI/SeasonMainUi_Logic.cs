using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class SeasonMainUi
    {
        private LDNetSeasonMgr m_SeasonMgr;
        private NMainUi m_ParentUI;
        private int m_MissionId;
        private int m_RePlayMissionId;
        private SeasonMissionItem m_MissionCfg;
        private int m_SeasonId;
        private bool m_IsOpenState;
        private SeasonItem m_SeasonCfg;
        private List<Transform> m_ZombiePoints = new List<Transform>();

        private bool m_IsPeakMatch = false;

        public void InitUI(NMainUi parentUI)
        {
            ChangeLanguage();
            m_SeasonMgr = Global.gApp.gSystemMgr.gSeasonMgr;
            m_ParentUI = parentUI;


            m_PassStartNormalBtn.button.AddListener(OnBattleStart);
            m_PassStartBossBtn.button.AddListener(OnBattleStart);
            m_CloseBtn.button.AddListener(OnPassMax);

            m_Act.button.AddListener(OnAct);
            m_SeasonBuff.button.AddListener(OnBuff);
            m_Map_DF_Btn.AddListener(OnPeakMatchMap2Click);

            m_ParentUI.AddTimer(1f, -1, OnPerSceondUpdate);
            m_Map_Btn.AddListener(OnPeakMatchPreviewClick);

            InitZombiePoints();
        }

        private void InitZombiePoints()
        {
            m_ZombiePoints.Add(m_jiangshi01.rectTransform);
            m_ZombiePoints.Add(m_jiangshi02.rectTransform);
            m_ZombiePoints.Add(m_jiangshi03.rectTransform);
            m_ZombiePoints.Add(m_jiangshi04.rectTransform);
            m_ZombiePoints.Add(m_jiangshi05.rectTransform);
            m_ZombiePoints.Add(m_jiangshi06.rectTransform);
            m_ZombiePoints.Add(m_jiangshi07.rectTransform);
            m_ZombiePoints.Add(m_jiangshi08.rectTransform);
            m_ZombiePoints.Add(m_jiangshi09.rectTransform);
            m_ZombiePoints.Add(m_jiangshi10.rectTransform);
            m_ZombiePoints.Add(m_jiangshi11.rectTransform);
            m_ZombiePoints.Add(m_jiangshi12.rectTransform);
        }

        private bool TryGuide()
        {
            // TODO: wzl 是否可以开启幸存者功能的引导  
            return false;
        }

        public void RefreshUI()
        {
            m_IsPeakMatch = Global.gApp.gSystemMgr.gSeasonMgr.IsOpenPeakMatch();
            RefreshCloseState();

            if (m_SeasonMgr.CheckSeasonDialogTip()) // 主线切换到赛季的剧情对话
            {
                return;
            }

            int shopCount = m_SeasonMgr.GetShopSeasonList().Count;
            if (!m_SeasonMgr.IsUnlock())
            {
                m_SeasonBuff.gameObject.SetActive(false);
                m_Act.gameObject.SetActive(shopCount > 0);
                return;
            }

            m_SeasonBuff.gameObject.SetActive(true);
            m_Act.gameObject.SetActive(shopCount > 0);

            if (TryGuide()) // 无对话  检测引导
            {
                return;
            }


            // ----------------------- 以下是常规赛季逻辑 -----------------------

            bool isPeakMatch = m_SeasonMgr.IsOpenPeakMatch();
            if (!isPeakMatch)
            {
                if (m_SeasonMgr.IsCurWeekPassAll())
                {
                    return;
                }
            }

            m_SeasonId = m_SeasonMgr.GetCurSeasonId();
            m_MissionId = m_SeasonMgr.GetCurSeasonMissionId();
            m_RePlayMissionId = m_SeasonMgr.GetLastSeasonMissionId();
            m_MissionCfg = SeasonMission.Data.Get(m_MissionId);
            m_SeasonMgr.IsMissionFinished(m_MissionId);
            m_SeasonCfg = Season.Data.Get(m_SeasonId);

            RefreshOpenState();
        }

        private void RefreshCloseState()
        {
            m_IsOpenState = false;
            m_PeakCompetition.gameObject.SetActive(false);
            m_SeasonNode.gameObject.SetActive(!m_IsPeakMatch);
            m_Bj.gameObject.SetActive(false);
            m_Bj_safe.gameObject.SetActive(true);
            m_OpenTitle.gameObject.SetActive(false);
            m_ClosedTitle.gameObject.SetActive(true);
            m_Open.gameObject.SetActive(false);
            m_close.gameObject.SetActive(true);
            m_CloseBtn.gameObject.SetActive(true);
            m_OpenBtnState.gameObject.SetActive(false);
            m_badge.gameObject.SetActive(false);
            m_Map_Btn.gameObject.SetActive(false);

            RefreshCloseTime();
        }

        private void RefreshPeakMatch()
        {
            m_SeasonNode.gameObject.SetActive(false);
            m_PeakCompetition.gameObject.SetActive(true);

            LoadSprite(m_bossImage.image, m_MissionCfg.peakCompetitionBoss);

            LoadSprite(m_BG.image, m_MissionCfg.peakCompetitionMainBG);
            LoadSprite(bossImage.image, m_MissionCfg.peakCompetitionBoss);
            m_bossName.text.SetTips(m_MissionCfg.peakCompetitionBossName);

            LDRankRecordDTOItem topOne = m_SeasonMgr.GetSeasonPeakMissionTopOne(m_SeasonId, m_MissionId);
            RefreshPeakPassPlayer(topOne);
        }

        private void RefreshCloseTime()
        {
            string time = m_SeasonMgr.GetSeasonNextShow();
            if (string.IsNullOrEmpty(time))
            {
                m_close.gameObject.SetActive(false);
                return;
            }

            m_close.gameObject.SetActive(true);
            m_ClosedTime.text.SetText(time);
        }

        private void RefreshOpenState()
        {
            m_IsOpenState = true;
            m_SeasonMgr.CheckSeasonOpenTip();

            Global.Log($"赛季 : m_SeasonId  = {m_SeasonId}  m_MissionId  = {m_MissionId}");

            int maxMissionCount = 0;
            int curMisionIndex = 0;
            int order = m_SeasonMgr.GetShowMaxMissionOrder(); // 最多显示到xx关
            //判断是否是 巅峰挑战
            if (Global.gApp.gSystemMgr.gSeasonMgr.IsOpenPeakMatch())
            {
                RefreshPeakMatch();
                m_SeasonBuffName.text.SetTips(67606);
                int normalCount = System.Math.Min(order, m_SeasonMgr.GetSeasonMissionCount());
                maxMissionCount = order - normalCount;
                curMisionIndex = maxMissionCount - (order - m_MissionCfg.Wellen);

                bool isFinish = Global.gApp.gSystemMgr.gSeasonMgr.IsMissionFinished(m_MissionId);
                if (!isFinish)
                {
                    curMisionIndex -= 1;
                }

                m_num.text.SetText($"{curMisionIndex}/{maxMissionCount}");
                m_OpenBtnState.gameObject.SetActive(true);
            }
            else
            {
                RefreshSeasonNormal();
                m_SeasonBuffName.text.SetTips(91191);
                maxMissionCount = System.Math.Min(order, m_SeasonMgr.GetSeasonMissionCount());
                curMisionIndex = System.Math.Min(order, m_MissionCfg.Wellen);
                m_Open.gameObject.SetActive(true);
                m_close.gameObject.SetActive(false);
                m_OpenTitle.gameObject.SetActive(true);
                m_ClosedTitle.gameObject.SetActive(false);
                m_badge.gameObject.SetActive(true);
                m_OpenBtnState.gameObject.SetActive(true);

                if (!string.IsNullOrEmpty(m_SeasonCfg.peakCompetitionEntryConditions))
                {
                    GlobalCfgItem globalCfg = GlobalCfg.Data.Get(80153);
                    int limitWave = 150;
                    if (globalCfg != null)
                    {
                        limitWave = globalCfg.valueInt;
                    }

                    if (m_MissionCfg.Wellen > limitWave)
                    {
                        string key = $"{LDLocalDataKeys.SeasonPeakMatchOpen}_{m_SeasonId}";
                        int isShowRed = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, key, 0);
                        if (isShowRed == 0)
                        {
                            Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_Season_PeakMatchOpen);
                        }

                        m_Map_Btn.gameObject.SetActive(true);
                    }
                }

                if (m_SeasonMgr.IsEndlessMode(m_MissionId))
                {
                    int minTime = m_SeasonMgr.GetMissionPassTime(m_MissionId);
                    if (minTime > 0)
                    {
                        m_OpenTime.text.SetTips(92012, DateTimeUtil.GetFormatTime2(minTime));
                    }
                    else
                    {
                        m_OpenTime.text.SetTips(92016);
                    }

                    m_OpenTitle.text.SetTips(92013);
                }
                else
                {
                    // List<int> missionList = m_SeasonMgr.GetCurSeasonMissionList();
                    m_OpenTime.text.SetTips(67559, curMisionIndex, maxMissionCount);
                    m_OpenTitle.text.SetTips(67510);
                }
            }


            RefreshBtns();
        }

        private void RefreshSeasonNormal()
        {
            m_SeasonNode.gameObject.SetActive(true);
            m_PeakCompetition.gameObject.SetActive(false);

            m_Bj.gameObject.SetActive(true);
            m_Bj_safe.gameObject.SetActive(false);

            LoadSprite(m_badge_icon.image, m_SeasonCfg.SeasonLogo);
            m_season_name.text.SetTips(m_SeasonCfg.SeasonName);

            RefreshZombiePoints();
        }

        private void RefreshBtns()
        {
            m_PassStartNormalBtn.gameObject.SetActive(false);
            m_PassStartBossBtn.gameObject.SetActive(false);
            m_BattleInfo.gameObject.SetActive(false);
            m_CloseBtn.gameObject.SetActive(false);

            bool isBoss = m_MissionCfg.type == LDMainPassType.Boss;
            m_PassStartNormalBtn.gameObject.SetActive(!isBoss);
            m_PassStartBossBtn.gameObject.SetActive(isBoss);
            m_BattleInfo.gameObject.SetActive(true);
            LDCommonItem commonItem = new LDCommonItem(m_MissionCfg.stamina_cost);
            m_PassBattleInfoCost.text.SetText(commonItem.Num);
        }

        private void RefreshZombiePoints()
        {
            foreach (Transform pointTsf in m_ZombiePoints)
            {
                pointTsf.gameObject.SetActive(false);
            }

            foreach (int point in m_MissionCfg.zombiePoint)
            {
                if (point <= m_ZombiePoints.Count)
                {
                    m_ZombiePoints[point - 1].gameObject.SetActive(true);
                }
            }
        }

        public void PlaySweepAni(List<LDCommonItem> rewards)
        {
            CommonUI commonUI = Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.CommonUI) as CommonUI;

            gameObject.transform.SetParent(m_ParentUI.SweepAniNode.gameObject.transform);
            m_ParentUI.SeasonSweepAni.gameObject.SetActive(true);
            m_Info.gameObject.SetActive(false);
            commonUI.AdapterNode.gameObject.SetActive(false);
            commonUI.menu.gameObject.SetActive(false);
            m_btnNode.gameObject.SetActive(false);

            m_ParentUI.AddTimer(3f, 1, (_, _) => { m_Bj.animator.Play("fxani_tognguan_jiangshi_1", -1, 0); });

            m_ParentUI.AddTimer(4.5f, 1, (_, _) =>
            {
                Global.gApp.gUiMgr.CloseUI(LDUICfg.PreventPerformanceUI);
                Global.gApp.gUiMgr.TryShowGetRewardUI(rewards, 67598);

                gameObject.transform.SetParent(m_ParentUI.SeasonNode.gameObject.transform);
                m_ParentUI.SeasonSweepAni.gameObject.SetActive(false);
                m_Info.gameObject.SetActive(true);
                commonUI.AdapterNode.gameObject.SetActive(true);
                commonUI.menu.gameObject.SetActive(true);
                m_btnNode.gameObject.SetActive(true);
                m_Bj.animator.Play("fxani_tognguan_jiangshi_0", -1, 0);

                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.MainUI, MainUIFunc.MainPass);
            });
        }

        public void RefreshPeakPassPlayer(LDRankRecordDTOItem topOne)
        {
            if (topOne == null)
            {
                m_emptyState.gameObject.SetActive(true);
                m_playerName.gameObject.SetActive(false);
            }
            else
            {
                m_emptyState.gameObject.SetActive(false);
                m_playerName.gameObject.SetActive(true);
                m_playerName.text.SetText(topOne.Name);
            }
        }

        private void OnBattleStart()
        {
            LDCommonItem staminaItem = new LDCommonItem(m_MissionCfg.stamina_cost);
            long needCount = staminaItem.Num;
            long curCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(staminaItem.Id);
            if (needCount > curCount)
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.BuyEnergyUI);
                return;
            }

            bool noTips = Global.gApp.gSystemMgr.gRoleMgr.GetNotTipsForCurLogin(LDSystemEnum.Season);
            long power = m_SeasonMgr.GetMissionPower(m_MissionId);
            if (!noTips && power > Global.gApp.gSystemMgr.gRoleMgr.GetPowerNum())
            {
                Global.gApp.gUiMgr.OpenUIAsync<FightTowerTipsUI>(LDUICfg.FightTowerTipsUI).SetLoadedCall(ui => { ui?.RefreshUI(power, LDSystemEnum.Season, MainPassStart,MainPassReplay); });
            }
            else if (m_SeasonMgr.IsCanSweep(m_MissionId, out int maxSweepId, out int canSweepCount))
            {
                Global.gApp.gUiMgr.OpenUIAsync<Season_skipPassUI>(LDUICfg.Season_skipPassUI).SetLoadedCall(ui => { ui?.RefreshUI(MainPassStart, m_MissionId, maxSweepId, canSweepCount); });
            }
            else
            {
                MainPassStart();
            }
        }

        private void MainPassReplay()
        {
            m_SeasonMgr.SendMissionStartReq(m_SeasonId,m_RePlayMissionId);
        }
        
        private void MainPassStart()
        {
            m_SeasonMgr.SendMissionStartReq(m_SeasonId, m_MissionId);
        }

        private void OnPassMax()
        {
            //Global.gApp.gToastMgr.ShowGameTips("");
        }

        private void OnAct()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.SeasonUI);
        }

        private void OnBuff()
        {
            if (m_SeasonMgr.IsUnlock(true))
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.SeasonBuffUI);
            }
        }

        private void OnPeakMatchMapClick()
        {
            Global.gApp.gUiMgr.OpenUIAsync<SeasonPeakMatchFinishUI>(LDUICfg.SeasonPeakMatchFinishUI).SetLoadedCall(ui => { ui?.InitData(SeasonPeakMatchFinishType.Battle); });
        }

        private void OnPeakMatchMap2Click()
        {
            Global.gApp.gUiMgr.OpenUIAsync<SeasonPeakMatchFinishUI>(LDUICfg.SeasonPeakMatchFinishUI).SetLoadedCall(ui => { ui?.InitData(SeasonPeakMatchFinishType.Battle); });
        }

        private void OnPeakMatchPreviewClick()
        {
            int seasonId = m_SeasonId;
            string key = $"{LDLocalDataKeys.SeasonPeakMatchOpen}_{seasonId}";
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, key, 1);
            Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_Season_PeakMatchOpen);

            Global.gApp.gUiMgr.OpenUIAsync<SeasonPeakMatchFinishUI>(LDUICfg.SeasonPeakMatchFinishUI).SetLoadedCall(ui => { ui?.InitData(SeasonPeakMatchFinishType.Preview); });
        }

        public void OnOtherUIClose(string name)
        {
            if (name.Equals(LDUICfg.Season_poltUI))
            {
                RefreshUI();
            }
        }

        private void OnPerSceondUpdate(float arg1, bool arg2)
        {
            if (!m_IsOpenState)
            {
                RefreshCloseTime();
            }
        }
    }
}