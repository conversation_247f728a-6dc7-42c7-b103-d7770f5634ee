using UnityEngine.UI;

namespace LD
{
    public partial class VipPrivilegeUI_PrivilegeList
    {
        public void RefreshPrivilegeItem(int vipLevel, string desc, bool isUp)
        {
            string[] lines = LDCommonTools.Split(desc);
            if (lines.Length > 0)
            {
                int curLevel = Global.gApp.gSystemMgr.gVipMgr.GetVipLevel();
                string type = lines[0];
                m_New_Dec.gameObject.SetActive(((type == "1" || type == "3") && !isUp) && curLevel <= vipLevel);
                m_fx_saoguang.gameObject.SetActive((type == "1" || type == "3") && curLevel == vipLevel);
                m_Important_Dec.gameObject.SetActive((type == "2" || type == "3"));
                m_Lock_Dec.gameObject.SetActive(type == "4" || (!isUp));
                m_Up_Dec.gameObject.SetActive(isUp && curLevel <= vipLevel);

                string privilegeDesc = Global.gApp.gSystemMgr.gVipMgr.GetPrivilegeDesc(vipLevel, desc);
                m_List_Txt.text.SetText(privilegeDesc);
                LayoutRebuilder.ForceRebuildLayoutImmediate(m_List_Txt.rectTransform);
            }
        }
    }
}