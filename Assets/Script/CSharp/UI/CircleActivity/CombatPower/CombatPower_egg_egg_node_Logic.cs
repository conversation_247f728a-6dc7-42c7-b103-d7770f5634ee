using System;
using UnityEngine;

namespace LD
{
    public partial class CombatPower_egg_egg_node
    {
        private NewCombatPowerTasksItem m_Cfg;
        private Animator m_Animator;
        private bool m_IsClickSend = false;
        private Action<int> m_ClickAction = null;
        private int m_EggIndex = -1;

        public void InitEgg(NewCombatPowerTasksItem cfg, int index, Action<int> click)
        {
            this.gameObject.SetActive(true);
            m_EggIndex = index;
            m_ClickAction = click;
            m_Animator = this.gameObject.transform.Find("root").GetComponent<Animator>();
            m_Click.AddListener(OnClickEgg);
            this.m_Cfg = cfg;
            LDCommonTools.DestoryChildren(m_item.rectTransform);
            KnapsackItem knapsack = LDUIPrefabTools.GetKnapsackItemUI(m_item.rectTransform);
            LDCommonItem item = new LDCommonItem(this.m_Cfg.eggShowItem[index]);
            LDUIPrefabTools.InitKnapsackItem(knapsack, item);
        }

        public void SetClickSend()
        {
            m_IsClickSend = true;
        }

        private void OnClickEgg()
        {
            if (m_IsClickSend)
                return;
            m_ClickAction?.Invoke(m_EggIndex);
        }

        public void SetEggItem(LDCommonItem item, int clickIndex, LDBaseUI baseUI)
        {
            LDCommonTools.DestoryChildren(m_item.rectTransform);
            KnapsackItem knapsack = LDUIPrefabTools.GetKnapsackItemUI(m_item.rectTransform);
            LDUIPrefabTools.InitKnapsackItem(knapsack, item);

            if (clickIndex == m_EggIndex)
            {
                // knapsack.ShowCanGet(true);
                m_fxdailinqu.gameObject.SetActive(true);
                m_Animator?.Play("fxani_dan_zadan", -1, 0);
            }
            else
            {
                baseUI.AddTimer(1f, 1, (a, b) => { m_Animator?.Play("fxani_dan_zadan01", -1, 0); });
            }
        }
    }
}