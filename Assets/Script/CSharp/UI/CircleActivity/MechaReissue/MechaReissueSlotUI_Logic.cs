using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    #region RunData

    public class LDMechaReissueRoundRunData : LDUIDataBase
    {
        public readonly int RoundCfgId;

        public LDMechaReissueRoundRunData(int cfgId)
        {
            RoundCfgId = cfgId;
        }
    }

    public class LDMechaReissueSlotRunData : LDUIDataBase
    {
        public readonly List<LDMechaReissueReward> RewardItems;//拉霸机带子上的奖励
        public readonly List<LDCommonItem> RewardItemsShow;//弹板展示的奖励
        public readonly int ResultType;

        public LDMechaReissueSlotRunData(List<LDMechaReissueReward> rewardItems, int resultType, List<LDCommonItem> rewardItemsShow)
        {
            RewardItems = rewardItems;
            ResultType = resultType;
            RewardItemsShow = rewardItemsShow;
        }
    }

    #endregion

    public partial class MechaReissueSlotUI
    {
        #region slot variables

        private int m_AutoPlayLeftTimes = 10;
        private readonly float m_AutoWaitTime = 1f;
        private readonly int m_AutoPlayTimes = 10;

        private readonly float m_PlayAniRate = 3;

        private SlotMachineEngine m_SlotEngine;
        private LDMechaReissueSlotRunData m_RunData;

        private List<Transform> reelParents;
        private readonly List<int> m_flyItemIdxList = new List<int>();
        private readonly List<List<ReelUIData>> m_SlotCfgInfo = new List<List<ReelUIData>>();

        #endregion

        #region activity variables

        private long m_CloseTime;
        private float m_PerSec = 1;

        private bool m_IsUseFirstCost;
        private bool m_IsPlayFireworksEff;

        private ActivityUI m_Parent;
        private ProgressBar m_progressBar;
        private MessageBar m_MessageBar;
        private KnapsackItem m_KnapsackItemExtra;
        private MechaReissueBaseItem m_MechaReissueBaseItem;

        private List<LDCircleMarqueeRecord> m_RecordsList = new List<LDCircleMarqueeRecord>();

        //头图切换
        private int adIndex = 0;            // 当前广告贴图索引
        private int currentActive = 0;      // 当前显示对象标识：0为 m_fx_jijiatu00，1为 m_fx_jijiatu01
        public float switchInterval = 3f;   // 切换间隔秒数
        private static readonly int MainTex = Shader.PropertyToID("_Main_Tex");

        #endregion

        #region 初始化 刷新

        public override void Init(string tabType, int activityId, LDBaseUI parentUI)
        {
            base.Init(tabType, activityId, parentUI);
            m_Parent = parentUI as ActivityUI;
            m_MessageBar = m_MessageBarMc.gameObject.GetComponent<MessageBar>();
            m_MechaReissueBaseItem =  Global.gApp.gSystemMgr.gCircleActivityMgr.GetMechaReissueBaseCfg();

            GenerateSlotCfgInfo();
            ResetUIInfo();

            InitSlotInfo();
            InitCostInfo();
            InitBtnInfo();
            InitProgressInfo();

            SetCloseTimeInfo();

            Global.gApp.gSystemMgr.gCircleActivityMgr.SendCircleMarqueeRecordRequest(activityId);
        }

        private void ShowCurrency()
        {
            m_Parent?.ForceShowCurrency(CurrencyTabUI.MechaReissue);
        }

        private void InitBtnInfo()
        {
            m_Help_Btn.AddListener(m_Parent.OnClickHelp);
            m_Pass_BTN.AddListener(m_Parent.OnClickBattlePass);
            m_PreviewGift.AddListener(m_Parent.OnClickGainGift);

            m_Orange_btn.AddListener(OnClickSpin);
            m_ten_toggle.toggle.AddListener(OnPressTenToggle);
            m_auto_Btn.gameObject.SetActive(false);
            m_toggle_skip.toggle.AddListener(OnSelectSkipToggle);

            SetSkipToggleInfo();
            RefreshBtnPassRedState();
            RefreshPreviewGiftBtn();
        }

        public override void OnOtherUICloseFresh()
        {
            RefreshPreviewGiftBtn();
            ReCalculateUseType();
            RefreshCostInfo();
        }

        private void RefreshPreviewGiftBtn()
        {
            bool isShowGainEntrance = Global.gApp.gSystemMgr.gActivityPreviewMgr.GetIsShowGainEntrance(ActivityId, (int)ActivityPreviewType.CircleActivity);
            m_PreviewGift.gameObject.SetActive(isShowGainEntrance);
        }

        public override void RefreshUI()
        {
            PlayAniShowPic();
            StartCoroutine(AlternateDisplay());
            ReCalculateUseType();
            RefreshCostInfo();
            ShowCurrency();
        }

        public void RefreshInfo(LDMechaReissueSlotRunData runData)
        {
            m_RunData = runData;

            ShowResult();

            if (GetSlotCount() == 1)
            {
                ReCalculateUseType();
            }
            RefreshCostInfo();
            RefreshBtnPassRedState();
            SetSkipToggleInfo();
        }

        public void RefreshInfo(LDMechaReissueRoundRunData runData)
        {
            InsertOneRecord(runData);
        }

        private void ShowResult()
        {
            if (GetIsSkipAni())
            {
                m_Parent.AddTouchMask();
                SkipAniSlot();
                SkipAniSlotResultShow();
                SkipAniSetProgress();
                DealRewardUI();
            }
            else
            {
                PlayAniSlot();
                StartCoroutine(PlayAniSlotResultShow());
            }
        }

        public void RefreshGetRoundRewardBackInfo()
        {
            SetProgressReward();
            SkipAniSetProgress();
        }

        private void InsertOneRecord(LDMechaReissueRoundRunData runData)
        {
            MechaReissueRoundItem roundItem = MechaReissueRound.Data.Get(runData.RoundCfgId);
            LDCircleMarqueeRecord msgData = new LDCircleMarqueeRecord
            {
                item = new LDCommonItem(roundItem.reward),
                playerId = Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId(),
                name = Global.gApp.gSystemMgr.gRoleMgr.GetRoleName()
            };

            m_MessageBar.AddNewMsgAtBegin(msgData);
            m_Chat.SetActive(true);
        }

        public void RefreshRecordShow()
        {
            List<LDCircleMarqueeRecord> recordsList = Global.gApp.gSystemMgr.gCircleActivityMgr.GetRecordList(ActivityId);
            if (recordsList.Count > 0)
            {
                m_MessageBar.ShowMessage(recordsList);
                m_Chat.SetActive(true);
            }
            else
            {
                m_Chat.SetActive(false);
            }
        }

        private void SetCloseTimeInfo()
        {
            if (Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(ActivityId) is LDMechaReissueActivityInfo activityInfo)
            {
                m_CloseTime = activityInfo.circleActivity.endTime;
            }
        }

        private void InitProgressInfo()
        {
            SetProgressReward();

            m_progressBar = m_ProgressBar.gameObject.GetComponent<ProgressBar>();
            SkipAniSetProgress();
        }

        private void SkipAniSetProgress()
        {
            if (Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(ActivityId) is LDMechaReissueActivityInfo activityInfo)
            {
                MechaReissueRoundItem roundCfg = Global.gApp.gSystemMgr.gCircleActivityMgr.GetMechaReissueRoundCfg();
                m_progressBar.SetProgressImmediately(activityInfo.totalPoint, roundCfg.totalPoint);
                RefreshReceiveStateExtra();
            }
        }

        private IEnumerator SetProgress()
        {
            if (Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(ActivityId) is LDMechaReissueActivityInfo activityInfo)
            {
                yield return m_progressBar.SetProgress(activityInfo.totalPoint);
            }
        }

        private void SetProgressReward()
        {
            if (Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(ActivityId) is LDMechaReissueActivityInfo activityInfo)
            {
                MechaReissueRoundItem roundCfg = Global.gApp.gSystemMgr.gCircleActivityMgr.GetMechaReissueRoundCfg();

                m_Ad_Txt.text.SetTips(roundCfg.adTxt); //广告文案根据阶段读表

                LDCommonTools.DestoryChildren(m_item.rectTransform);
                LDCommonItem commonItem = new LDCommonItem(roundCfg.reward);
                m_KnapsackItemExtra = LDUIPrefabTools.GetKnapsackItemUI(m_item.rectTransform);
                LDUIPrefabTools.InitKnapsackItem(m_KnapsackItemExtra, commonItem);

                RefreshReceiveStateExtra();
            }
        }

        private void RefreshReceiveStateExtra()
        {
            bool isCanGet = Global.gApp.gSystemMgr.gCircleActivityMgr.CheckMechaReissueProgressRewardCanGet(ActivityId);
            if (Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(ActivityId) is LDMechaReissueActivityInfo activityInfo)
            {
                if (isCanGet)
                {
                    m_KnapsackItemExtra.ShowMask(false);
                    m_KnapsackItemExtra.ShowCanGet(true);
                    m_KnapsackItemExtra.SetClickCallback(OnClickExtraReward);
                }
                else
                {
                    m_KnapsackItemExtra.ShowMask(false);
                    m_KnapsackItemExtra.ShowCanGet(false);
                    m_KnapsackItemExtra.SetClickCallback(null);
                }
            }
        }

        private void OnClickExtraReward()
        {
            MechaReissueRoundItem roundCfg = Global.gApp.gSystemMgr.gCircleActivityMgr.GetMechaReissueRoundCfg();

            Global.gApp.gSystemMgr.gCircleActivityMgr.SendReceiveProgressReward_MechaReissue(ActivityId, roundCfg.id);
        }

        #endregion

        #region 广告图切换

        private void PlayAniShowPic()
        {
            if (currentActive == 0)
            {
                m_fx_jijiatu00.gameObject.SetActive(false);
                m_fx_jijiatu00.gameObject.SetActive(true);
                m_fx_jijiatu01.gameObject.SetActive(false);
            }
            else
            {
                m_fx_jijiatu01.gameObject.SetActive(false);
                m_fx_jijiatu01.gameObject.SetActive(true);
                m_fx_jijiatu00.gameObject.SetActive(false);
            }
            // 更新第一次显示的贴图
            UpdateActiveTexture();
        }

        /// <summary>
        /// 更新当前激活对象的贴图（用于 Start 初始显示）
        /// </summary>
        private void UpdateActiveTexture()
        {
            string[] ads = m_MechaReissueBaseItem.advertisement;
            int count = ads.Length;
            if (count == 0)
            {
                return;
            }
            string path = ads[adIndex % count];
            adIndex++;
            if (currentActive == 0)
            {
                ChangeTexture(m_fx_jijiatu00.rectTransform, path);
            }
            else
            {
                ChangeTexture(m_fx_jijiatu01.rectTransform, path);
            }
        }
        private void ChangeTexture(Transform trans,  string path)
        {
            ParticleSystemRenderer particleSystemRenderer = trans.GetComponent<ParticleSystemRenderer>();
            MaterialPropertyBlock mpb = new MaterialPropertyBlock();
            particleSystemRenderer.GetPropertyBlock(mpb);
            Texture texture = LoadTexture(path);
            mpb.SetTexture(MainTex, texture);
            particleSystemRenderer.SetPropertyBlock(mpb);
        }

        private IEnumerator AlternateDisplay()
        {

            // 获取贴图路径集合
            string[] ads = m_MechaReissueBaseItem.advertisement;
            int count = ads.Length;
            if (count == 0)
            {
                Debug.LogWarning("广告贴图集合为空！");
                yield break;
            }

            while (true)
            {
                yield return new WaitForSeconds(switchInterval);

                currentActive = (currentActive == 0) ? 1 : 0; // 切换当前显示对象
                string path = ads[adIndex % count]; // 选取下一个贴图（循环取模）
                adIndex++;

                if (currentActive == 0)// 对即将显示的对象更新贴图，并设置激活状态
                {
                    m_fx_jijiatu00.gameObject.SetActive(false);
                    m_fx_jijiatu00.gameObject.SetActive(true);
                    ChangeTexture(m_fx_jijiatu00.rectTransform, path);

                    Animator animator01 = m_fx_jijiatu01.gameObject.GetComponent<Animator>();
                    animator01.Play("fxani_jijiaxiaoshi", -1, 0);
                }
                else
                {
                    Animator animator00 = m_fx_jijiatu00.gameObject.GetComponent<Animator>();
                    animator00.Play("fxani_jijiaxiaoshi", -1, 0);
                    m_fx_jijiatu01.gameObject.SetActive(false);
                    m_fx_jijiatu01.gameObject.SetActive(true);
                    ChangeTexture(m_fx_jijiatu01.rectTransform, path);
                }
            }
        }

        #endregion

        #region 拉霸机主体逻辑

        private void InitSlotInfo()
        {
            reelParents = new List<Transform>() { m_Reel1.rectTransform, m_Reel2.rectTransform, m_Reel3.rectTransform };

            m_SlotEngine ??= new SlotMachineEngine(m_SlotCfgInfo, reelParents, this);
        }

        private void ResetUIInfo()
        {
            m_Rate5.gameObject.SetActive(false);
            m_Rate20.gameObject.SetActive(false);
            m_ten_toggle.toggle.isOn = false;
            m_ImagePressed.gameObject.SetActive(false);
            m_ImageNormal.gameObject.SetActive(true);

            m_IsPlayFireworksEff = false;
        }

        private void PlayAniSlot()
        {
            m_Parent.AddTouchMask();
            m_AniSlotRoot.animator.enabled = true;
            string playAniName = "fxani_laohuji_choujiangdajiang";
            m_AniSlotRoot.animator.Play(playAniName, 0, 0);
            m_AniSlotRoot.animator.speed = GetPlayAniRate();

            StartCoroutine(WaitForAnimationComplete());
        }

        private void SkipAniSlot()
        {
            m_AniSlotRoot.animator.enabled = true;
            string playAniName = "fxani_laohuji_choujiangdajiang";
            m_AniSlotRoot.animator.Play(playAniName, 0, 1);
            m_AniSlotRoot.animator.speed = 0;
        }

        private void SetSkipToggleInfo()
        {

            bool isSelected = Global.gApp.gSystemMgr.gCircleActivityMgr.GetSkipToggleState(LDLocalDataKeys.MechaSkipState);
            m_toggle_skip.toggle.isOn = isSelected;

            bool isOpenSkip = false;
            if (Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(ActivityId) is LDMechaReissueActivityInfo activityInfo)
            {
                int openSkipNum = m_MechaReissueBaseItem.SkipAm;
                if (activityInfo.sumCount >= openSkipNum)
                {
                    isOpenSkip = true;
                }
            }
            m_toggle_skip.gameObject.SetActive(isOpenSkip);
        }

        private bool GetIsSkipAni()
        {
            return m_toggle_skip.toggle.isOn;
        }

        private void OnSelectSkipToggle(bool isOn)
        {
            Global.gApp.gSystemMgr.gCircleActivityMgr.SetSkipToggleState(LDLocalDataKeys.MechaSkipState, isOn);
        }

        private float GetPlayAniRate()
        {
            if (GetIsAuto())
            {
                return m_PlayAniRate;
            }

            return 1.0f;
        }

        private bool GetIsAuto()
        {
            return m_ten_toggle.toggle.isOn;
        }

        private void ResetAuto()
        {
            m_ten_toggle.toggle.isOn = false;
        }

        private IEnumerator WaitForAnimationComplete()
        {
            // 等待一帧确保动画已开始
            yield return new WaitForSeconds(0.05f);

            AnimatorStateInfo stateInfo = m_AniSlotRoot.animator.GetCurrentAnimatorStateInfo(0);
            float animationLength = stateInfo.length;

            yield return new WaitForSeconds(animationLength + 0.2f);

            StartCoroutine(OnAnimationComplete());
        }

        private IEnumerator OnAnimationComplete()
        {
            m_AniSlotRoot.animator.enabled = false;

            if (GetIsAuto())
            {
                yield return DealTip();
                yield return PlayFlyItem();

                StartCoroutine(DealRewardUIAuto());
            }
            else
            {
                yield return DealTip();
                yield return PlayFlyItem();
                DealRewardUI();
            }
            m_fx_yanhua.gameObject.SetActive(false);
        }

        private IEnumerator PlayFlyItem()
        {
            if (m_flyItemIdxList.Count > 0)
            {
                m_SlotEngine.PlayFlyItem(m_item.rectTransform.position, m_flyItemIdxList);
                yield return new WaitForSeconds(1);
                yield return SetProgress();
            }
            SetProgressReward();

            m_flyItemIdxList.Clear();
        }

        private IEnumerator DealTip()
        {
            if (m_IsPlayFireworksEff)
            {
                m_fx_yanhua.gameObject.SetActive(true);
            }

            float rate = GetPlayAniRate();
            if (m_RunData.ResultType == 2)
            {
                m_Rate5.gameObject.SetActive(true);
                m_Rate20.gameObject.SetActive(false);

                m_Rate5.animator.speed = rate;

                yield return new WaitForSeconds(1.2f / rate);
                m_Rate5.gameObject.SetActive(false);
                m_Rate20.gameObject.SetActive(false);
            }
            else if (m_RunData.ResultType == 3)
            {
                m_Rate5.gameObject.SetActive(false);

                if(!m_IsPlayFireworksEff)
                {
                    m_Rate20.gameObject.SetActive(true);
                    m_Rate20.animator.speed = rate;

                    yield return new WaitForSeconds(1.2f / rate);
                    m_Rate5.gameObject.SetActive(false);
                    m_Rate20.gameObject.SetActive(false);
                }
            }
            m_IsPlayFireworksEff = false;
        }

        private IEnumerator DealRewardUIAuto()
        {
            Global.gApp.gUiMgr.TryShowGetRewardUI(GetShowReward());

            yield return new WaitForSeconds(m_AutoWaitTime);
            Global.gApp.gUiMgr.CloseUI(LDUICfg.GetRewardUI);
            Global.gApp.gUiMgr.CloseUI(LDUICfg.GetNewWeaponUI);
            Global.gApp.gUiMgr.CloseUI(LDUICfg.GetNewUAVUI);
            Global.gApp.gUiMgr.CloseUI(LDUICfg.MechaGainedUI);
            Global.gApp.gUiMgr.CloseUI(LDUICfg.MechaSkinGainedUI);
            m_AutoPlayLeftTimes--;
            if (GetIsAuto())
            {
                if (m_AutoPlayLeftTimes > 0)
                {
                    DoSpin(m_AutoPlayLeftTimes);
                }
                else
                {
                    m_AutoPlayLeftTimes = m_AutoPlayTimes;
                    m_Parent.RemoveTouchMask();

                    CheckAutoState();
                }
            }
        }

        private void CheckAutoState()
        {
            bool isEnough = Global.gApp.gSystemMgr.gCircleActivityMgr.CheckMechaReissueSlotEnough(m_AutoPlayTimes, m_IsUseFirstCost);
            if (!isEnough)
            {
                ResetAuto();
            }
        }

        private void DealRewardUI()
        {
            m_Parent.RemoveTouchMask();

            Global.gApp.gUiMgr.TryShowGetRewardUI(GetShowReward());
        }

        private List<LDCommonItem> GetShowReward()
        {
            return m_RunData.RewardItemsShow;
        }

        public void InitCostInfo()
        {
            m_costNum1.text.SetText($"×1");
            m_costNum10.text.SetText($"×10");

            int count = GetSlotCount();
            m_IsUseFirstCost = Global.gApp.gSystemMgr.gCircleActivityMgr.GetIsUseFirstCost(count);
            RefreshCostInfo();
        }

        public void RefreshBtnPassRedState()
        {
            var redTip = m_RedTips_Pass.gameObject.GetComponent<RedTips>();
            redTip.FreshState(Global.gApp.gSystemMgr.gCircleActivityMgr.GetRedStateBattlePass(ActivityId));
        }

        public void RefreshCostInfo(bool isReCalcUse = false)
        {
            int count = GetSlotCount();

            if (isReCalcUse && GetIsSkipAni())
            {
                m_IsUseFirstCost = Global.gApp.gSystemMgr.gCircleActivityMgr.GetIsUseFirstCost(count);
            }
            LDCommonItem costItem = Global.gApp.gSystemMgr.gCircleActivityMgr.GetMechaReissueCost(count, m_IsUseFirstCost);

            LoadSprite(m_item_icon.image, costItem.Icon);
            m_num.text.SetText($"{UiTools.FormateMoney(costItem.CurNum)}/{costItem.Num}");
        }

        private int GetSlotCount()
        {
            return m_ten_toggle.toggle.isOn ? m_AutoPlayTimes : 1;
        }

        private List<ReelUIData> GetResultReelData()
        {
            List<ReelUIData> resultReelData = new List<ReelUIData>();
            if (m_RunData.ResultType == 1)
            {
                foreach (LDMechaReissueReward machineReward in m_RunData.RewardItems)
                {
                    ReelUIData reelData = GenReelUIData(machineReward);
                    resultReelData.Add(reelData);
                }
            }
            else if (m_RunData.ResultType == 2)
            {
                LDMechaReissueReward machineReward = m_RunData.RewardItems[0];
                ReelUIData reelData = GenReelUIData(machineReward);
                resultReelData.Add(reelData);
                resultReelData.Add(reelData);

                LDMechaReissueReward machineReward1 = m_RunData.RewardItems[1];
                ReelUIData reelData1 = GenReelUIData(machineReward1);
                resultReelData.Add(reelData1);
                // LDCommonTools.Shuffle(resultReelData);  取消打乱
            }

            else if (m_RunData.ResultType == 3)
            {
                LDMechaReissueReward machineReward = m_RunData.RewardItems[0];
                ReelUIData reelData = GenReelUIData(machineReward);

                MechaReissueSlotRewardItem rewardItem = MechaReissueSlotReward.Data.Get(machineReward.id);
                m_IsPlayFireworksEff = rewardItem.effect == 1;

                resultReelData.Add(reelData);
                resultReelData.Add(reelData);
                resultReelData.Add(reelData);
            }
            return resultReelData;
        }

        private void SkipAniSlotResultShow()
        {
            List<ReelUIData> resultReelData = GetResultReelData();

            SetRewardPointIdListData(resultReelData);

            GenerateSlotCfgInfo();
            m_SlotEngine.ShowSlotShow(m_SlotCfgInfo); //随机打乱一下
            m_SlotEngine.SetResultShow(resultReelData);
        }

        private IEnumerator PlayAniSlotResultShow()
        {
            yield return new WaitForSeconds(0.2f);

            List<ReelUIData> resultReelData = GetResultReelData();

            SetRewardPointIdListData(resultReelData);

            GenerateSlotCfgInfo();
            m_SlotEngine.ShowSlotShow(m_SlotCfgInfo); //随机打乱一下
            m_SlotEngine.SetResultShow(resultReelData);
        }

        private void SetRewardPointIdListData(List<ReelUIData> resultReelData)
        {
            m_flyItemIdxList.Clear();
            for (int idx = 0; idx < resultReelData.Count; idx++)
            {
                ReelUIData reelUIData = resultReelData[idx];
                if (reelUIData.point > 0)
                {
                    m_flyItemIdxList.Add(idx);
                }
            }
        }

        private ReelUIData GenReelUIData(LDMechaReissueReward machineReward)
        {
            int cfgId = machineReward.id;
            MechaReissueSlotRewardItem cfgItem = Global.gApp.gSystemMgr.gCircleActivityMgr.GetMechaReissueRewardCfg(cfgId);
            if (cfgItem == null)
            {
                return null;
            }

            ReelUIData reelData = new ReelUIData()
            {
                icon = cfgItem.showIcon,
                // num = machineReward.reward?.Num ?? 0,
                point = machineReward.point,
            };

            return reelData;
        }

        private void OnClickSpin()
        {
            int count = GetSlotCount();
            m_IsUseFirstCost = Global.gApp.gSystemMgr.gCircleActivityMgr.GetIsUseFirstCost(count);
            bool isEnough = Global.gApp.gSystemMgr.gCircleActivityMgr.CheckMechaReissueSlotEnough(count, m_IsUseFirstCost);
            if (isEnough)
            {
                DoSpin(count);
            }
            else
            {
                m_Parent.SelectTab(LDCircleActivitySubUIType.purchase);
            }
        }

        private void DoSpin(int count)
        {
            Global.gApp.gSystemMgr.gCircleActivityMgr.SendMechaReissueRequest(count, ActivityId, m_IsUseFirstCost, GetIsSkipAni());
        }

        private void OnPressTenToggle(bool isOn)
        {
            m_ImagePressed.gameObject.SetActive(isOn);
            m_ImageNormal.gameObject.SetActive(!isOn);

            Global.Log("OnPressTenToggle: "+isOn);

            ReCalculateUseType();
            RefreshCostInfo();
        }

        private void ReCalculateUseType()
        {
            int count = GetSlotCount();
            if (!m_IsUseFirstCost)
            {
                m_IsUseFirstCost = Global.gApp.gSystemMgr.gCircleActivityMgr.GetIsUseFirstCost(count);
            }
            bool isEnough = Global.gApp.gSystemMgr.gCircleActivityMgr.CheckMechaReissueSlotEnough(count, m_IsUseFirstCost);
            if (!isEnough)
            {
                m_IsUseFirstCost = false;
            }
        }

        /// <summary>
        /// 根据 LaBaMachineItem 数组，按 type=1、2、3 分别生成 5 个 id 的列表，
        /// 如果该类型数量不足 5 个，则用该类型最后一个 id 补足。
        /// </summary>
        private void GenerateSlotCfgInfo()
        {
            m_SlotCfgInfo.Clear();

            // 分别处理 type=1,2,3
            for (int type = 1; type <= 3; type++)
            {
                List<MechaReissueSlotRewardItem> typeItems = new List<MechaReissueSlotRewardItem>();
                foreach (var kvPaItem in Global.gApp.gSystemMgr.gCircleActivityMgr.GetMechaReissueCfg())
                {
                    var value = kvPaItem.Value;
                    if (value.type == type)
                    {
                        typeItems.Add(value);
                    }
                }

                List<int> selectedIds = new List<int>();

                if (typeItems.Count == 0)
                {
                    List<ReelUIData> selectedDataEmpty = new List<ReelUIData>();
                    m_SlotCfgInfo.Add(selectedDataEmpty);
                    continue;
                }

                if (typeItems.Count >= 5)
                {
                    LDCommonTools.Shuffle(typeItems);

                    for (int i = 0; i < 5; i++)
                    {
                        selectedIds.Add(typeItems[i].id);
                    }
                }
                else
                {
                    foreach (MechaReissueSlotRewardItem t in typeItems)
                    {
                        selectedIds.Add(t.id);
                    }
                    int lastId = typeItems[^1].id;
                    while (selectedIds.Count < 5)
                    {
                        selectedIds.Add(lastId);
                    }
                }

                LDCommonTools.Shuffle(selectedIds);

                List<ReelUIData> selectedData = new List<ReelUIData>();
                foreach (int id in selectedIds)
                {
                    MechaReissueSlotRewardItem cfgItem = Global.gApp.gSystemMgr.gCircleActivityMgr.GetMechaReissueRewardCfg(id);
                    LDCommonItem item = new LDCommonItem(cfgItem.reward);
                    ReelUIData reelData = new ReelUIData
                    {
                        icon = cfgItem.showIcon,
                        num = item.Num,
                    };
                    selectedData.Add(reelData);
                }

                m_SlotCfgInfo.Add(selectedData);
            }
        }

        #endregion

        #region 更新

        private void Update()
        {
            m_PerSec += UnityEngine.Time.deltaTime;
            if (m_PerSec >= 1f)
            {
                m_PerSec -= 1f;

                UpdateTime();
            }
        }

        private void UpdateTime()
        {
            if (DateTimeUtil.GetServerTime() - m_CloseTime > 0)
            {
                m_Parent.TouchClose();
                return;
            }

            string time = Global.gApp.gGameData.GetExpireTimeTips2(m_CloseTime);
            m_Time_Txt.text.SetText(time);
        }

        #endregion
    }
}