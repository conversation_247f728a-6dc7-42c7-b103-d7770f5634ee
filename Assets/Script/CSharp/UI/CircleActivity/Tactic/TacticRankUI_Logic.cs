using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class TacticRankUI
    {
        public const int Tab_RankReward = 1;
        public const int Tab_PlayerRank = 2;
        
        private LDNetTacticMgr m_TacticMgr;
        private LDNetActivityInfo m_ActivityInfo;
        private TacticRankUI_RankReward m_RankRewardNode;
        private TacticRankUI_PlayerRank m_PlayerRankNode;
        private Dictionary<int, Transform> m_Tabs = new Dictionary<int, Transform>();
        private Dictionary<int, GameObject> m_UINodes = new Dictionary<int, GameObject>();

        public LDRankUIData RankUIData;
        
        protected override void OnInitImp()
        {
            m_TacticMgr = Global.gApp.gSystemMgr.gTacticMgr;
            m_ActivityInfo = m_TacticMgr.Data.ActivityInfo;
            m_btn_close.AddListener(TouchClose);
            m_btn_help.AddListener(OnShowHelp);
            m_BtnHelpBg.AddListener(OnHideHelp);
            m_btn_RankRewardTab.button.AddListener(OnRankReward);
            m_btn_PlayerRankTab.button.AddListener(OnPlayerRank);
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
           
        }

        public override void OnFreshUI(LDUIDataBase data)
        {
            if (data is LDRankUIData rankUIData)
            {
                RankUIData = rankUIData;
                InitPanel();
                RefreshTab(Tab_RankReward);
                RefreshTime();
            }
        }

        private void InitPanel()
        {
            m_Tabs.Clear();
            m_Tabs.Add(Tab_RankReward, m_btn_RankRewardTab.rectTransform);
            m_Tabs.Add(Tab_PlayerRank, m_btn_PlayerRankTab.rectTransform);
            
            m_UINodes.Clear();
            
            m_RankReward.CacheInstanceList();
            m_RankRewardNode = m_RankReward.GetInstance();
            m_RankRewardNode.InitUI(this);
            m_UINodes.Add(Tab_RankReward, m_RankRewardNode.gameObject);
            
            m_PlayerRank.CacheInstanceList();
            m_PlayerRankNode = m_PlayerRank.GetInstance();
            m_PlayerRankNode.InitUI(this);
            m_UINodes.Add(Tab_PlayerRank, m_PlayerRankNode.gameObject);
        }

        public void RefreshTab(int tab)
        {
            foreach (KeyValuePair<int,Transform> tabData in m_Tabs)
            {
                tabData.Value.GetChild(0).gameObject.SetActive(tab == tabData.Key);
                tabData.Value.GetChild(1).gameObject.SetActive(tab != tabData.Key);
            }

            foreach (KeyValuePair<int,GameObject> uiNode in m_UINodes)
            {
                uiNode.Value.SetActive(uiNode.Key == tab);
            }
        }
        
        private void OnRankReward()
        {
            RefreshTab(Tab_RankReward);
        }

        private void OnPlayerRank()
        {
            RefreshTab(Tab_PlayerRank);
        }

        private void OnShowHelp()
        {
            m_helpNode.gameObject.SetActive(true);
        }

        private void OnHideHelp()
        {
            m_helpNode.gameObject.SetActive(false);
        }
        
        #region 倒计时
        private void RefreshTime()
        {
            if (m_ActivityInfo == null)
                return;
            //倒计时
            long endTime = m_ActivityInfo.EndTime;
            long curTime = DateTimeUtil.GetServerTime();
            if (curTime < endTime)
            {
                m_Time_Txt.gameObject.SetActive(true);
                int offset = (int)(endTime - curTime);
                RefreshTimeText(endTime - curTime);
                AddTimer(1, offset, TimerCall);
            }
            else
            {
                m_Time_Txt.gameObject.SetActive(false);
            }
        }

        private void TimerCall(float a, bool b)
        {
            long endTime = m_ActivityInfo.EndTime;
            long curTime = DateTimeUtil.GetServerTime();
            long mills = endTime - curTime;
            if (mills <= 0)
            {
                TouchClose();
            }

            RefreshTimeText(mills);
        }

        private void RefreshTimeText(long mills)
        {
            TimeSpan timeSpan = TimeSpan.FromMilliseconds((long)mills);
            string timeStr = "";
            if (timeSpan.Days > 0)
            {
                timeStr = UiTools.Localize(91162, timeSpan.Days, timeSpan.ToString(@"hh\:mm\:ss"));
            }
            else
            {
                timeStr = timeSpan.ToString(@"hh\:mm\:ss");
            }

            m_Time_Txt.text.SetText(timeStr);
        }
        
        #endregion
     
    }
}