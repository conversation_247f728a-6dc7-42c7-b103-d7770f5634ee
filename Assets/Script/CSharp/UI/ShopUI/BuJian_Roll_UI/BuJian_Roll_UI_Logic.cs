using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class BuJian_Roll_UI
    {
        private int m_ShopId;
        private List<LDNetRollItem> m_ShowRollItem;

        private List<BuJian_Roll_UI_Item> m_RollUIItems = new List<BuJian_Roll_UI_Item>();
        private int m_PlayIndex = 0;

        private Animator m_BaoXiangAnim;

        private Dictionary<int, RectTransform_Container> m_QualityEffect =
            new Dictionary<int, RectTransform_Container>();

        private Dictionary<int, GameObject> m_FlyQualityEffect = new();

        private Dictionary<int, GameObject> m_FlyEffect = new();
        private int m_WaitMsgTimerId = -1;

        public override void OnFreshUI()
        {
        }

        public override void OnFreshUI(LDUIDataBase val)
        {
            RefreshView(val as LDShowRollResultData);
        }

        protected override void OnInitImp()
        {
            m_BaoXiangAnim = m_kaiqibaoxiang.gameObject.GetComponent<Animator>();
            m_kaiqibaoxiang.gameObject.SetActive(false);

            m_Item.CacheInstanceList();

            m_QualityEffect.Add(1, m_fxbaiguang);
            m_QualityEffect.Add(2, m_fx_lvguang);
            m_QualityEffect.Add(3, m_fx_languang);
            m_QualityEffect.Add(4, m_fx_ziguang);
            m_QualityEffect.Add(5, m_fx_chengguang);

            m_FlyQualityEffect.Add(1, m_fx_choukatuoweibai.gameObject);
            m_FlyQualityEffect.Add(2, m_fx_choukatuoweilv.gameObject);
            m_FlyQualityEffect.Add(3, m_fx_choukatuoweilan.gameObject);
            m_FlyQualityEffect.Add(4, m_fx_choukatuoweizi.gameObject);
            m_FlyQualityEffect.Add(5, m_fx_choukatuoweihuang.gameObject);

            m_fx_choukatuoweibai.gameObject.SetActive(false);
            m_fx_choukatuoweilv.gameObject.SetActive(false);
            m_fx_choukatuoweilan.gameObject.SetActive(false);
            m_fx_choukatuoweizi.gameObject.SetActive(false);
            m_fx_choukatuoweihuang.gameObject.SetActive(false);

            bool isSkip = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.RollShopDiyPartSkipAnim) == 1;
            m_skipBtn.toggle.isOn = isSkip;
            m_skipBtn.toggle.onValueChanged.AddListener((arg0 =>
            {
                Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.RollShopDiyPartSkipAnim,
                    arg0 ? 1 : 0);
            }));

            HideBtns();
        }

        protected override void OnCloseImp()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.ShopDraw, 3);
            Global.gApp.gMsgDispatcher.Broadcast(MsgIds.TryCommonUIGuide);
        }

        protected override void OnOtherUICloseFresh(string uiName)
        {
            StartCoroutine(PlayShowItem(m_PlayIndex));
        }

        public void InitUI(int shopId, bool multi, bool useFirst)
        {
            Global.gApp.gSystemMgr.gRollsShopInfoMgr.SendRollsShopBuyRequest(shopId, multi, useFirst);
            RemoveTimer(m_WaitMsgTimerId);
            m_WaitMsgTimerId = AddTimer(10f, 1, (a, b) => { TouchClose(); });
        }

        public void RefreshView(LDShowRollResultData showData)
        {
            RemoveTimer(m_WaitMsgTimerId);
            m_ShopId = showData.ShopId;
            m_ShowRollItem = showData.RollItem;
            SetItemData();
            m_PlayIndex = 0;
            m_kaiqibaoxiang.gameObject.SetActive(true);
            RefreshTip();
            StartCoroutine(PlayAllEffect());
        }

        private void RefreshTip()
        {
            RollsShopItem cfgItem = RollsShop.Data.Get(m_ShopId);
            LDNetRollsShopInfo rollsShopInfo = Global.gApp.gSystemMgr.gRollsShopInfoMgr.Data.GetRollInfoByID(m_ShopId);
            int guaranteeCount = rollsShopInfo.GuaranteeCount; //保底次数

            // if (guaranteeCount < cfgItem.limitRewardNum)
            // {
            //     m_S_tips.gameObject.SetActive(true);
            //     m_Tips.gameObject.SetActive(false);
            //     m_S_tips.text.SetTips(67864, cfgItem.limitRewardNum - guaranteeCount);
            // }
            // else
            {
                m_S_tips.gameObject.SetActive(false);
                m_Tips.gameObject.SetActive(true);
                int maxCount = cfgItem.dropNum; //保底次数
                m_Tips.text.SetTips(67805, cfgItem.dropNum - (guaranteeCount % maxCount));
            }
        }

        private IEnumerator PlayAllEffect()
        {
            //先播放特效
            int maxQuality = 1;
            foreach (LDNetRollItem rollItem in m_ShowRollItem)
            {
                if (rollItem.ItemInfo.Count > 0)
                {
                    LDCommonItem item = rollItem.ItemInfo[0];
                    if (item.Type == LDCommonType.Item)
                    {
                        if (item.ItemCfg.type.Contains("piece"))
                        {
                            bool isTranslate = item.SourceId > 0;

                            if (isTranslate)
                            {
                                string sourceType = item.GetSourceItemType();
                                LDCommonItem sourceItem = new LDCommonItem(sourceType, item.SourceId, 1);
                                if (item.SourceQuality > 0)
                                {
                                    sourceItem.SetNewQuality(item.SourceQuality);
                                }

                                if (sourceItem.Quality > maxQuality)
                                {
                                    maxQuality = sourceItem.Quality;
                                }
                            }

                            continue;
                        }
                    }

                    if (item.Quality > maxQuality)
                    {
                        maxQuality = rollItem.ItemInfo[0].Quality;
                    }
                }
            }

            if (maxQuality > 5)
            {
                maxQuality = 5;
            }

            if (m_QualityEffect.TryGetValue(maxQuality, out RectTransform_Container effect))
            {
                effect.gameObject.SetActive(true);
            }

            float waitTime = 2.16f;
            if (m_skipBtn.toggle.isOn)
            {
                m_BaoXiangAnim.speed = 10f;
                waitTime = 0.2f;
            }
            else
            {
                m_BaoXiangAnim.speed = 1f;
            }

            m_BaoXiangAnim.Play("fxaim_choukakaqi");
            PlayUIAudioClip(AudioConfig.UI_CommonBox);

            yield return new WaitForSeconds(waitTime);


            StartCoroutine(PlayShowItem(m_PlayIndex));
        }

        private IEnumerator PlayShowItem(int playIndex)
        {
            for (int i = playIndex; i < m_RollUIItems.Count; i++)
            {
                m_PlayIndex = i + 1;
                BuJian_Roll_UI_Item itemUI = m_RollUIItems[i];


                int quality = itemUI.GetItemQuality();
                if (!m_FlyQualityEffect.TryGetValue(quality, out GameObject fly))
                {
                    fly = m_FlyQualityEffect[1];
                }

                GameObject obj = null;
                if (!m_skipBtn.toggle.isOn)
                {
                    obj = GameObject.Instantiate(fly.gameObject);
                    obj.transform.SetParent(m_FlyStartNode.gameObject.transform, false);
                    obj.gameObject.transform.position = m_fx_choukatuoweihuang.gameObject.transform.position;
                }

                float time = itemUI.ShowItem(obj, m_skipBtn.toggle.isOn);
                yield return new WaitForSeconds(time);
                if (itemUI.CheckShowExtern())
                {
                    yield break;
                }
            }

            yield return new WaitForSeconds(0.5f);

            bool haveTrans = false;

            foreach (BuJian_Roll_UI_Item item in m_RollUIItems)
            {
                bool trans = item.PlayTrans();
                if (!haveTrans)
                {
                    haveTrans = trans;
                }
            }

            if (haveTrans)
            {
                yield return new WaitForSeconds(0.3f);
            }

            ShowBtns();
        }

        private void HideBtns()
        {
            m_BtnNode01.gameObject.SetActive(false);
            m_BtnNode02.gameObject.SetActive(false);
            m_LeftBtn.gameObject.SetActive(false);
            m_RightBtn.gameObject.SetActive(false);
            m_close_btn.gameObject.SetActive(false);
            m_skipBtn.gameObject.SetActive(false);
            m_close_btn.button.onClick.RemoveAllListeners();
            m_LeftBtn.button.onClick.RemoveAllListeners();
            m_RightBtn.button.onClick.RemoveAllListeners();
            foreach (KeyValuePair<int, RectTransform_Container> keyValuePair in m_QualityEffect)
            {
                keyValuePair.Value.gameObject.SetActive(false);
            }
        }

        private void ShowBtns()
        {
            m_BtnNode01.gameObject.SetActive(true);
            m_BtnNode02.gameObject.SetActive(true);
            m_close_btn.AddListener(TouchClose);
            m_LeftBtn.AddListener(SingleClick);
            m_RightBtn.AddListener(MultiClick);
            m_GuideBtn.AddListener(OnGuide);
            m_LeftBtn.gameObject.SetActive(true);
            m_RightBtn.gameObject.SetActive(true);
            m_close_btn.gameObject.SetActive(true);
            m_skipBtn.gameObject.SetActive(true);
            RefreshBtnView();

            Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.ShopDraw, m_GuideBtn.rectTransform, 2);
        }

        private void SetItemData()
        {
            m_Item.CacheInstanceList();
            m_RollUIItems.Clear();
            GridLayoutGroup grid = m_Content.gameObject.GetComponent<GridLayoutGroup>();
            if (m_ShowRollItem.Count == 1)
            {
                grid.childAlignment = TextAnchor.MiddleCenter;
            }
            else
            {
                grid.childAlignment = TextAnchor.UpperLeft;
            }

            for (int i = 0; i < m_ShowRollItem.Count; i++)
            {
                LDNetRollItem item = m_ShowRollItem[i];
                BuJian_Roll_UI_Item itemUI = m_Item.GetInstance(true);
                itemUI.RefreshUI(item);
                m_RollUIItems.Add(itemUI);
            }
        }

        private void RefreshBtnView()
        {
            RollsShopItem cfgItem = RollsShop.Data.Get(m_ShopId);

            LDCommonItem singleFirst = new LDCommonItem(cfgItem.first_draw_once_consume);
            LDCommonItem singleOther = new LDCommonItem(cfgItem.draw_once_consume);

            LDCommonItem multiFirst = new LDCommonItem(cfgItem.first_multi_draw_consume);
            LDCommonItem multiOther = new LDCommonItem(cfgItem.multi_draw_consume);


            long singleFirstHaveCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(singleFirst.Id);
            // long singleOtherHaveCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(singleOther.Id);

            long multiFirstHaveCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(multiFirst.Id);
            // long multiOtherHaveCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(multiOther.Id);
            if (singleFirstHaveCount > 0)
            {
                //单抽优先消耗充足，显示优先消耗
                LoadSprite(m_Left_Icon.image, singleFirst.Icon);
                m_Left_Cost.text.text = singleFirst.Num.ToString();
            }
            else
            {
                LoadSprite(m_Left_Icon.image, singleOther.Icon);
                m_Left_Cost.text.text = singleOther.Num.ToString();
            }

            if (multiFirstHaveCount >= 10)
            {
                LoadSprite(m_Right_Icon.image, multiFirst.Icon);
                m_Right_Cost.text.text = multiFirst.Num.ToString();
            }
            else
            {
               LoadSprite(m_Right_Icon.image, multiOther.Icon);
                m_Right_Cost.text.text = multiOther.Num.ToString();
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_Left_Cost.rectTransform);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_Right_Cost.rectTransform);
        }

        private void SingleClick()
        {
            bool isSend = Global.gApp.gSystemMgr.gRollsShopInfoMgr.SendRollShopBuy(m_ShopId, false);
            if (isSend)
                HideBtns();
        }

        private void MultiClick()
        {
            bool isSend = Global.gApp.gSystemMgr.gRollsShopInfoMgr.SendRollShopBuy(m_ShopId, true);
            if (isSend)
                HideBtns();
        }

        private void OnGuide()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.ShopDraw, 2);
            Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.ShopDraw, m_close_btn.rectTransform, 3);
        }
    }
}