using System.Collections.Generic;

namespace LD
{
    public partial class UAVRollTips_list
    {
        private List<UAVRollTips_list_item01> m_ItemLists = new();

        public void RefreshView(UavRollItem cfg)
        {
            m_ItemLists.Clear();
            m_item01.CacheInstanceList();
            for (int i = 0; i < cfg.item.Length; i++)
            {
                var item = cfg.item[i];
                var itemUI = m_item01.GetInstance(true);
                itemUI.RefreshItem(item);
                m_ItemLists.Add(itemUI);
            }

            m_probability.text.SetTips(67845, $"{cfg.probability / 100f}%");
            m_s.gameObject.SetActive(cfg.rarity == 1);
            LoadSprite(m_queBG.image, cfg.BG);
            m_treasure_name.text.SetTips(cfg.qualityName);
        }
    }
}