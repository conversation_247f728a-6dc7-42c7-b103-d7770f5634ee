using System.Collections.Generic;

namespace LD
{
    public partial class UAVRollTips
    {
        private List<UAVRollTips_list> m_Lists = new();

        protected override void OnInitImp()
        {
            btn_close.AddListener(TouchClose);
            OnFreshUI();
        }

        protected override void OnCloseImp()
        {
        }

        public override void OnFreshUI()
        {
            m_Lists.Clear();
            m_list.CacheInstanceList();
            UavRollItem[] allItems = UavRoll.Data.items;
            List<UavRollItem> temp = new();
            temp.AddRange(allItems);
            temp.Sort((a, b) => { return a.order - b.order; });
            for (int i = 0; i < temp.Count; i++)
            {
                var item = temp[i];
                var itemUI = m_list.GetInstance(true);
                itemUI.RefreshView(item);
                m_Lists.Add(itemUI);
            }
        }
    }
}