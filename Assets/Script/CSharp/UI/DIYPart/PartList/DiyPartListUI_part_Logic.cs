using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class DiyPartListUI_lineNode_part
    {
        private DIYPartCfgItem m_PartCfgItem;
        private LDNetMechaPartItemDTO m_NetMechaPartItemDTO;
        private LDNetMechaPartDataMgr m_NetMechaPartDataMgr;
        private Animator m_Animator;
        private float m_DelayTime = 0;
        private DiyPartListUI m_DiyPartListUI;
        private RedTips m_RedTipsComp;

        public void Init(DiyPartListUI diyPartListUI, DIYPartCfgItem partItem, int index)
        {
            m_DiyPartListUI = diyPartListUI;
            m_rootnode.rectTransform.localScale = Vector3.zero;
            m_DelayTime = index * 0.08f;
            m_NetMechaPartDataMgr = Global.gApp.gSystemMgr.gMechaPartDataMgr;
            m_PartCfgItem = partItem;
            m_Animator = gameObject.GetComponent<Animator>();
            m_RedTipsComp = m_RedTips.gameObject.GetComponent<RedTips>();
            OnFresh();
        }

        private void Update()
        {
            if (m_Animator != null)
            {
                m_DelayTime -= Time.deltaTime;
                if (m_DelayTime < 0)
                {
                    m_rootnode.rectTransform.localScale = Vector3.one;
                    m_Animator.Play("fxaim_tubiao", -1, 0);
                    m_Animator = null;
                }
            }
        }

        public void OnFresh()
        {
            m_RedTipsComp.FreshState(Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartRedState(m_PartCfgItem.id));
            m_NetMechaPartItemDTO = m_NetMechaPartDataMgr.Data.GetNetMechaPartItemDTO(m_PartCfgItem.id);
            m_img_quaBg.AddListener(OnClick);

            int lv = Mathf.Max(m_NetMechaPartItemDTO.Lv, 1);
            int qua = m_NetMechaPartItemDTO.GetShowQua();

            LDCommonTools.DestoryChildren(m_fx.rectTransform);

            InstanceUIEffectNode(LDUIResTools.GetQualityDIYEffectPath(qua), ResSceneType.NormalRes, m_fx.rectTransform);

            int qualityCount = LDUIResTools.GetItemQualityNum(qua);

            if (qualityCount > 0)
            {
                m_Num_Quality.gameObject.SetActive(true);
                LoadSprite(m_Quality1.image, LDUIResTools.GetItemQualityItemIcon(qua));
                LoadSprite(m_Quality2.image, LDUIResTools.GetItemQualityItemIcon(qua));
                LoadSprite(m_Quality3.image, LDUIResTools.GetItemQualityItemIcon(qua));
                LoadSprite(m_Quality4.image, LDUIResTools.GetItemQualityItemIcon(qua));
                LoadSprite(m_Quality5.image, LDUIResTools.GetItemQualityItemIcon(qua));

                m_Quality1.gameObject.SetActive(qualityCount >= 1);
                m_Quality2.gameObject.SetActive(qualityCount >= 2);
                m_Quality3.gameObject.SetActive(qualityCount >= 3);
                m_Quality4.gameObject.SetActive(qualityCount >= 4);
                m_Quality5.gameObject.SetActive(qualityCount >= 5);
            }
            else
            {
                m_Num_Quality.gameObject.SetActive(false);
            }


            //m_img_quaBg.image.sprite = LoadSprite(LDUIResTools.GetItemQualityBg(qua));
            //m_txt_quaNum.text.text = "+" + qua;
            m_img_rankIcon.gameObject.SetActive(true);
            LoadSprite(m_img_rankIcon.image, LDUIResTools.GetItemRarityIconPath(m_PartCfgItem.rarity));
            LoadSprite(m_img_sortIcon.image, LDUIResTools.GetElementIconPath(m_PartCfgItem.element));
            string iconPath = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartIcon(m_PartCfgItem.id);
            LoadSprite(m_img_icon.image, iconPath);
            if (m_PartCfgItem.pos == LDDIYPartItemType.PartWeapon)
            {
                m_txt_partLvl.text.text = "Lv." + lv;
            }
            else
            {
                m_txt_partLvl.text.text = string.Empty;
            }

            m_img_equip.gameObject.SetActive(m_NetMechaPartDataMgr.DIYPartEquiped(m_PartCfgItem.id));


            m_img_lockBG.gameObject.SetActive(m_NetMechaPartItemDTO.Lv > 0);

            int nextQua = m_NetMechaPartItemDTO.GetShowQua() + 1;
            if (!m_NetMechaPartDataMgr.DIYPartMaxQua(m_PartCfgItem.id))
            {
                DIYPartQualityItem dIYPartLevelItem = DIYPartQuality.Data.Get(qua);
                long needCount = 1;
                if (m_NetMechaPartItemDTO.UnLocked())
                {
                    needCount = dIYPartLevelItem.itemCost1;
                }
                else
                {
                    needCount = dIYPartLevelItem.pieceNum;
                }

                LDCommonItem costIteocostItemm = new LDCommonItem(LDCommonType.Item, m_PartCfgItem.pieceID, needCount);

                //m_chipIcon.image.sprite = LoadSprite(costIteocostItemm.Icon);.


                m_txt_chipNum.text.text = Global.gApp.gSystemMgr.gBagMgr.GetItemCountStr(costIteocostItemm.Id) +
                                          "/" + costIteocostItemm.Num;

                long curCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(costIteocostItemm.Id);
                m_progress_full.gameObject.SetActive(curCount >= costIteocostItemm.Num);

                m_progress.image.fillAmount = 1.0f * curCount / costIteocostItemm.Num;
                m_progress.gameObject.SetActive(true);
            }
            else
            {
                m_progress_full.gameObject.SetActive(false);
                m_progress.gameObject.SetActive(false);
                m_txt_chipNum.text.SetTips(96018);
            }

            if (!m_NetMechaPartDataMgr.DIYPartUnLocked(m_PartCfgItem.id))
            {
                m_txt_partLvl.gameObject.SetActive(false);
                m_img_lockBG.gameObject.SetActive(true);
                m_img_icon.image.color = Color.gray;
                m_img_sortIcon.image.color = Color.gray;
            }
            else
            {
                m_txt_partLvl.gameObject.SetActive(true);
                m_img_icon.image.color = Color.white;
                m_img_sortIcon.image.color = Color.white;
                m_img_lockBG.gameObject.SetActive(false);
            }

            if (Global.gApp.gSystemMgr.gMechaPartDataMgr.CanComposed(m_PartCfgItem.id, false))
            {
                m_txt_unlockTips.gameObject.SetActive(true);
                m_img_lock.gameObject.SetActive(false);
                m_unLock_1.gameObject.SetActive(true);
                m_unLock_1.gameObject.GetComponent<Animator>().Play("fxaim_dailingqu01", -1, 0);
            }
            else
            {
                m_unLock_1.gameObject.SetActive(false);
                m_txt_unlockTips.gameObject.SetActive(false);
                m_img_lock.gameObject.SetActive(true);
            }
        }

        private void OnClick()
        {
            if (m_NetMechaPartItemDTO.UnLocked())
            {
                Global.gApp.gUiMgr.OpenUIAsync<PartUpgradeUI>(LDUICfg.PartUpgradeUI).SetLoadedCall(
                    partUpgradeUI => { partUpgradeUI.InitByItem(m_PartCfgItem); }
                );
            }
            else
            {
                if (Global.gApp.gSystemMgr.gMechaPartDataMgr.CanComposed(m_PartCfgItem.id, false))
                {
                    m_unLock_1.gameObject.GetComponent<Animator>().Play("fxaim_kaisuo01", -1, 0);
                    m_DiyPartListUI.AddTouchMask();
                    m_DiyPartListUI.AddTimer(0.5f, 1, DelayCall);
                }
                else
                {
                    Global.gApp.gUiMgr.OpenUIAsync<DIYPart_Tips>(LDUICfg.DIYPart_Tips).SetLoadedCall(
                        tipsUI => { tipsUI.InitData(m_PartCfgItem.id, -1, -99, -99); }
                    );
                }
            }
        }

        private void DelayCall(float a, bool b)
        {
            m_DiyPartListUI.RemoveTouchMask();
            Global.gApp.gSystemMgr.gMechaPartDataMgr.TryMechaPartCompose(m_PartCfgItem.id);
        }
    }
}