using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class PartSkinUpgradeResultUI
    {
        private LDNetMechaPartSkinDataMgr m_SkinMgr;
        private SkinStarUpResultUI m_RefultUI;
        protected override void OnInitImp()
        {
            m_SkinMgr = Global.gApp.gSystemMgr.gMechaPartSkinePart;
            m_RefultUI = m_SkinStarUpResultUI.gameObject.GetComponent<SkinStarUpResultUI>();
            m_RefultUI.Init(this);
        }

        public override void OnFreshUI()
        {
        }

        public void RefreshUI(int skinId)
        {
            int star = m_SkinMgr.GetSkinStarLv(skinId);
            SkinItem cfg = Skin.Data.Get(skinId);
            SkinStarItem starCfg = m_SkinMgr.GetSkillUpItem(skinId, star);
            m_RefultUI.SetStar(star);
            m_RefultUI.SetNameNode(skinId, 1);
            m_RefultUI.SetDesc(UiTools.Localize(starCfg.tips));
            m_RefultUI.SetShowImage(cfg.bigIcon);
            
            List<LDAttrAddition> preAttrs = m_SkinMgr.GetSkinStarLvAttrAdditions(skinId, star - 1);
            List<LDAttrAddition> curAttrs = m_SkinMgr.GetSkinStarLvAttrAdditions(skinId, star);
            m_RefultUI.SetAttrs(preAttrs, curAttrs, m_SkinMgr.IsSkinMaxLv(skinId));
        }

        protected override void OnCloseImp()
        {

        }
    }
}