using System.Collections.Generic;
using LD.Protocol;
using SuperScrollView;
using UnityEngine;
using UnityEngine.UI;
using Vector3 = UnityEngine.Vector3;

namespace LD
{
    public partial class BattlePassMain_BattlePass1
    {
        private int m_BattlePassId;
        private List<BattlePassMain_BattlePass1_entry> m_Entrys = new();
        private List<BattlePassRewardItem> m_ShowCfgItems = new();
        private KnapsackItem m_KnapsackItem1;
        private KnapsackItem m_KnapsackItem2;
        private KnapsackItem m_KnapsackItem3;
        private BattlePassRewardItem m_ShowDownCfg;
        private LoopListView2 m_LoopListView;
        private int m_ShowIndex;
        private ScrollRect m_ScrollRect;
        private bool m_LoopListInit = false;

        public void RefreshCallBack()
        {
            m_LoopListView.RefreshAllShownItem();
            RefreshAdvance();
        }

        public void RefreshView(int id)
        {
            m_ScrollRect = m_ScrollView.gameObject.GetComponent<ScrollRect>();
            m_BattlePassId = id;
            m_unlock.AddListener(OnClickUnlock);
            m_pindown_btn.AddListener(PinDownClick);
            m_pintop_btn.AddListener(PinTopClick);
            m_Downd_Item.AddListener(NextItemClick);
            m_LoopListView = m_ScrollView.gameObject.GetComponent<LoopListView2>();
            RefreshTop();
            RefreshList();
            // string showBuyMark = Global.gApp.gSystemMgr.gNetClientDataMgr.GetGeneralClientDataValue(GeneralClientDataKey.BattlePass1BuyShowMark);
            // if (!showBuyMark.Equals("1"))
            // {
            //     OnClickUnlock();
            //     Global.gApp.gSystemMgr.gNetClientDataMgr.ChangeGenerateClientData(GeneralClientDataKey.BattlePass1BuyShowMark, "1");
            // }
        }

        private void RefreshTop()
        {
            var roleMgr = Global.gApp.gSystemMgr.gRoleMgr;
            m_player_lv.text.SetText(roleMgr.GetLevel());
            long curExp = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(LDSpecialItemId.Exp);
            int maxExp = roleMgr.GetLevelMaxExp();
            if (maxExp <= 0)
            {
                m_expNUM.text.SetTips(67722);
            }
            else
            {
                m_expNUM.text.SetText($"{UiTools.FormateMoney(curExp)}/{UiTools.FormateMoney(maxExp)}");
            }

            m_RoleExp.image.fillAmount = roleMgr.GetLevelPercent();
            RefreshAdvance();
        }

        private void RefreshAdvance()
        {
            LDBattlePassInfo battlePassInfo = Global.gApp.gSystemMgr.gPaymentMgr.Data.GetBattlePassMallData(m_BattlePassId);
            bool isAdvance = battlePassInfo.Advance;
            bool isSuperAdvance = battlePassInfo.SuperAdvance;
            if (isSuperAdvance && isAdvance)
            {
                m_Orange_lable.text.SetTips(67717);
            }
            else
            {
                m_Orange_lable.text.SetTips(67716);
            }
        }

        private void RefreshList()
        {
            BattlePassRewardItem[] allItems = BattlePassReward.Data.items;
            List<BattlePassRewardItem> showItems = new List<BattlePassRewardItem>();
            for (int i = 0; i < allItems.Length; i++)
            {
                BattlePassRewardItem cfg = allItems[i];
                if (cfg.battlepass_id != m_BattlePassId)
                    continue;
                showItems.Add(cfg);
            }

            m_ShowCfgItems = showItems;
            if (!m_LoopListInit)
            {
                m_LoopListView.InitListView(m_ShowCfgItems.Count, OnGetItemByIndex);
                m_LoopListInit = true;
            }
            else
            {
                m_LoopListView.RefreshAllShownItem();
            }


            m_ShowIndex = 0;
            int lv = Global.gApp.gSystemMgr.gRoleMgr.GetLevel();
            for (int i = 0; i < showItems.Count; i++)
            {
                BattlePassRewardItem cfg = showItems[i];
                if (cfg.battlepass_id != m_BattlePassId)
                    continue;
                if (cfg.lv <= lv)
                {
                    if (i > m_ShowIndex)
                    {
                        m_ShowIndex = i;
                    }
                }
            }

            MoveToShow(m_ShowIndex);
        }

        private LoopListViewItem2 OnGetItemByIndex(LoopListView2 listView, int index)
        {
            if (index < 0 || index >= m_ShowCfgItems.Count)
            {
                return null;
            }

            LoopListViewItem2 item = listView.NewListViewDefaultItem();
            BattlePassMain_BattlePass1_entry itemScript = item.GetComponent<BattlePassMain_BattlePass1_entry>();
            itemScript.RefreshView(m_ShowCfgItems[index]);

            if (m_LoopListView.ItemList.Count > 0)
            {
                // LoopListViewItem2 last = m_LoopListView.ItemList[^1];
                //
                // int lastIndex = index;
                // if (last.ItemIndex > index)
                // {
                //     lastIndex = index + 7;
                // }
                // else
                // {
                //     lastIndex = index - 1;
                //     if (lastIndex <= 6)
                //     {
                //         lastIndex = 6;
                //     }
                // }
                //
                // if (lastIndex >= m_ShowCfgItems.Count)
                // {
                //     lastIndex = m_ShowCfgItems.Count - 1;
                // }

                int lastIndex = 0;
                foreach (LoopListViewItem2 item2 in m_LoopListView.ItemList)
                {
                    if (CheckScrollViewCanSee(item2.rectTransform()))
                    {
                        if (item2.ItemIndex > lastIndex)
                        {
                            lastIndex = item2.ItemIndex;
                        }
                    }
                }

                var nextCfg = GetDownItem(m_ShowCfgItems[lastIndex]);
                RefreshNextPointItem(nextCfg);


                if (m_ShowIndex > index && Mathf.Abs(m_ShowIndex - index) > 10)
                {
                    m_pindown_btn.gameObject.SetActive(true);
                }
                else
                {
                    m_pindown_btn.gameObject.SetActive(false);
                }

                if (index > m_ShowIndex && Mathf.Abs(m_ShowIndex - index) > 10)
                {
                    m_pintop_btn.gameObject.SetActive(true);
                }
                else
                {
                    m_pintop_btn.gameObject.SetActive(false);
                }
            }

            return item;
        }

        private bool CheckScrollViewCanSee(RectTransform target)
        {
            float y = m_Content.gameObject.transform.localPosition.y;
            float targetY = target.gameObject.transform.localPosition.y;
            if (targetY + y <= 0 && targetY + y > -1200)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        private BattlePassRewardItem GetDownItem(BattlePassRewardItem showItem)
        {
            foreach (BattlePassRewardItem cfgItem in m_ShowCfgItems)
            {
                if (cfgItem.lv > showItem.lv && cfgItem.is_import == 1)
                {
                    return cfgItem;
                }
            }

            return m_ShowCfgItems[^1];
        }

        private void PinTopClick()
        {
            MoveToShow(m_ShowIndex);
        }

        private void PinDownClick()
        {
            MoveToShow(m_ShowIndex);
        }

        private void MoveToShow(int showIndex)
        {
            int realIndex = showIndex;
            if (showIndex < 3)
            {
                realIndex = 0;
            }
            else
            {
                realIndex = showIndex - 2;
            }

            if (realIndex >= m_ShowCfgItems.Count)
            {
                realIndex = m_ShowCfgItems.Count - 1;
            }

            m_LoopListView.MovePanelToItemIndex(realIndex, 0);
        }

        private void NextItemClick()
        {
            int index = 0;
            for (int i = 0; i < m_ShowCfgItems.Count; i++)
            {
                if (m_ShowCfgItems[i].id == m_ShowDownCfg.id)
                {
                    index = i;
                    break;
                }
            }

            MoveToShow(index);
        }

        private void RefreshNextPointItem(BattlePassRewardItem cfg)
        {
            m_ShowDownCfg = cfg;
            m_LVrewards.text.SetText(cfg.lv);
            if (m_KnapsackItem1 == null)
            {
                m_KnapsackItem1 = LDUIPrefabTools.GetKnapsackItemUI(m_DownItem1.rectTransform);
            }

            if (m_KnapsackItem2 == null)
            {
                m_KnapsackItem2 = LDUIPrefabTools.GetKnapsackItemUI(m_DownItem2.rectTransform);
            }

            if (m_KnapsackItem3 == null)
            {
                m_KnapsackItem3 = LDUIPrefabTools.GetKnapsackItemUI(m_DownItem3.rectTransform);
            }

            LDCommonItem commonItem1 = new LDCommonItem(cfg.normal_reward);
            LDCommonItem commonItem2 = new LDCommonItem(cfg.advance_reward);
            LDCommonItem commonItem3 = new LDCommonItem(cfg.advance_super_reward);

            LDUIPrefabTools.InitKnapsackItem(m_KnapsackItem1, commonItem1);
            LDUIPrefabTools.InitKnapsackItem(m_KnapsackItem2, commonItem2);
            LDUIPrefabTools.InitKnapsackItem(m_KnapsackItem3, commonItem3);

            int Lv = Global.gApp.gSystemMgr.gRoleMgr.GetLevel();

            if (Lv >= cfg.lv)
            {
                //可领

                var info = Global.gApp.gSystemMgr.gPaymentMgr.Data.GetBattlePassMallData(cfg.battlepass_id);
                bool isAdvance = info.Advance;
                bool isSuperAdvance = info.SuperAdvance;
                bool isGetFree = Global.gApp.gSystemMgr.gBattlePassMgr.Data.CheckIsBuy(cfg.id, BattlePassBuyType.Free.GetHashCode());
                bool isGetAdvance = Global.gApp.gSystemMgr.gBattlePassMgr.Data.CheckIsBuy(cfg.id, BattlePassBuyType.Advance.GetHashCode());
                bool isGetSuperAdvance = Global.gApp.gSystemMgr.gBattlePassMgr.Data.CheckIsBuy(cfg.id, BattlePassBuyType.SuperAdvance.GetHashCode());
                m_KnapsackItem1.ShowLocked(false);
                m_KnapsackItem2.ShowLocked(!isAdvance);
                m_KnapsackItem3.ShowLocked(!isSuperAdvance);

                m_KnapsackItem1.ShowCanGet(!isGetFree);
                m_KnapsackItem2.ShowCanGet(isAdvance && !isGetAdvance);
                m_KnapsackItem3.ShowCanGet(isSuperAdvance && !isGetSuperAdvance);
                if (!isGetFree)
                {
                    m_KnapsackItem1.SetClickCallback(OnReceiveClick);
                }
                else
                {
                    m_KnapsackItem1.SetClickCallback(null);
                }

                if (isAdvance && !isGetAdvance)
                {
                    m_KnapsackItem2.SetClickCallback(OnReceiveClick);
                }
                else
                {
                    m_KnapsackItem2.SetClickCallback(null);
                }

                if (isSuperAdvance && !isGetSuperAdvance)
                {
                    m_KnapsackItem3.SetClickCallback(OnReceiveClick);
                }
                else
                {
                    m_KnapsackItem3.SetClickCallback(null);
                }

                m_KnapsackItem1.ShowMask(isGetFree);
                m_KnapsackItem2.ShowMask(isGetAdvance);
                m_KnapsackItem3.ShowMask(isGetSuperAdvance);
            }
            else
            {
                //不可以领取
                m_KnapsackItem1.ShowLocked(true);
                m_KnapsackItem2.ShowLocked(true);
                m_KnapsackItem3.ShowLocked(true);

                m_KnapsackItem1.SetClickCallback(null);
                m_KnapsackItem2.SetClickCallback(null);
                m_KnapsackItem3.SetClickCallback(null);
            }
        }

        private void OnReceiveClick()
        {
            Global.gApp.gSystemMgr.gBattlePassMgr.ReceiveBattlePass(m_ShowDownCfg.battlepass_id);
        }

        private void OnClickUnlock()
        {
            Global.gApp.gUiMgr.OpenUIAsync<BattlePassBuy>(LDUICfg.BattlePassBuy).SetLoadedCall(buyUI => { buyUI.RefreshView(m_BattlePassId); });
        }
    }
}