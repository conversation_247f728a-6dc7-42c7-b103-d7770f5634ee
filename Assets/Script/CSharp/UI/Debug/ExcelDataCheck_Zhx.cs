using LD;
using UnityEngine;

public partial class ExcelDataCheck
{
    static void CheckExcelData_Zhx()
    {
        CheckCampsite();
        CheckTeam();
        CheckEquipment();
        CheckBattlePass();
        CheckExpedition();
        CheckVip();
    }

    /// <summary>
    /// 检查营地相关
    /// </summary>
    static void CheckCampsite()
    {
        Debug.Log("Start check survivor 表 ==================================");

        var survivor = Survivor.Data.items;
        foreach (var item in survivor)
        {
            FileTipsByLocal(item.name);
            FilePngByLocal(item.icon);
        }

        Debug.Log("End survivor 表 ==================================");
        Debug.Log("Start check driver 表 ==================================");

        var driver = Driver.Data.items;
        foreach (var item in driver)
        {
            FileTipsByLocal(item.name);
            FilePngByLocal(item.icon);
            FilePngByLocal(item.halfIcon);
            FilePngByLocal(item.bigIcon);
            FilePrefabByLocal(item.driverAnim);
            FileTipsByLocal(item.DriverTitle);
        }

        Debug.Log("End driver 表 ==================================");
    }

    /// <summary>
    /// 组队表
    /// </summary>
    static void CheckTeam()
    {
        Debug.Log("Start check teaminstance 表 ==================================");

        var team = TeamInstance.Data.items;
        foreach (var item in team)
        {
            FileTipsByLocal(item.name);
        }

        Debug.Log("End teaminstance 表 ==================================");
    }

    /// <summary>
    /// 装备表
    /// </summary>
    static void CheckEquipment()
    {
        Debug.Log("Start check equipment slot 表 ==================================");

        var slot = Slot.Data.items;
        foreach (var item in slot)
        {
            FileTipsByLocal(item.name);
            FilePngByLocal(item.emptyStateIcon);
        }

        Debug.Log("End equipment slot 表 ==================================");
        Debug.Log("Start check equipment 表 ==================================");

        var equipment = Equipment.Data.items;
        foreach (var item in equipment)
        {
            FileTipsByLocal(item.name);
            FilePngByLocal(item.icon);
        }

        Debug.Log("End equipment 表 ==================================");
    }

    /// <summary>
    /// 战令
    /// </summary>
    static void CheckBattlePass()
    {
        Debug.Log("Start check battlepass 表 ==================================");

        var Data = BattlePass.Data.items;
        foreach (var item in Data)
        {
            FilePngByLocal(item.icon);
        }

        Debug.Log("End battlepass 表 ==================================");
    }

    /// <summary>
    /// 远征
    /// </summary>
    static void CheckExpedition()
    {
        Debug.Log("Start check expedition Event 表 ==================================");

        var Data = ExpeditionEvent.Data.items;
        foreach (var item in Data)
        {
            FileTipsByLocal(item.shijianTitle);
            FilePngByLocal(item.shijianBg);
        }

        Debug.Log("End expedition Event  表 ==================================");
        Debug.Log("Start check expedition box 表 ==================================");

        var box = ExpeditionBox.Data.items;
        foreach (var item in box)
        {
            FileTipsByLocal(item.name);
            FileTipsByLocal(item.desc);
            FilePngByLocal(item.icon);
        }

        Debug.Log("End expedition box 表 ==================================");
    }

    /// <summary>
    /// vip
    /// </summary>
    static void CheckVip()
    {
        Debug.Log("Start check vipInfo 表 ==================================");
        var vipInfo = VIPinfo.Data.items;
        foreach (var item in vipInfo)
        {
            FileTipsByLocal(item.vipName);
            FileTipsByLocal(item.saleTxt, true);
            FilePngByLocal(item.vipItemDec);
        }

        Debug.Log("End vipInfo 表 ==================================");
        Debug.Log("Start check quick recharge 表 ==================================");
        var quickRechargeItems = QuickRecharge.Data.items;
        foreach (var item in quickRechargeItems)
        {
            FileTipsByLocal(item.rechargeName);
            FileTipsByLocal(item.rechargeAds, true);
            FilePngByLocal(item.BG);
            FileTipsByLocal(item.discountTxt, true);
        }

        Debug.Log("End quick recharge 表 ==================================");
        Debug.Log("Start check privilege 表 ==================================");
        var privilege = Privilege.Data.items;
        foreach (var item in privilege)
        {
            FileTipsByLocal(item.desc);
        }

        Debug.Log("End privilege 表 ==================================");
    }
}