using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class TriggerGift_Main_Gift
    {
        private LDBaseUI m_ParentUI;
        private LDDIYPackInfo m_Info;
        private DIYPackItem m_Cfg;
        private int m_TimerId;
        private float m_ShowEffectDelay = -1;
        private float m_ShowEffectTime;

        private LDPaymentBtnUI m_PaymentBtn;

        public void RefreshView(int giftId, LDBaseUI parent)
        {
            if (m_PaymentBtn == null)
            {
                m_PaymentBtn = m_lock1.gameObject.AddComponent<LDPaymentBtnUI>();
                m_PaymentBtn.InitBtn(m_lock1.button, this, OnClick);
            }

            m_ItemBtn.AddListener(OnClickItem);
            m_ParentUI = parent;
            LDDIYPackInfo giftInfo = Global.gApp.gSystemMgr.gPaymentMgr.Data.GetDiyPackInfo(giftId);
            m_Info = giftInfo;
            DIYPackItem cfg = DIYPack.Data.Get(giftId);
            if (cfg == null)
            {
                this.gameObject.SetActive(false);
                return;
            }

            m_Cfg = cfg;
            m_Item.CacheInstanceList();
            foreach (string s in cfg.itemId_DIY)
            {
                var itemUI = m_Item.GetInstance(true);
                LDUIPrefabTools.InitKnapsackItem(itemUI.rectTransform(), s);
            }

            m_GiftName.text.SetTips(cfg.Name);
            LoadSprite(m_partImage.image, cfg.partImage);
            LoadSprite(m_backGround.image, cfg.GiftBG);
            if (giftId == 900 || giftId == 901)
            {
                m_supervalueNode.gameObject.SetActive(false);
            }
            else
            {
                m_supervalueNode.gameObject.SetActive(true);
                m_superValue.text.SetTips(cfg.superValue);
            }

            m_PaymentBtn.ShowPrice(cfg.iapId_DIY);


            var curTime = DateTimeUtil.GetServerTime();
            var dis = (m_Info.CloseTime - curTime) / 1000;
            m_ParentUI.RemoveTimer(m_TimerId);
            m_TimerId = m_ParentUI.AddTimer(1, (int)dis, OnTimerCall);
            OnTimerCall(0, false);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_time.rectTransform.parent.rectTransform());
        }

        private void OnTimerCall(float arg1, bool arg2)
        {
            if (arg2)
            {
                var ui = m_ParentUI as TriggerGift_Main;
                ui?.OnFreshUI();
            }
            else
            {
                var endTime = m_Info.CloseTime;
                string time = Global.gApp.gGameData.GetExpireTimeTips2(endTime);
                m_time.text.SetText(time);
            }
        }

        private void OnClick()
        {
            Global.gApp.gSystemMgr.gPaymentMgr.SendMallBuyItemRequest(m_Cfg.iapId_DIY);
        }

        private void OnClickItem()
        {
            Global.gApp.gUiMgr.OpenUIAsync<TriggerGift_Hint>(LDUICfg.TriggerGift_Hint).SetLoadedCall(ui =>
                ui.RefreshView(m_Info.Id)
            );
        }

        public void PlayShowEffect(float delay)
        {
            m_ShowEffectDelay = delay;
            m_ShowEffectTime = 0;
            m_root.gameObject.SetActive(false);
        }

        private void Update()
        {
            if (m_ShowEffectDelay > 0)
            {
                m_ShowEffectTime += Time.deltaTime;
                if (m_ShowEffectTime > m_ShowEffectDelay)
                {
                    m_ShowEffectDelay = -1;
                    m_root.gameObject.SetActive(true);
                    m_ani.animator.Play("fxani_huatiao02", -1, 0);
                }
            }
        }
    }
}