using System.Collections.Generic;

namespace LD
{
    public partial class EquipmentCreationUI_item_weapon
    {
        private SlotItem m_SlotCfg;

        private EquipmentItemUI m_EquipmentItemUI;

        public void RefreshSlot(SlotItem slot)
        {
            m_SlotCfg = slot;
            m_Btn.AddListener(OnEquipClick);
            var equipInfo = Global.gApp.gSystemMgr.gEquipmentMgr.GetEquipBySlotId(m_SlotCfg.id);
            m_EmptyEquipNode.gameObject.SetActive(equipInfo == null);
            m_EquipNode.gameObject.SetActive(equipInfo != null);
            if (equipInfo != null)
            {
                if (m_EquipmentItemUI == null)
                {
                    m_EquipmentItemUI = LDUIPrefabTools.GetEquipmentItemUI(this.EquipNode.rectTransform);
                    m_EquipmentItemUI.transform.SetAsFirstSibling();
                    m_EquipmentItemUI.SetClickCallback(OnEquipClick);
                }

                LDUIPrefabTools.InitEquipmentItem(m_EquipmentItemUI, equipInfo);
            }
            else
            {
                LoadSprite(m_EmptyEquip.image, m_SlotCfg.emptyStateIcon);
            }

            RefreshRed();
        }

        public void RefreshRed()
        {
            bool slotRed = Global.gApp.gSystemMgr.gEquipmentMgr.CheckSlotHaveRed(m_SlotCfg.id);
            m_RedTips.gameObject.SetActive(slotRed);
        }

        private void OnEquipClick()
        {
            Global.gApp.gUiMgr.OpenUIAsync<EquipmentWeaponUI>(LDUICfg.EquipmentWeaponUI).SetLoadedCall(ui =>
                ui.InitData(m_SlotCfg.id)
            );
        }
    }
}