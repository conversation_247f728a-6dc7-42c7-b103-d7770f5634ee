using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class MechaSkinUpgradeResultUI
    {
        private LDNetMechaSkinMgr m_SkinMgr;
        private SkinStarUpResultUI m_RefultUI;
        
        protected override void OnInitImp()
        {
            m_SkinMgr = Global.gApp.gSystemMgr.gMechaSkinDataMgr;
            m_RefultUI = m_SkinStarUpResultUI.gameObject.GetComponent<SkinStarUpResultUI>();
            m_RefultUI.Init(this);
        }

        public override void OnFreshUI()
        {
        }

        public void RefreshUI(int skinId)
        {
            int star = m_SkinMgr.GetMechaSkinStarLv(skinId);
            SkinItem cfg = Skin.Data.Get(skinId);
            SkinStarItem starCfg = m_SkinMgr.GetSkinStarLvCfgByLv(skinId, star);
            m_RefultUI.SetStar(star);
            m_RefultUI.SetNameNode(skinId, 1);
            m_RefultUI.SetDesc(UiTools.Localize(starCfg.tips));
            m_RefultUI.SetShowModel(cfg.skinPrefab);
            
            List<LDAttrAddition> preAttrs = m_SkinMgr.GetSkinStarLvAttrAdditions(skinId, star - 1);
            List<LDAttrAddition> curAttrs = m_SkinMgr.GetSkinStarLvAttrAdditions(skinId, star);
            m_RefultUI.SetAttrs(preAttrs, curAttrs, star == m_SkinMgr.GetMechaSkinStarLvMax(skinId));
        }

        protected override void OnCloseImp()
        {

        }
    }
}