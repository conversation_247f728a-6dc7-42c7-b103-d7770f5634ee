using DG.Tweening.Core.Easing;
using System.Collections.Generic;
using static LD.LDUICfg;

namespace LD
{
    public class LDUIRewardData
    {
        public List<LDRewardUIData> m_RewardDatas = new List<LDRewardUIData>();
        public List<LDSystemUnlockUIData> m_SysUnlockDatas = new List<LDSystemUnlockUIData>();
        public List<LDRoleLevelUpUIData> m_RoleLvUpDatas = new List<LDRoleLevelUpUIData>();
        public bool m_PauseShowRoleLvUp;
        public List<string> m_DailyAwardDatas = new List<string>();
        public List<string> m_StartAwardDatas = new List<string>();
        public LDMechaSpinFinishUIData m_MechaSpinFinishData = null;
        public bool m_ShowFirstReward = false;
        public int m_ShowDIYPackId = -1;
        public List<int> m_ShowCircleActivity = new List<int>();
        public List<int> m_DiyActivityShows = new();
        public bool m_ShowAppPingJia = false;
        public bool m_ShowVipUpgrade = false;
        public bool m_ShowSeasonUnlock = false;
        public bool m_ShowSeasonPeakMatchUnlock = false;
        public bool m_ShowSeasonBoxRewardUI = false;
        public bool m_ShowSeasonPeakMatchFinish = false;
        public bool m_ShowSeasonTurntableUI = false;
        public bool m_ShowSeasonDFPreUI = false;
        public int m_ShowCollectActivityId = -1;

        public bool m_ShowDoubleReward = false;
        public bool m_ShowNotEngry = false;

        public List<int> m_TriggerGiftList = new();
        public int m_TriggerGiftDelyCount = 0;
        public List<int> m_TriggerGiftListImadiate = new();
        public bool m_ShowNewEquip = false;
        public Dictionary<int, int> m_ShowBattlePass = new();
        public List<int> m_ShowTimeLimitedRecharge = new List<int>();
        public bool m_NewCombatPowerActivity = false;
    }

    public partial class LDUiMgr
    {
        private LDUIRewardData m_UIRewardData;

        #region preshowData

        private List<LDRewardUIData> m_RewardDatas
        {
            get { return m_UIRewardData.m_RewardDatas; }
            set { m_UIRewardData.m_RewardDatas = value; }
        }

        private List<LDSystemUnlockUIData> m_SysUnlockDatas
        {
            get { return m_UIRewardData.m_SysUnlockDatas; }
            set { m_UIRewardData.m_SysUnlockDatas = value; }
        }

        private List<LDRoleLevelUpUIData> m_RoleLvUpDatas
        {
            get { return m_UIRewardData.m_RoleLvUpDatas; }
            set { m_UIRewardData.m_RoleLvUpDatas = value; }
        }

        private List<string> m_DailyAwardDatas
        {
            get { return m_UIRewardData.m_DailyAwardDatas; }
            set { m_UIRewardData.m_DailyAwardDatas = value; }
        }

        private List<string> m_StartAwardDatas
        {
            get { return m_UIRewardData.m_StartAwardDatas; }
            set { m_UIRewardData.m_StartAwardDatas = value; }
        }

        private LDMechaSpinFinishUIData m_MechaSpinFinishData
        {
            get { return m_UIRewardData.m_MechaSpinFinishData; }
            set { m_UIRewardData.m_MechaSpinFinishData = value; }
        }

        private bool m_ShowFirstReward
        {
            get { return m_UIRewardData.m_ShowFirstReward; }
            set { m_UIRewardData.m_ShowFirstReward = value; }
        }

        private int m_ShowDIYPackId
        {
            get { return m_UIRewardData.m_ShowDIYPackId; }
            set { m_UIRewardData.m_ShowDIYPackId = value; }
        }

        private List<int> m_ShowCircleActivity
        {
            get { return m_UIRewardData.m_ShowCircleActivity; }
            set { m_UIRewardData.m_ShowCircleActivity = value; }
        }

        public List<int> m_DiyActivityShows
        {
            get { return m_UIRewardData.m_DiyActivityShows; }
            set { m_UIRewardData.m_DiyActivityShows = value; }
        }

        private bool m_ShowAppPingJia
        {
            get { return m_UIRewardData.m_ShowAppPingJia; }
            set { m_UIRewardData.m_ShowAppPingJia = value; }
        }

        private bool m_ShowVipUpgrade
        {
            get { return m_UIRewardData.m_ShowVipUpgrade; }
            set { m_UIRewardData.m_ShowVipUpgrade = value; }
        }

        private bool m_ShowSeasonUnlock
        {
            get { return m_UIRewardData.m_ShowSeasonUnlock; }
            set { m_UIRewardData.m_ShowSeasonUnlock = value; }
        }

        private bool m_ShowSeasonBoxRewardUI
        {
            get { return m_UIRewardData.m_ShowSeasonBoxRewardUI; }
            set { m_UIRewardData.m_ShowSeasonBoxRewardUI = value; }
        }

        private bool m_ShowSeasonTurntableUI
        {
            get { return m_UIRewardData.m_ShowSeasonTurntableUI; }
            set { m_UIRewardData.m_ShowSeasonTurntableUI = value; }
        }

        private bool m_ShowSeasonDFPreViewUI
        {
            get { return m_UIRewardData.m_ShowSeasonDFPreUI; }
            set { m_UIRewardData.m_ShowSeasonDFPreUI = value; }
        }

        private int m_ShowCollectActivityId
        {
            get { return m_UIRewardData.m_ShowCollectActivityId; }
            set { m_UIRewardData.m_ShowCollectActivityId = value; }
        }

        private bool m_ShowDoubleReward
        {
            get { return m_UIRewardData.m_ShowDoubleReward; }
            set { m_UIRewardData.m_ShowDoubleReward = value; }
        }

        private bool m_ShowNotEngry
        {
            get { return m_UIRewardData.m_ShowNotEngry; }
            set { m_UIRewardData.m_ShowNotEngry = value; }
        }

        private List<int> m_TriggerGiftList
        {
            get { return m_UIRewardData.m_TriggerGiftList; }
            set { m_UIRewardData.m_TriggerGiftList = value; }
        }

        private int m_TriggerGiftDelyCount
        {
            get { return m_UIRewardData.m_TriggerGiftDelyCount; }
            set { m_UIRewardData.m_TriggerGiftDelyCount = value; }
        }

        private List<int> m_TriggerGiftListImadiate
        {
            get { return m_UIRewardData.m_TriggerGiftListImadiate; }
            set { m_UIRewardData.m_TriggerGiftListImadiate = value; }
        }

        public bool m_ShowNewEquip
        {
            get { return m_UIRewardData.m_ShowNewEquip; }
            set { m_UIRewardData.m_ShowNewEquip = value; }
        }

        //新加的在上面的类里面加，方便统一的清理数据 !!!!

        #endregion

        public void ClearNetData()
        {
            m_UIRewardData = new LDUIRewardData();
        }

        /// <summary>
        /// 打开通用恭喜获得UI
        /// </summary>
        public void TryShowGetRewardUI(LDCommonItem item, int titleTipsId = 96001)
        {
            List<LDCommonItem> commonItems = new List<LDCommonItem> { item };
            TryShowGetRewardUI(commonItems, titleTipsId);
        }

        public void TryShowGetRewardUI(List<LDCommonItem> items, int titleTipsId = 96001)
        {
            if (items.Count != 0)
            {
                LDRewardUIData rewardUIData = GetRewardUIData(items, titleTipsId);
                m_RewardDatas.Add(rewardUIData);
            }

            TryShowPerformanceUI();
        }

        public LDRewardUIData GetRewardUIData(List<string> items, int titleTipsId = 96001)
        {
            List<LDCommonItem> commonItems = new List<LDCommonItem>();
            foreach (string item in items)
            {
                LDCommonItem commonItem = new LDCommonItem(item);
                commonItems.Add(commonItem);
            }

            return GetRewardUIData(commonItems, titleTipsId);
        }

        public LDRewardUIData GetRewardUIData(List<LDCommonItem> items, int titleTipsId = 96001)
        {
            LDRewardUIData rewardUIData = new LDRewardUIData();
            rewardUIData.Items = items;
            rewardUIData.TitleTipsId = titleTipsId;
            return rewardUIData;
        }

        /// <summary>
        /// 打开特殊展示获得UI
        /// </summary>
        public void TryShowExternRewardUI(LDCommonItem item, int titleTipsId = 96001)
        {
            List<LDCommonItem> commonItems = new List<LDCommonItem> { item };
            LDRewardUIData rewardUIData = new LDRewardUIData();
            rewardUIData.Items = commonItems;
            rewardUIData.TitleTipsId = titleTipsId;
            rewardUIData.OnlyExternReward = true;
            m_RewardDatas.Add(rewardUIData);
            TryShowPerformanceUI();
        }

        public bool TryShowExtern(LDRewardUIData preRewardData, LDCommonItem item)
        {
            if (item.Type == LDCommonType.DIYPart)
            {
                Global.gApp.gUiMgr.OpenUIAsync<GetNewWeaponUI>(LDUICfg.GetNewWeaponUI).SetLoadedCall(baseUI => { baseUI.InitData(preRewardData, item); });

                return true;
            }
            // TODO: dqx UAV 获得之后的展示界面，好像不太好判定，我自己可以打一个标签 用
            else if (item.Type == LDCommonType.UAV)
            {
                // if (Global.gApp.gSystemMgr.gLocalDataMgr.IsNewGenUAV(item.Id))
                {
                    if (item.IsSRarity)
                    {
                        Global.gApp.gUiMgr.OpenUIAsync<GetNewUAVUI>(LDUICfg.GetNewUAVUI).SetLoadedCall(baseUI => { baseUI?.InitData(preRewardData, item); });

                        return true;
                    }
                }
            }
            else if (item.Type == LDCommonType.Mecha)
            {
                Global.gApp.gUiMgr.OpenUIAsync<MechaGainedUI>(LDUICfg.MechaGainedUI).SetLoadedCall(baseUI => { baseUI?.RefreshUI(preRewardData, item); });

                return true;
            }
            else if (item.Type == LDCommonType.Mechaskin)
            {
                if (!Global.gApp.gSystemMgr.gMechaSkinDataMgr.MechaSkinIdCache.Contains(item.Id))
                {
                    Global.gApp.gSystemMgr.gMechaSkinDataMgr.MechaSkinIdCache.Add(item.Id);

                    Global.gApp.gUiMgr.OpenUIAsync<MechaSkinGainedUI>(LDUICfg.MechaSkinGainedUI).SetLoadedCall(baseUI => { baseUI?.RefreshUI(preRewardData, item); });

                    return true;
                }

                return false;
            }
            else if (item.Type == LDCommonType.Diypartskin)
            {
                if (!Global.gApp.gSystemMgr.gMechaPartSkinePart.PartSkinIdCache.Contains(item.Id))
                {
                    Global.gApp.gSystemMgr.gMechaPartSkinePart.PartSkinIdCache.Add(item.Id);

                    Global.gApp.gUiMgr.OpenUIAsync<PartSkinGainedUI>(LDUICfg.PartSkinGainedUI).SetLoadedCall(baseUI => { baseUI.RefreshUI(preRewardData, item); });

                    return true;
                }

                return false;
            }
            else if (item.Type == LDCommonType.Driver)
            {
                if (item.ShowGainDriver && Global.gApp.gSystemMgr.gCampsiteMgr.IsNewDriver(item.Id))
                {
                    Global.gApp.gUiMgr.OpenUIAsync<NewDriverUI>(LDUICfg.NewDriverUI).SetLoadedCall(baseUI => { baseUI?.InitView(preRewardData, item); });

                    return true;
                }
            }
            else if (item.Type == LDCommonType.Equipment)
            {
                // Global.gApp.gSystemMgr.gEquipmentMgr.ShowEquipInfoWindow(item.Uid);
                // return true;
            }
            else if (item.Type == LDCommonType.Aircraft)
            {
                Global.gApp.gUiMgr.OpenUIAsync<AircraftGainedUI>(LDUICfg.AircraftGainedUI).SetLoadedCall(baseUI => { baseUI?.RefreshUI(preRewardData, item); });
                return true;
            }

            return false;
        }

        /// <summary>
        /// RewardUI check 弹出
        /// 1 Reward 关闭的时候弹出
        /// 2 特殊 获得 关闭的时候 检测
        /// </summary>
        /// <param name="preRewardData"></param>
        public void CheckExternRewardDataUI(LDRewardUIData preRewardData)
        {
            if (!CheckOpenUI())
            {
                return;
            }

            if (preRewardData != null)
            {
                LDCommonItem SpecialItem = null;
                foreach (LDCommonItem item in preRewardData.Items)
                {
                    if (TryShowExtern(preRewardData, item))
                    {
                        SpecialItem = item;
                        break;
                    }
                }

                if (SpecialItem != null)
                {
                    preRewardData.Items.Remove(SpecialItem);
                }
                else
                {
                    TryShowPerformanceUI();
                }
            }
            else
            {
                TryShowPerformanceUI();
            }
        }

        /// <summary>
        /// 打开角色升级UI
        /// </summary>
        public void TryShowRoleLvUpUI(LDRoleLevelUpUIData lvUpUIData)
        {
            m_RoleLvUpDatas.Add(lvUpUIData);
            TryShowPerformanceUI();
        }

        public void SetPauseShowRoleLvUpUI(bool isPause)
        {
            m_UIRewardData.m_PauseShowRoleLvUp = isPause;
        }

        /// <summary>
        /// 打开系统解锁UI
        /// </summary>
        public void TryShowSysUnlockUI(LDSystemUnlockUIData sysUnlockUIData)
        {
            m_SysUnlockDatas.Add(sysUnlockUIData);
            TryShowPerformanceUI();
        }

        /// <summary>
        /// 打开28天签到
        /// </summary>
        public void TryShowDailyAward()
        {
            m_DailyAwardDatas.Add("1");
            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

        /// <summary>
        /// 打开7 14日签到
        /// </summary>
        public void TryShowStartAward()
        {
            //这个应该只会有1次，但是新账号进来会同步两次
            if (m_StartAwardDatas.Count <= 0)
                m_StartAwardDatas.Add("1");
            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

        /// <summary>
        /// 打开首冲  等级判断
        /// </summary>
        public void TryShowFirstRewardByLv(int Lv)
        {
            List<string> conditions = Global.gApp.gSystemMgr.gFirstRechargeMgr.GetShowConditions();
            if (conditions == null)
                return;

            foreach (string condition in conditions)
            {
                string[] lines = LDCommonTools.Split(condition);
                int type = LDParseTools.IntParse(lines[0]);
                if (type == 5)
                {
                    int disLv = LDParseTools.IntParse(lines[1]);
                    if (disLv == Lv)
                    {
                        m_ShowFirstReward = true;
                    }

                    break;
                }
            }

            TryShowPerformanceUI();
        }

        public void TryShowFirstRewardByMission(int mission)
        {
            List<string> conditions = Global.gApp.gSystemMgr.gFirstRechargeMgr.GetShowConditions();
            if (conditions == null)
                return;

            foreach (string condition in conditions)
            {
                string[] lines = LDCommonTools.Split(condition);
                int type = LDParseTools.IntParse(lines[0]);
                if (type == 1)
                {
                    int disMission = LDParseTools.IntParse(lines[1]);
                    if (disMission == mission)
                    {
                        if (Global.gApp.gSystemMgr.gMainPassMgr.IsPassFinished(disMission))
                            m_ShowFirstReward = true;
                    }

                    break;
                }
            }

            TryShowPerformanceUI();
        }

        /// <summary>
        /// 打开部件礼包  等级判断
        /// </summary>
        public void TryShowDIYPackByMission(int mission = -1)
        {
            m_ShowDIYPackId = Global.gApp.gSystemMgr.gDIYPackMgr.GetIsShowDIYPackId(mission);
            if (m_ShowDIYPackId == -1)
                return;

            TryShowPerformanceUI();
        }


        public bool TryShowCircleActivity(int activityId, long openTime)
        {
            if (activityId == 0)
            {
                return false;
            }

            string key = $"CircleActivity_{activityId}_{openTime}";
            long nextShowTime = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, key);
            if (nextShowTime != 0 && DateTimeUtil.GetServerTime() < nextShowTime)
            {
                return false;
            }

            if (!m_ShowCircleActivity.Contains(activityId))
            {
                m_ShowCircleActivity.Add(activityId);
            }

            TryShowPerformanceUI();

            return true;
        }

        public bool TryShowCollectActivity(int activityId, long openTime)
        {
            if (activityId == 0)
            {
                return false;
            }

            string key = $"CollectActivity_{activityId}_{openTime}";
            long nextShowTime = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, key);
            if (nextShowTime != 0 && DateTimeUtil.GetServerTime() < nextShowTime)
            {
                return false;
            }

            m_ShowCollectActivityId = activityId;

            TryShowPerformanceUI();
            return true;
        }

        /// <summary>
        /// 触发礼包拍脸
        /// </summary>
        /// <param name="giftId"></param>
        public void TryShowTriggerGift(int giftId, long openTime)
        {
            var isShow = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, $"{LDLocalDataKeys.TriggerGift_Hint}_{giftId}_{openTime}", 0);
            if (isShow == 1)
            {
                return;
            }

            var cfg = DIYPack.Data.Get(giftId);
            if (cfg != null)
            {
                if (cfg.Appearance == 1)
                {
                    if (!m_TriggerGiftListImadiate.Contains(giftId))
                    {
                        m_TriggerGiftListImadiate.Add(giftId);
                    }
                }
                else if (cfg.Appearance == 2)
                {
                    if (!m_TriggerGiftList.Contains(giftId))
                    {
                        m_TriggerGiftList.Add(giftId);
                    }

                    if (m_TriggerGiftDelyCount == 0)
                    {
                        m_TriggerGiftDelyCount = 1;
                    }
                }

                TryShowPerformanceUI();
            }
        }

        public void TryReduceTriggerGiftDelayCount()
        {
            if (m_TriggerGiftDelyCount > 0)
            {
                m_TriggerGiftDelyCount -= 1;
            }
        }

        // 机甲转盘
        public void TryShowMechaSpinFinishReward(LDMechaSpinFinishUIData data)
        {
            m_MechaSpinFinishData = data;
            TryShowPerformanceUI();
        }

        /// <summary>
        /// 部件累抽弹窗
        /// </summary>
        /// <param name="id"></param>
        public void TryShowDiyActivity(int id)
        {
            if (!m_DiyActivityShows.Contains(id))
            {
                m_DiyActivityShows.Add(id);
            }

            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

        /// <summary>
        /// 跳转app评价
        /// </summary>
        public void TryShowAppPingJia(int mission)
        {
            if (!RuntimeSettings.IsAudit)
            {
                GlobalCfgItem cfg = GlobalCfg.Data.Get(80044);
                int disMission = cfg.valueInt;
                if (mission > disMission)
                {
                    if (Global.gApp.gSystemMgr.gMainPassMgr.IsPassFinished(mission))
                    {
                        //没存状态
                        if (Global.gApp.gSystemMgr.gNetClientDataMgr.CheckAppPingJiaCanShow())
                        {
                            m_ShowAppPingJia = true;
                        }
                    }
                }
            }
        }

        public void TryShowAppPingJiaNewUser(int mission)
        {
            if (!RuntimeSettings.IsAudit)
            {
                GlobalCfgItem cfg = GlobalCfg.Data.Get(80044);
                int disMission = cfg.valueInt;
                if (mission == disMission)
                {
                    if (Global.gApp.gSystemMgr.gMainPassMgr.IsPassFinished(mission))
                    {
                        //没存状态
                        if (Global.gApp.gSystemMgr.gNetClientDataMgr.CheckAppPingJiaCanShow())
                        {
                            m_ShowAppPingJia = true;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// vip升级
        /// </summary>
        /// <returns></returns>
        public void TryShowVipUpgrade()
        {
            if (!m_ShowVipUpgrade)
                m_ShowVipUpgrade = true;
            TryShowPerformanceUI();
        }

        // 赛季解锁
        public void TryShowSeasonUnlock()
        {
            if (!m_ShowSeasonUnlock)
            {
                m_ShowSeasonUnlock = true;
            }

            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

// 赛季解锁
        public void TryShowSeasonPeakMatchUnlock()
        {
            if (!m_UIRewardData.m_ShowSeasonPeakMatchUnlock)
            {
                m_UIRewardData.m_ShowSeasonPeakMatchUnlock = true;
            }

            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

        // 赛季战斗结算宝箱
        public void TryShowSeasonBoxRewardUI()
        {
            if (!m_ShowSeasonBoxRewardUI)
            {
                m_ShowSeasonBoxRewardUI = true;
            }

            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

        // 赛季战斗结算普通
        public void TryShowSeasonNormalRewardUI()
        {
            if (!m_UIRewardData.m_ShowSeasonPeakMatchFinish)
            {
                m_UIRewardData.m_ShowSeasonPeakMatchFinish = true;
            }

            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

        // 赛季战斗结算转盘
        public void TryShowSeasonTurntableUI()
        {
            if (!m_ShowSeasonTurntableUI)
            {
                m_ShowSeasonTurntableUI = true;
            }

            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

        // 赛季100关 预告歼灭模式界面
        public void TryShowSeasonDFPreViewUI()
        {
            if (!m_ShowSeasonDFPreViewUI)
            {
                m_ShowSeasonDFPreViewUI = true;
            }

            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

        public bool CheckOpenUI()
        {
            if (Global.gApp.gUiMgr.PerformanceUIOpened())
            {
                return false;
            }


            return true;
        }

        //afk auto 双倍 或者免体力
        public void TryShowDoubleReward()
        {
            m_ShowDoubleReward = true;
            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

        public void TryShowNotEnergy()
        {
            m_ShowNotEngry = true;
            if (Global.gApp.gUiMgr.CheckOpenMainUI())
            {
                TryShowPerformanceUI();
            }
        }

        /// <summary>
        /// 检查是不是在主界面
        /// </summary>
        /// <returns></returns>
        public bool CheckOpenMainUI()
        {
            if (Global.gApp.gUiMgr.GetTopUI() is NMainUi)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 检查是不是在组队界面
        /// </summary>
        /// <returns></returns>
        public bool CheckOpenTeamUI()
        {
            if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.TeamInstanceMainUI) != null)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 检查是不是在组队界面
        /// </summary>
        /// <returns></returns>
        public bool CheckOpenEquipmentUI()
        {
            if (Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.EquipmentMainUI) != null)
            {
                return true;
            }

            return false;
        }

        public bool CheckGuide()
        {
            if (!RuntimeSettings.Release && Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(false, LDLocalDataKeys.GM_JumpGuide, 0) > 0)
            {
                return true;
            }

            if (Global.gApp.gSystemMgr.gGuideMgr.HasGuide())
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 新装备获得
        /// </summary>
        public void TryShowNewEquip()
        {
            m_ShowNewEquip = true;
        }

        /// <summary>
        /// 战令 限时的
        /// </summary>
        /// <param name="battlePassId"></param>
        public void TryShowBattlePass(int battlePassId)
        {
            var cfg = GamePlayBattlePass.Data.Get(battlePassId);
            if (cfg != null)
            {
                if (cfg.resetPeriod != 1)
                {
                    return;
                }

                var day = DateTimeUtil.GetMonthOfDay();
                string key = GeneralClientDataKey.BattlePassPublicizeMarkPop + "_" + battlePassId + "_" + day;
                int value = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, key, 0);
                if (value == 0)
                {
                    var begin = DateTimeUtil.GetFirstDayOfCurrentMonthTimestamp() * 1000;
                    var dis = begin + 7 * DateTimeUtil.Day_Mills;
                    if (DateTimeUtil.GetServerTime() < dis)
                    {
                        m_UIRewardData.m_ShowBattlePass[battlePassId] = day;
                    }
                }
            }

            if (m_UIRewardData.m_ShowBattlePass.Count > 0)
            {
                TryShowPerformanceUI();
            }
        }

        /// <summary>
        /// 限时累抽
        /// </summary>
        /// <param name="activityId"></param>
        /// <param name="openTime"></param>
        /// <returns></returns>
        public bool TryShowTimeLimitedRecharge(int activityId, long openTime)
        {
            if (activityId == 0)
            {
                return false;
            }

            string key = $"{LDLocalDataKeys.TimeLimitedShowToday}_{activityId}_{openTime}";
            long nextShowTime = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, key);
            if (nextShowTime != 0 && DateTimeUtil.GetServerTime() < nextShowTime)
            {
                return false;
            }

            if (!m_UIRewardData.m_ShowTimeLimitedRecharge.Contains(activityId))
            {
                m_UIRewardData.m_ShowTimeLimitedRecharge.Add(activityId);
            }

            TryShowPerformanceUI();

            return true;
        }

        public bool TryShowNewCombatPower()
        {
            LDNetActivityInfo activityInfo = Global.gApp.gSystemMgr.gCombatPowerMgr.Data.ActivityInfo;
            if (activityInfo == null || !activityInfo.IsOpen())
            {
                return false;
            }

            string key = $"{LDLocalDataKeys.NewCombatPowerActivity}_{activityInfo.ActivityId}_{activityInfo.BeginTime}";
            long nextShowTime = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, key);
            if (nextShowTime != 0 && DateTimeUtil.GetServerTime() < nextShowTime)
            {
                return false;
            }

            m_UIRewardData.m_NewCombatPowerActivity = true;

            TryShowPerformanceUI();

            return true;
        }

        public bool TryShowPerformanceUI()
        {
            if (!CheckOpenUI())
            {
                return false;
            }

            if (!CheckGuide()) // 后面的界面都会被新手引导阻断
            {
                return false;
            }

            if (m_ShowNotEngry || m_ShowDoubleReward)
            {
                if (m_ShowDoubleReward)
                {
                    OpenUIAsync(DoubleRewardUI);
                }
                else
                {
                    if (m_ShowNotEngry)
                    {
                        OpenUIAsync(NotEnergyUI);
                    }
                }

                m_ShowNotEngry = false;
                m_ShowDoubleReward = false;
                return true;
            }


            if (Global.gApp.CurFightScene != null && Global.gApp.CurFightScene.gPassInfo.GameEnded())
            {
                return false;
            }

            var teamUI = Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.TeamInstanceMainUI);
            if (teamUI == null)
            {
                //
                if (Global.gApp.gSystemMgr.gTeamMgr.TryEnterOldRoom())
                {
                    Global.gApp.gSystemMgr.gTeamMgr.TryShowTeamInviteWindow();
                    return true;
                }
                else
                {
                    Global.gApp.gSystemMgr.gTeamMgr.TryShowTeamInviteWindow();
                }
            }

            if (m_RewardDatas.Count != 0)
            {
                LDRewardUIData rewardUIData = m_RewardDatas[0];
                m_RewardDatas.Remove(rewardUIData);

                if (rewardUIData.OnlyExternReward)
                {
                    CheckExternRewardDataUI(rewardUIData);
                }
                else
                {
                    Global.gApp.gUiMgr.OpenUIAsync<GetRewardUI>(LDUICfg.GetRewardUI).SetLoadedCall(baseUI => { baseUI?.RefreshUI(rewardUIData); });
                }

                return true;
            }

            if (Global.gApp.CurScene is CampsiteScene == false)
            {
                return false;
            }

            if (m_ShowSeasonUnlock)
            {
                m_ShowSeasonUnlock = false;
                OpenUIAsync(LDUICfg.Season_unlcok);
                return true;
            }


            if (m_ShowSeasonBoxRewardUI)
            {
                m_ShowSeasonBoxRewardUI = false;
                OpenUIAsync(LDUICfg.Season_BoxUI);
                return true;
            }


            if (m_ShowSeasonTurntableUI)
            {
                m_ShowSeasonTurntableUI = false;
                OpenUIAsync(LDUICfg.Season_Turntable);
                return true;
            }

            if (m_UIRewardData.m_ShowSeasonPeakMatchFinish && Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.Season_Turntable) == null)
            {
                m_UIRewardData.m_ShowSeasonPeakMatchFinish = false;
                OpenUIAsync<SeasonPeakMatchFinishUI>(LDUICfg.SeasonPeakMatchFinishUI).SetLoadedCall(ui => { ui.InitData(SeasonPeakMatchFinishType.Unlock); });
                return true;
            }

            if (m_UIRewardData.m_ShowSeasonPeakMatchUnlock && Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.Season_Turntable) == null)
            {
                m_UIRewardData.m_ShowSeasonPeakMatchUnlock = false;
                OpenUIAsync<SeasonPeakMatchFinishUI>(LDUICfg.SeasonPeakMatchFinishUI).SetLoadedCall(ui => { ui.InitData(SeasonPeakMatchFinishType.Tips); });
                return true;
            }

            if (m_ShowSeasonDFPreViewUI && Global.gApp.gUiMgr.GetOpenPanelCompent(LDUICfg.Season_Turntable) == null)
            {
                m_ShowSeasonDFPreViewUI = false;
                OpenUIAsync(LDUICfg.SeasonDFPreViewUI);
                return true;
            }


            if (m_RoleLvUpDatas.Count != 0)
            {
                if (m_UIRewardData.m_PauseShowRoleLvUp)
                {
                    return false;
                }
                LDRoleLevelUpUIData roleLevelUpUIData = m_RoleLvUpDatas[0];
                m_RoleLvUpDatas.Remove(roleLevelUpUIData);

                Global.gApp.gUiMgr.OpenUIAsync<RoleLevelUpUI>(LDUICfg.RoleLevelUpUI).SetLoadedCall(baseUI => { baseUI?.RefreshUI(roleLevelUpUIData); });

                return true;
            }

            if (m_SysUnlockDatas.Count != 0 && CheckOpenMainUI())
            {
                LDSystemUnlockUIData sysUnlockUIData = m_SysUnlockDatas[0];
                m_SysUnlockDatas.Remove(sysUnlockUIData);

                Global.gApp.gUiMgr.OpenUIAsync<SystemUnlockUI>(LDUICfg.SystemUnlockUI).SetLoadedCall(baseUI => { baseUI?.RefreshUI(sysUnlockUIData); });

                return true;
            }

            if (m_StartAwardDatas.Count != 0 && !PerformanceFunctionUIOpened())
            {
                if (Global.gApp.gUiMgr.CheckOpenMainUI())
                {
                    m_StartAwardDatas.RemoveAt(0);
                    if (Global.gApp.gSystemMgr.gStartAwardMgr.IsUnlock(false))
                    {
                        if (Global.gApp.gSystemMgr.gStartAwardMgr.CheckNeedShowMainUIBtn())
                        {
                            if (Global.gApp.gSystemMgr.gStartAwardMgr.NeedShowSignIn())
                            {
                                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.StartAwardUI);
                                return true;
                            }
                        }
                    }
                }
            }

            if (m_DailyAwardDatas.Count != 0 && !PerformanceFunctionUIOpened())
            {
                if (Global.gApp.gUiMgr.CheckOpenMainUI())
                {
                    m_DailyAwardDatas.RemoveAt(0);
                    if (Global.gApp.gSystemMgr.gDailyAwardMgr.IsUnlock(false))
                    {
                        if (Global.gApp.gSystemMgr.gDailyAwardMgr.NeedShowSignIn())
                        {
                            Global.gApp.gUiMgr.OpenUIAsync<WelfareUI>(LDUICfg.WelfareUI).SetLoadedCall(baseUI => { baseUI?.OnFreshUI(LDSystemEnum.DailyAward.GetHashCode()); });

                            return true;
                        }
                    }
                }
            }

            if (m_ShowFirstReward && !PerformanceFunctionUIOpened() && CheckOpenMainUI())
            {
                m_ShowFirstReward = false;
                int firstId = Global.gApp.gSystemMgr.gFirstRechargeMgr.GetShowId();
                if (firstId != -1)
                {
                    Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.FirstRechargeUI);
                    return true;
                }
            }

            if (m_ShowDIYPackId != -1)
            {
                int val = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.MissionPack_Tips + m_ShowDIYPackId, 0);
                if (val == 0 && !PerformanceFunctionUIOpened() && CheckOpenMainUI())
                {
                    int activityId = m_ShowDIYPackId;
                    Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.MissionPack_Tips + m_ShowDIYPackId, m_ShowDIYPackId);
                    Global.gApp.gUiMgr.OpenUIAsync<DIYPackUI>(LDUICfg.DIYPackUI).SetLoadedCall(baseUI => { baseUI.InitData(activityId); });

                    m_ShowDIYPackId = -1;
                    return true;
                }
            }

            if (m_ShowCircleActivity.Count > 0 && CheckOpenMainUI() && !PerformanceFunctionUIOpened())
            {
                int activityId = m_ShowCircleActivity[0];
                SeasonActivityCfg activityCfg = Global.gApp.gSystemMgr.gCircleActivityMgr.GetSeasonActivityCfg(activityId);

                if (activityCfg?.EnterUI == LDUICfg.AeroplaneStartUI)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<AeroplaneStartUI>(activityCfg.EnterUI).SetLoadedCall(baseUI => { baseUI.InitData(activityId); });
                }
                else if (activityCfg?.EnterUI == LDUICfg.Slots_StartUI)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<Slots_StartUI>(activityCfg.EnterUI).SetLoadedCall(baseUI => { baseUI.InitData(activityId); });
                }
                else if (activityCfg?.EnterUI == LDUICfg.NinjaTrailStartUI)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<NinjaTrailStartUI>(activityCfg.EnterUI).SetLoadedCall(baseUI => { baseUI.InitData(activityId); });
                }
                else if (activityCfg?.EnterUI == LDUICfg.MechaReissueStartUI)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<MechaReissueStartUI>(activityCfg.EnterUI).SetLoadedCall(baseUI => { baseUI.InitData(activityId); });
                }
                else if (activityCfg?.EnterUI == LDUICfg.MechaTreasureStartUI)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<MechaTreasureStartUI>(activityCfg.EnterUI).SetLoadedCall(baseUI => { baseUI.InitData(activityId); });
                }
                else if (activityCfg?.EnterUI == LDUICfg.DeepExploreStartUI)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<DeepExploreStartUI>(activityCfg.EnterUI).SetLoadedCall(baseUI => { baseUI.InitData(activityId); });
                }
                else if (activityCfg?.EnterUI == LDUICfg.RouletteStartUI)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<RouletteStartUI>(activityCfg.EnterUI).SetLoadedCall(baseUI => { baseUI.InitData(activityId); });
                }

                m_ShowCircleActivity.Remove(activityId);
                return true;
            }

            if (m_ShowCollectActivityId != -1 && CheckOpenMainUI() && !PerformanceFunctionUIOpened())
            {
                if (Global.gApp.gSystemMgr.gCollectActivityMgr.IsUnlock())
                {
                    int activityId = m_ShowCollectActivityId;
                    Global.gApp.gUiMgr.OpenUIAsync<CollectAdUI>(LDUICfg.CollectAdUI).SetLoadedCall(baseUI => { baseUI.InitData(activityId); });
                    m_ShowCollectActivityId = -1;

                    return true;
                }
            }

            //触发礼包  立即触发
            if (m_TriggerGiftListImadiate.Count > 0)
            {
                if (!PerformanceFunctionUIOpened())
                {
                    int gift = m_TriggerGiftListImadiate[0];
                    m_TriggerGiftListImadiate.RemoveAt(0);

                    Global.gApp.gUiMgr.OpenUIAsync<TriggerGift_Hint>(LDUICfg.TriggerGift_Hint).SetLoadedCall(baseUI => { baseUI.RefreshView(gift); });

                    LDDIYPackInfo giftInfo = Global.gApp.gSystemMgr.gPaymentMgr.Data.GetDiyPackInfo(gift);
                    if (giftInfo != null)
                    {
                        Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, $"{LDLocalDataKeys.TriggerGift_Hint}_{gift}_{giftInfo.OpenTime}", 1);
                    }

                    return true;
                }
            }

            //触发礼包
            if (m_TriggerGiftList.Count > 0 && m_TriggerGiftDelyCount == 0)
            {
                if (!PerformanceFunctionUIOpened() && CheckOpenMainUI())
                {
                    int gift = m_TriggerGiftList[0];
                    var cfg = DIYPack.Data.Get(gift);
                    if (cfg != null)
                    {
                        bool isShow = false;
                        if (cfg.advertisementImage.Length > 0)
                        {
                            if (Global.gApp.gSystemMgr.gFilterMgr.Filter(cfg.advertisementImage[0]))
                            {
                                isShow = true;
                            }
                        }
                        else
                        {
                            isShow = true;
                        }

                        if (isShow)
                        {
                            m_TriggerGiftList.RemoveAt(0);
                            LDDIYPackInfo giftInfo = Global.gApp.gSystemMgr.gPaymentMgr.Data.GetDiyPackInfo(gift);
                            if (giftInfo != null)
                            {
                                if (giftInfo.IsOpen() && !giftInfo.IsBuy())
                                {
                                    Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, $"{LDLocalDataKeys.TriggerGift_Hint}_{gift}_{giftInfo.OpenTime}", 1);
                                    Global.gApp.gUiMgr.OpenUIAsync<TriggerGift_Hint>(LDUICfg.TriggerGift_Hint).SetLoadedCall(baseUI => { baseUI.RefreshView(gift); });
                                }
                                else
                                {
                                    TryShowPerformanceUI();
                                }
                            }

                            return true;
                        }
                    }
                }
            }

            // 章节礼包
            if (Global.gApp.gSystemMgr.gMissionPackMgr.CheckOpenTips() && CheckOpenMainUI())
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.MissionPackMain);
                return true;
            }

            // 机甲转盘拍脸
            if (
                Global.gApp.gSystemMgr.gMechaSpinMgr.IsUnlock() &&
                Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.MechaSpin_Open, 0) == 0 &&
                CheckOpenMainUI()
            )
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.MechaSpin_Hint);
                return true;
            }

            // 机甲转盘抽完所有奖励
            if (m_MechaSpinFinishData != null)
            {
                Global.gApp.gUiMgr.OpenUIAsync<MechaSpinFinishUI>(LDUICfg.MechaSpinFinishUI).SetLoadedCall(baseUI =>
                {
                    baseUI.RefreshUI(m_MechaSpinFinishData.ShowDatas, m_MechaSpinFinishData.FinishNum);
                });

                m_MechaSpinFinishData = null;
                return true;
            }

            //部件累抽
            if (m_DiyActivityShows.Count > 0 && !PerformanceFunctionUIOpened() && CheckOpenMainUI())
            {
                int id = m_DiyActivityShows[0];
                m_DiyActivityShows.RemoveAt(0);

                var openServerActivityItemCfg = Global.gApp.gSystemMgr.gDiyActivityMgr.GetDiyActivityCfg(id);
                if (openServerActivityItemCfg != null)
                {
                    if (openServerActivityItemCfg.ActivityType == LDNetActivityType.DIYActivityType)
                    {
                        Global.gApp.gUiMgr.OpenUIAsync<DiyActivity_Hint>(DiyActivityHint).SetLoadedCall(baseUI => { baseUI.InitData(id); });
                    }
                    else if (openServerActivityItemCfg.ActivityType == LDNetActivityType.UAVActivityType)
                    {
                        Global.gApp.gUiMgr.OpenUIAsync<UAVActivity_Hint>(UAVActivityHint).SetLoadedCall(baseUI => { baseUI.InitData(id); });
                    }
                    else if (openServerActivityItemCfg.ActivityType == LDNetActivityType.CombatTargetType)
                    {
                        Global.gApp.gUiMgr.OpenUIAsync<CombatTarget_Hint>(LDUICfg.CombatTarget_Hint).SetLoadedCall(baseUI => { baseUI.InitData(id); });
                    }

                    var info = Global.gApp.gSystemMgr.gActivityMgr.Data.GetActivityInfo(id);
                    if (info != null)
                    {
                        Global.gApp.gSystemMgr.gNetClientDataMgr.ChangeGenerateClientData($"{GeneralClientDataKey.DiyActivityMark}_{info.BeginTime}", "1");
                    }

                    return true;
                }
            }

            if (m_ShowVipUpgrade)
            {
                OpenUIAsync(LDUICfg.VipPrivilegeUpgradeUI);
                m_ShowVipUpgrade = false;
            }


            if (m_ShowNewEquip && !PerformanceFunctionUIOpened() && CheckOpenEquipmentUI())
            {
                var equipInfo = Global.gApp.gSystemMgr.gEquipmentMgr.Data.GetNewEquip();
                if (equipInfo != null)
                {
                    Global.gApp.gSystemMgr.gEquipmentMgr.ShowEquipInfoWindow(equipInfo);
                    return true;
                }
                else
                {
                    m_ShowNewEquip = false;
                }
            }

            if (m_UIRewardData.m_ShowBattlePass.Count > 0 && !PerformanceFunctionUIOpened() && CheckOpenMainUI())
            {
                foreach (KeyValuePair<int, int> pair in m_UIRewardData.m_ShowBattlePass)
                {
                    var battlePassId = pair.Key;
                    var cfg = GamePlayBattlePass.Data.Get(battlePassId);
                    if (Global.gApp.gSystemMgr.gFilterMgr.Filter(cfg.unlockCondition))
                    {
                        var day = pair.Value;
                        string key = GeneralClientDataKey.BattlePassPublicizeMarkPop + "_" + battlePassId + "_" + day;
                        Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, key, day);

                        Global.gApp.gUiMgr.OpenUIAsync<BattlePassBuyPublicize>(LDUICfg.BattlePassBuyPublicize).SetLoadedCall(ui =>
                            ui.RefreshView(battlePassId));

                        m_UIRewardData.m_ShowBattlePass.Remove(battlePassId);

                        return true;
                    }
                }
            }

            if (m_UIRewardData.m_ShowTimeLimitedRecharge.Count > 0 && !PerformanceFunctionUIOpened() && CheckOpenMainUI())
            {
                m_UIRewardData.m_ShowTimeLimitedRecharge.RemoveAt(0);
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.TimeLimitedRechargeStartUI);
                return true;
            }

            if (m_UIRewardData.m_NewCombatPowerActivity && !PerformanceFunctionUIOpened() && CheckOpenMainUI())
            {
                m_UIRewardData.m_NewCombatPowerActivity = false;
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.CombatPower_StartUI);
                return true;
            }

            //新的在上面加！！！！
            //这个是新的拍脸系统，不用这个系统的都加在上面
            foreach (PerformanceTaskBase task in m_PerformanceTasks)//todo 增加循环索引
            {
                if (task.TryRun())
                {
                    return true;
                }
            }

            //新的在上面加！！！！
            //这个是弹评价跳转的，这个放到最后，后面加新的写上面！！！！！！
            if (m_ShowAppPingJia && !PerformanceFunctionUIOpened() && CheckOpenMainUI())
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.AppPingJia);
                m_ShowAppPingJia = false;
                Global.gApp.gSystemMgr.gNetClientDataMgr.SetAppPingJiaIsShow();
                return true;
            }

            if (Global.gApp.gSystemMgr.gSettingMgr.TryOpenBindUI())
            {
                return true;
            }


            // 上面的if  记得都要return
            Global.gApp.gMsgDispatcher.Broadcast(MsgIds.OnShowPerformanceUIFinish);
            return false;
        }
    }
}