using UnityEngine;

namespace LD
{
    public partial class ArenaMechaDiyUI
    {
        public LDArenaBodyBlockInstaller BodyBlockInstaller { set; get; }
        private LDBodyBlock m_SelectBodyBlock;
        public Camera RayCamera { set; get; }
        public Camera RenderCamera { set; get; }
        private bool m_RayBolck = false;
        private Vector3 m_CurMousePos;
        private Collider m_RayCollider;
        private RenderTexture m_RenderTex;

        private void InitDIYNode()
        {
            Global.gApp.gUICameraCmpt.cullingMask = LDFightConstVal.UICameraFightMask;
            RayCamera = m_RayCameraNode.gameObject.GetComponentInChildren<Camera>(true);
            RenderCamera = m_RenderCameraNode.gameObject.GetComponentInChildren<Camera>(true);

            //m_DIYShow.gameObject.SetActive(false);


            //Global.gApp.gUICameraCmpt.GetUniversalAdditionalCameraData().cameraStack.Add(RayCamera);
            m_RenderTex = UiTools.CreatureTexture(1, true);
            RenderCamera.targetTexture = m_RenderTex;
            m_DIYShow.rawImage.texture = m_RenderTex;
            Global.gApp.gGameAdapter.AdaptCamera(RayCamera);
            // Global.gApp.gGameAdapter.AdaptCamera(RenderCamera);
        }

        public void TryCreateBodyBlockInstaller(DIYPartCfgItem partCfgItem, LDCreature creature2)
        {
            int equipCount = m_Creature.GetBlockCountByType(partCfgItem.pos);
            MechaItem mechaItem = Mecha.Data.Get(m_Creature.DiyData.BodyId);
            if (partCfgItem.pos == LDDIYPartItemType.PartWeapon)
            {
                // 已经装备不能装备
                if (m_Creature.BlockEquipment(partCfgItem.id))
                {
                    string tips = Global.gApp.gGameData.GetTipsInCurLanguage(95014);
                    tips = string.Format(tips, "1");
                    Global.gApp.gToastMgr.ShowGameTips(tips);

                    return;
                }

                if (equipCount >= mechaItem.weapon)
                {
                    Global.gApp.gToastMgr.ShowGameTips(95012);
                    return;
                }
            }
            else
            {
                if (equipCount >= mechaItem.equip)
                {
                    Global.gApp.gToastMgr.ShowGameTips(95013);
                    return;
                }
            }

            int maxEquipCount = m_Creature.BlockEquipmentCount(partCfgItem.id);
            if (maxEquipCount >= partCfgItem.maxNum)
            {
                string tips = Global.gApp.gGameData.GetTipsInCurLanguage(95014);
                tips = string.Format(tips, partCfgItem.maxNum.ToString());
                Global.gApp.gToastMgr.ShowGameTips(tips);
                return;
            }

            if (BodyBlockInstaller == null)
            {
                BodyBlockInstaller = new LDArenaBodyBlockInstaller(this, m_Creature);
                BodyBlockInstaller.InitByUI(partCfgItem, creature2);
            }

            ChangeSelectBlock(null);
        }

        public void DestroyBodyBlockInstaller(LDBodyBlock bodyBlock)
        {
            if (BodyBlockInstaller != null)
            {
                BodyBlockInstaller.OnDestroySelf();
                BodyBlockInstaller = null;
                if (bodyBlock != null)
                {
                    if (bodyBlock.Attached)
                    {
                        ChangeSelectBlock(bodyBlock);
                    }
                }
            }
            else
            {
                ChangeSelectBlock(null);
            }

            m_RayCollider = null;
            m_RayBolck = false;
            OnDIYHandleEnd();
            FreshDIYItemState();
        }

        private void FreshDIYItemState()
        {
            foreach (ArenaMechaDiyUI_DiyItem item in DiyItem.mCachedList)
            {
                item.FreshState();
            }
        }

        public bool GetRayBolck()
        {
            return m_RayBolck;
        }

        private void OnUpdateHandle()
        {
            //RayCamera.transform.LookAt(m_DIYNode.transform,Vector3.up);
            //RenderCamera.transform.LookAt(m_DIYNode.transform,Vector3.up);
            if (BodyBlockInstaller != null)
            {
                m_RayCollider = null;
                m_RayBolck = false;
                BodyBlockInstaller.OnDUpdate(Time.deltaTime);
                GetCreature().SetAnimSpeed(0);
            }
            else
            {
                if (!m_RayBolck)
                {
                    if (Input.GetMouseButtonDown(0))
                    {
                        if (!m_AdjustPanel.gameObject.activeSelf)
                        {
                            ChangeSelectBlock(null);
                        }


                        Ray ray = RayCamera.ScreenPointToRay(GetTouchPos());
                        RaycastHit raycastHit = new RaycastHit();
                        //Debug.DrawLine(ray.origin, ray.direction * 1000,Color.red,10);
                        if (Physics.Raycast(ray, out raycastHit, Mathf.Infinity, LDFightConstVal.MechaMask))
                        {
                            if (raycastHit.collider.gameObject.layer == LDFightConstVal.MechaPart)
                            {
                                m_RayBolck = true;
                                m_CurMousePos = GetTouchPos();
                                m_RayCollider = raycastHit.collider;
                            }
                        }
                    }
                }
                else
                {
                    if (m_RayCollider != null)
                    {
                        if (Input.GetMouseButton(0))
                        {
                            if ((GetTouchPos() - m_CurMousePos).sqrMagnitude > 25)
                            {
                                BodyBlockInstaller = new LDArenaBodyBlockInstaller(this, m_Creature);
                                BodyBlockInstaller.InitByRayBlock(m_RayCollider);
                                ChangeSelectBlock(m_Creature.GetBodyBlockByMeshCollider(m_RayCollider));
                                m_RayBolck = false;
                                m_RayCollider = null;
                            }
                        }
                        else if (Input.GetMouseButtonUp(0))
                        {
                            ChangeSelectBlock(m_Creature.GetBodyBlockByMeshCollider(m_RayCollider));
                            m_RayCollider = null;
                            m_RayBolck = false;
                        }
                    }
                }

                GetCreature().SetAnimSpeed(1);
            }
        }

        public Vector3 GetTouchPos()
        {
            Vector3 screenOffset = Global.gApp.gGameAdapter.GetScreenScale();
            Vector3 mousePos = Input.mousePosition;
            return mousePos;
        }

        public bool TryChangeSelectBlock(int blockId)
        {
            if (BodyBlockInstaller == null)
            {
                bool equipState = m_Creature.BlockEquipment(blockId);
                if (equipState)
                {
                    if (m_SelectBodyBlock != null)
                    {
                        if (m_SelectBodyBlock.PartItem.id == blockId)
                        {
                            return false;
                        }
                    }

                    ChangeSelectBlock(blockId);
                    return true;
                }
                else
                {
                    ChangeSelectBlock(null);
                }
            }

            return false;
        }

        public bool HasSelectBlock()
        {
            return m_SelectBodyBlock != null;
        }

        private void ChangeSelectBlock(int blockId)
        {
            LDBodyBlock bodyBlock = m_Creature.GetBlockEnableByBlockItemId(blockId);
            ChangeSelectBlock(bodyBlock);
        }

        private void ChangeSelectBlock(LDBodyBlock bodyBlock)
        {
            if (bodyBlock != null)
            {
                OnCanSave();
            }

            if (m_SelectBodyBlock != null)
            {
                m_SelectBodyBlock.SetSelect(false);
                m_SelectBodyBlock = null;
            }

            m_SelectBodyBlock = bodyBlock;
            m_SelectBodyBlock?.SetSelect(true);
            if (bodyBlock != null && BodyBlockInstaller == null)
            {
                OnShowAdjust();
            }
            else
            {
                OnHideAdjust();
            }
        }

        public void OnCanSave()
        {
            m_btn_reset.gameObject.SetActive(true);
            m_Creature.CanSave = true;
        }

        protected void CloseDIYHandle()
        {
            DestroyBodyBlockInstaller(null);
            Physics.autoSimulation = false;
            RenderCamera.targetTexture = null;
            m_DIYShow.rawImage.texture = null;
            GameObject.Destroy(m_RenderTex);
        }
    }
}