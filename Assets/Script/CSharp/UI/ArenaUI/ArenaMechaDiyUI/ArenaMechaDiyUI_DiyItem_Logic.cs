namespace LD
{
    public partial class ArenaMechaDiyUI_DiyItem
    {
        private ArenaMechaDiyUI m_MainView;
        private DIYPartCfgItem m_Item;
        private LDNetMechaPartItemDTO m_NetMechaPartItemDTO;

        private KnapsackItem m_KnapsackItem;
        private RedTips m_RedTipCompt;
        private int m_MechaId;

        public override void ItemInit()
        {
            if (m_KnapsackItem == null)
            {
                m_KnapsackItem = LDUIPrefabTools.GetKnapsackItemUI(m_scaleNode.rectTransform);
                m_KnapsackItem.SetShowDetail(false);
            }

            if (m_RedTipCompt == null)
            {
                m_RedTipCompt = m_RedTips.gameObject.GetComponent<RedTips>();
            }
        }

        public void Init(ArenaMechaDiyUI mainUI, DIYPartCfgItem item, int mechaId)
        {
            m_MainView = mainUI;
            m_Item = item;
            m_MechaId = mechaId;
            m_NetMechaPartItemDTO = Global.gApp.gSystemMgr.gMechaPartDataMgr.Data.GetNetMechaPartItemDTO(item.id);
            LDUIPrefabTools.InitKnapsackItem(m_KnapsackItem, m_NetMechaPartItemDTO,false);

            m_BtnNode.gameObject.GetComponent<LDDragButton>().OnPointMove = OnPointMove;
            m_BtnNode.gameObject.GetComponent<LDDragButton>().OnPointClick = OnPointClick;
            FreshState();
        }

        public void FreshState()
        {
            if (m_MainView != null)
            {
                bool equipState = m_MainView.GetCreature().BlockEquipment(m_Item.id);
                m_KnapsackItem.equip.gameObject.SetActive(equipState);
            }

            m_comboSkillNode.gameObject.SetActive(false);
            if (m_Item.pos == LDDIYPartItemType.PartWeapon)
            {
                m_partLevel_bg.gameObject.SetActive(true);
                string strTxt = Global.gApp.gGameData.GetTipsInCurLanguage(9546);
                m_txt_partLevel.text.text = string.Format(strTxt, m_NetMechaPartItemDTO.Lv);
            }
            else
            {
                m_partLevel_bg.gameObject.SetActive(false);
            }

            FreshRedState();
            LDNetMechaDTOItem mechaData = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaData(m_MechaId);
            if (mechaData != null)
            {
                MechaItem mechaItem = Mecha.Data.Get(mechaData.MechaId);
                foreach (int partId in mechaItem.optionalWeapon)
                {
                    if (partId == m_Item.id)
                    {
                        LoadSprite(m_img_mechaIcon.image, mechaItem.circleIcon);
                        m_comboSkillNode.gameObject.SetActive(true);
                        return;
                    }
                }
            }
        }

        private void OnPointClick()
        {
            if (Global.gApp.gSystemMgr.gMechaPartDataMgr.ClearPartNewState(m_Item.id))
            {
                FreshRedState();
            }

            if (m_MainView != null && m_MainView.BodyBlockInstaller == null)
            {
                if (!m_MainView.TryChangeSelectBlock(m_Item.id))
                {
                    m_MainView.OnOpenTips(m_Item);
                }
            }

            FreshRedState();
            m_MainView.FreshArrowRedTips();
        }

        private void FreshRedState()
        {
            // if (Global.gApp.gSystemMgr.gMechaPartDataMgr.IsNewPart(m_Item.id))
            // {
            //     m_RedTipCompt.FreshState(LDRedTipsState.RedPoint);
            // }
            // else
            // {
            // }
            m_RedTipCompt.FreshState(LDRedTipsState.None);
        }

        private void OnPointMove()
        {
            if (Global.gApp.gSystemMgr.gMechaPartDataMgr.ClearPartNewState(m_Item.id))
            {
                FreshRedState();
            }

            if (m_MainView != null)
            {
                m_MainView.TryCreateBodyBlockInstaller(m_Item, m_MainView.GetCreature());
            }

            m_MainView.FreshArrowRedTips();
        }

        public override void ItemRecycle()
        {
        }
    }
}