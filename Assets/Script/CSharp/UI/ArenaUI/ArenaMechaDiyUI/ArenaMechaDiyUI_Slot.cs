using UnityEngine.UI;

namespace LD
{
    public partial class ArenaMechaDiyUI
    {
        private void InitSlot()
        {
        }

        private void FreshSlot()
        {
            //equipSlotItem.CacheInstanceList();
            //weaponSlotItem.CacheInstanceList();

            MechaItem mechaItem = Mecha.Data.Get(m_Creature.DiyData.BodyId);
            if (m_WpnState)
            {
                if (mechaItem != null)
                {
                    int wpnEquipCount = m_Creature.GetBlockCountByType(LDDIYPartItemType.PartWeapon);
                    m_txt_weapon_Num.text.SetTips(12814);
                    string txtWpn = ("(" + wpnEquipCount + "/" + mechaItem.weapon + ")");
                    m_txt_fullEquip.text.SetText(txtWpn);
                    m_txt_fullEquip_Gray.text.SetText(txtWpn);

                    if (!string.IsNullOrEmpty(mechaItem.WeaponLoadReward))
                    {
                        m_fullEquipNode.gameObject.SetActive(true);
                        LDAttrAddition lDAttrAddition = new LDAttrAddition(mechaItem.WeaponLoadReward);
                        string txt = ": " + lDAttrAddition.GetNameText() + " +" + lDAttrAddition.GetValueStr();
                        m_txt_fullEquipAttr.text.text = txt;
                        m_txt_fullEquipAttr_Gray.text.text = txt;
                        if (wpnEquipCount < mechaItem.weapon)
                        {
                            m_txt_fullEquipAttr.gameObject.SetActive(false);
                            m_txt_fullEquip.gameObject.SetActive(false);
                            m_txt_fullEquipAttr_Gray.gameObject.SetActive(true);
                            m_txt_fullEquip_Gray.gameObject.SetActive(true);
                        }
                        else
                        {
                            m_txt_fullEquipAttr.gameObject.SetActive(true);
                            m_txt_fullEquip.gameObject.SetActive(true);
                            m_txt_fullEquipAttr_Gray.gameObject.SetActive(false);
                            m_txt_fullEquip_Gray.gameObject.SetActive(false);
                        }
                    }
                    else
                    {
                        m_fullEquipNode.gameObject.SetActive(false);
                    }
                }
            }
            else
            {
                m_txt_fullEquipAttr.gameObject.SetActive(false);
                m_txt_fullEquipAttr_Gray.gameObject.SetActive(false);
                int equipmentCount = m_Creature.GetBlockCountByType(LDDIYPartItemType.PartEquipment);
                string txtWpn = ("(" + equipmentCount + "/" + mechaItem.equip + ")  ");
                m_txt_fullEquip.text.SetText(txtWpn);
                m_txt_fullEquip_Gray.text.SetText(txtWpn);

                m_txt_fullEquip.gameObject.SetActive(equipmentCount >= mechaItem.equip);
                m_txt_fullEquip_Gray.gameObject.SetActive(equipmentCount < mechaItem.equip);
            }

            m_txt_equip_Num.text.SetTips(12815);
            m_txt_weapon_Num.gameObject.SetActive(m_WpnState);
            m_txt_equip_Num.gameObject.SetActive(!m_WpnState);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_fullEquipNode.rectTransform);
        }
    }
}