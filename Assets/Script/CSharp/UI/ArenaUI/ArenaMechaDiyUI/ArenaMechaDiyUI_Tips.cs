using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class ArenaMechaDiyUI
    {
        public void OnOpenTips(DIYPartCfgItem dIYPartCfgItem)
        {
            tipsPanel.gameObject.SetActive(true);
            LDNetMechaPartItemDTO mechaPartItemDTO = m_MechaPartDataMgr.Data.GetNetMechaPartItemDTO(dIYPartCfgItem.id);
            LoadSprite(m_img_element.image, LDUIResTools.GetElementIconPath(dIYPartCfgItem.element));
            m_txt_partName.text.SetTips(dIYPartCfgItem.name);
            m_txt_partName.text.SetQuaColor(mechaPartItemDTO.GetShowQua());
            m_txt_partLv.text.text = "Lv." + mechaPartItemDTO.Lv;
            m_txt_partDesc.text.text = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartTips(dIYPartCfgItem.id,false); ;

            m_txt_equip_Desc.gameObject.SetActive(dIYPartCfgItem.pos == LDDIYPartItemType.PartEquipment);
            m_txt_partDesc.gameObject.SetActive(dIYPartCfgItem.pos == LDDIYPartItemType.PartWeapon);

            skillItem.CacheInstanceList();
            int index = 0;
            foreach (int citiaoId in dIYPartCfgItem.EntryGroup)
            {
                index++;
                ArenaMechaDiyUI_skillItem mechaDiyUI_SkillItem = skillItem.GetInstance();
                mechaDiyUI_SkillItem.InitData(index,dIYPartCfgItem); 
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(m_img_tips_BG.rectTransform as RectTransform);
        }
        public void OnCloseTips()
        {
            tipsPanel.gameObject.SetActive(false);
        } 
    }
}