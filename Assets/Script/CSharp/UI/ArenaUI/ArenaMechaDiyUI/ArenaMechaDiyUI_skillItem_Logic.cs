using UnityEngine;

namespace LD
{
    public partial class ArenaMechaDiyUI_skillItem
    {
        public void InitData(int skillIndex, DIYPartCfgItem partCfgItem)
        {
            int citiaoId = partCfgItem.EntryGroup[skillIndex - 1];
            //m_txt_skillName.text.SetText();

            CitiaoItem citiaoItem = Citiao.Data.Get(citiaoId);
            if (citiaoItem == null)
            {
                return;
            }

            m_txt_skillNameNew.text.SetTips(citiaoItem.citiaoName);

            LoadSprite(m_img_skillIcon1.image, LDUIResTools.GetElementIconPath(partCfgItem.element));

            int[] optionalCondition = citiaoItem.optionalCondition;
            if (optionalCondition.Length > 0)
            {
                int opType = optionalCondition[0];
                if (opType == LDCiTiaoOptionCondition.DIYPart) //����ָ������������ID��
                {
                    string iconPath = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartIcon(optionalCondition[1]);
                    LoadSprite(m_img_skillIcon2.image, iconPath);
                }
                else if (opType == LDCiTiaoOptionCondition.DIYPartXI) //����ָ��ϵ������ϵ����ö�٣�
                {
                    //DIYPartCfgItem dIYPartCfgItem = DIYPartCfg.Data.Get(optionalCondition[1]);
                    LoadSprite(m_img_skillIcon2.image, LDUIResTools.GetElementIconPath(optionalCondition[1]));
                }
                else if (opType == LDCiTiaoOptionCondition.Mecha) //����ָ�����ף�����ID��
                {
                    string iconPath = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaIcon(optionalCondition[1]);
                    LoadSprite(m_img_skillIcon2.image, iconPath);
                }
                else if (opType == LDCiTiaoOptionCondition.MechaXI) //����ָ��ϵ���ף�ϵ����ö�٣�
                {
                    LoadSprite(m_img_skillIcon2.image, LDUIResTools.GetElementIconPath(optionalCondition[1]));
                }
            }
            else
            {
                Debug.LogError("citiaoId optionalCondition enempty ");
            }
        }
    }
}