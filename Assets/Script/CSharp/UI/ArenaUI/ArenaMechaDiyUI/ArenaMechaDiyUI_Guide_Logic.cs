namespace LD
{
    public partial class ArenaMechaDiyUI
    {
        private int m_GuideTimer = 0;

        public void TryGuide()
        {
            m_GuideEffect.gameObject.SetActive(false);
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.BattleDIY, 0);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.BattleDIY, m_AdjustPanel.rectTransform, 1))
            {
                m_AdjustPanel.gameObject.SetActive(true);
                m_GuideBtn.rectTransform.SetParent(m_AdjustPanel.rectTransform, true);
                m_GuideEffect.gameObject.SetActive(true);
                return;
            }

            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.FirstDIY, 1);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.FirstDIY, null, 2))
            {
                return;
            }

            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.FirstDIY, 2);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.FirstDIY, m_GuideBtn.rectTransform, 3))
            {
                return;
            }

            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.SecondDIY, 1);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.SecondDIY, null, 2))
            {
                return;
            }

            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.SecondDIY, 2);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.SecondDIY, m_GuideBtn.rectTransform, 3))
            {
                return;
            }

            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.MechaChange, 5);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.MechaChange, m_GuideBtn.rectTransform, 6))
            {
                return;
            }
        }

        public void ForceGuide()
        {
            Global.gApp.gSystemMgr.gGuideMgr.ForceStartGuide(LDGuideType.BattleDIY, null);
        }

        private void OnGuide()
        {
            if (Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.BattleDIY, 1))
            {
                m_GuideEffect.gameObject.SetActive(false);
                m_GuideBtn.rectTransform.SetParent(m_Guide.rectTransform, true);
                TryAddGuideTimer();
            }

            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.FirstDIY, 3);
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.SecondDIY, 3);
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.MechaChange, 6);
        }

        private void TryAddGuideTimer()
        {
            m_GuideTimer = AddTimer(8, 1, OnGuideTimerOver);
        }

        private void OnGuideTimerOver(float arg1, bool arg2)
        {
            Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.BattleDIY, m_btn_close.rectTransform, 2);
        }

        private void OnUpdateGuide()
        {
            if (m_GuideTimer > 0)
            {
                if (Global.GetTouchCount() > 0)
                {
                    RemoveTimer(m_GuideTimer);
                    TryAddGuideTimer();
                }
            }
        }
    }
}