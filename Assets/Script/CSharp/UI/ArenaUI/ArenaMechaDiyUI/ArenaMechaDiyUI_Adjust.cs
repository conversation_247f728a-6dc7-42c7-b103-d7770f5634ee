namespace LD
{
    public partial class ArenaMechaDiyUI
    {
        private void InitAdjust()
        {
            OnHideAdjust();
            m_btn_AdjustMask.AddListener(OnCloseJustPanel);
            m_btn_delete.AddListener(OnDelBlock);
            //m_btn_skin.AddListener(OnSkinPart);
        }

        private void OnSkinPart()
        {
            //PartUpgradeUI partUpgradeUI = Global.gApp.gUiMgr.OpenUI(LDUICfg.PartUpgradeUI) as PartUpgradeUI;
            //DIYPartCfgItem dIYPartCfgItem = DIYPartCfg.Data.Get(m_SelectBodyBlock.Pid);
            //partUpgradeUI.InitByItem(dIYPartCfgItem);
            //partUpgradeUI.OnShowDecorateView();
            //partUpgradeUI.tabNode.gameObject.SetActive(false);
        }

        private void OnDelBlock()
        {
            if (m_SelectBodyBlock != null)
            {
                m_SelectBodyBlock.DeAttach();
                m_SelectBodyBlock.DestroyBodyBlock();
                FreshDIYItemState();
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_DIY_Last);
            }

            OnDIYHandleEnd();
            OnCloseJustPanel();
        }

        public void OnCloseJustPanel()
        {
            OnHideAdjust();
            ChangeSelectBlock(null);
        }

        private void OnShowAdjust()
        {
            if (m_SelectBodyBlock == null)
            {
                return;
            }

            m_AdjustPanel.gameObject.SetActive(true);
            m_btn_delete.gameObject.SetActive(true);
            adjustItem.CacheInstanceList();
            int selectPartId = m_SelectBodyBlock.Pid;

            DIYPartCfgItem partCfgItem = DIYPartCfg.Data.Get(selectPartId);
            if (partCfgItem.axisRotation.Length > 0)
            {
                ArenaMechaDiyUI_adjustItem mechaDiyUI_AdjustItem = adjustItem.GetInstance();
                mechaDiyUI_AdjustItem.Init(1, m_SelectBodyBlock);
            }

            if (partCfgItem.zoom.Length > 0)
            {
                ArenaMechaDiyUI_adjustItem mechaDiyUI_AdjustItem = adjustItem.GetInstance();
                mechaDiyUI_AdjustItem.Init(2, m_SelectBodyBlock);
            }
        }

        private void OnHideAdjust()
        {
            adjustItem.CacheInstanceList();
            m_AdjustPanel.gameObject.SetActive(false);
            m_btn_delete.gameObject.SetActive(false);
        }
    }
}