using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public partial class ArenaMainUI_Main
    {
        private ArenaMainUI m_ParentUI;
        private LDNetArenaMgr m_ArenaMgr;
        private int m_RefreshCD;
        private Coroutine m_Coroutine;
        private ArenaMainUI_Main_Top3 m_Top3ItemUI;
        private bool m_InValidTime;
        private float m_ContentPosY = 740f;

        public void InitUI(ArenaMainUI parentUI)
        {
            m_ParentUI = parentUI;
            m_ArenaMgr = Global.gApp.gSystemMgr.gArenaMgr;
            m_Help_Button.button.AddListener(OnHelp);
            m_BattleLog_Btn.button.AddListener(OnBattleLog);
            m_ExchangeShop_Btn.button.AddListener(OnShop);
            m_Reward_Btn.button.AddListener(OnReward);
            m_Refresh_Btn.button.AddListener(OnRefresh);
            m_Lineup_Btn.AddListener(OnLineup);

            TryGetData();

            parentUI.AddTimer(1, -1, OnPerSecUpdate);
        }

        public void TryGetData(bool isRefresh = false)
        {
            if (m_ArenaMgr.SendFighterListGetReq())
            {
                if (!isRefresh)
                {
                    m_RootNode.gameObject.SetActive(false);
                }
            }
            else
            {
                RefreshUI();
            }
        }

        public void RefreshUI()
        {
            m_Power.text.SetText(string.Empty);
            m_RootNode.gameObject.SetActive(true);
            m_InValidTime = m_ArenaMgr.CheckValidTime();

            RefreshList();
            RefreshMyRank();
            RefreshBtn();
            TryPlayCoinAni();
            TryGuide();
        }

        private void TryGuide()
        {
            Global.gApp.gSystemMgr.gGuideMgr.RemoveGuide(LDGuideType.Arena, 1);
            if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.Arena, m_LineUpBtnNode.rectTransform, 2)) return;

            if (m_BattleObject.mCachedList.Count > 1)
            {
                ArenaMainUI_Main_BattleObject itemUI = m_BattleObject.mCachedList[^1];
                if (itemUI.IsSelf())
                {
                    itemUI = m_BattleObject.mCachedList[^2];
                }

                if (itemUI != null)
                {
                    LDCommonTools.ScrollVeiwVerticalJumpTo(m_RoleList.gameObject, 100);
                    if (Global.gApp.gSystemMgr.gGuideMgr.AddGuide(LDGuideType.Arena, itemUI.GetGuideNode(), 5)) return;    
                }
            }
        }

        private void RefreshList()
        {
            float contentPosY = m_Content.rectTransform.localPosition.y;
            if (contentPosY != 0f)
            {
                m_ContentPosY = contentPosY;
            }
            
            m_Top3.CacheInstanceList();
            m_Top3ItemUI = m_Top3.GetInstance(true);
            m_Top3ItemUI.ResetUI();
            
            m_blank.CacheInstanceList();
            m_blank.GetInstance(true);
            
            m_BattleObject.CacheInstanceList();
            List<LDArenaFighter> list = m_ArenaMgr.GetFighterList();
            for (int i = 0; i < list.Count; i++)
            {
                ArenaMainUI_Main_BattleObject itemUI = m_BattleObject.GetInstance(true);
                itemUI.RefreshUI(i, list[i], m_ParentUI);

                if (list[i].IsMySelf())
                {
                    m_Power.text.SetText(list[i].GetPowerStr());
                }
            }

            LDCommonTools.SetLocalPosY(m_Content.rectTransform, m_ContentPosY);

            if (m_Coroutine != null) StopCoroutine(m_Coroutine);
            m_Coroutine = StartCoroutine(RefreshListImp());
        }

        private IEnumerator RefreshListImp()
        {
            // Top3
            List<LDArenaFighter> top3List = m_ArenaMgr.GetFighterTop3List();
            foreach (LDArenaFighter fighter in top3List)
            {
                m_Top3ItemUI.SetUI(fighter);
                yield return 1;
            }
            
            // 挑战列表
            foreach (ArenaMainUI_Main_BattleObject battleObject in m_BattleObject.mCachedList)
            {
                battleObject.RefreshModel();
                yield return 1;
            }
        }

        private void RefreshMyRank()
        {
            m_MySelf_Num.text.SetText(m_ArenaMgr.Data.Rank);
        }

        private void RefreshBtn()
        {
            long time = DateTimeUtil.GetServerTime() - m_ArenaMgr.FighterListGetTime;
            m_RefreshCD = Mathf.CeilToInt((5000 - time) / 1000f);
            m_Refresh_Dec.gameObject.SetActive(m_RefreshCD <= 0);
            m_Refresh_Dec_Gray.gameObject.SetActive(m_RefreshCD > 0);
            string txt = m_RefreshCD > 0 ? UiTools.Localize(96005, m_RefreshCD) : UiTools.Localize(9504);
            m_Refresh_Btn_Txt.text.SetText(txt);
        }
        
        private void TryPlayCoinAni()
        {
            if (m_ArenaMgr.PlayMainUICoinAni)
            {
                m_ArenaMgr.PlayMainUICoinAni = false;
                m_diaoluo.gameObject.SetActive(false);
                m_diaoluo.gameObject.SetActive(true);
                m_ParentUI.AddTimer(2f, 1, (_, _) => m_diaoluo.gameObject.SetActive(false));
            }
        }

        private void OnLineup()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.ArenaLineupUI);
        }

        private void OnRefresh()
        {
            if (m_RefreshCD > 0)
            {
                return;
            }

            TryGetData(true);
        }

        private void OnBattleLog()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.ArenaBattleLogUI);
        }

        private void OnShop()
        {
            Global.gApp.gSystemMgr.gExchangeShopMgr.OpenExchangeShopUI(LDExchangeShopType.Arena);
        }

        private void OnReward()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.ArenaRankUI);
        }

        private void OnHelp()
        {
            Global.gApp.gUiMgr.OpenUIAsync<CommonExplainUI>(LDUICfg.CommonExplainUI).SetLoadedCall(ui =>
            {
                ui?.RefreshUI(m_ParentUI.ActivityCfg.activityTitle, m_ParentUI.ActivityCfg.ActivityHelp);
            });
        }


        private void OnPerSecUpdate(float arg1, bool arg2)
        {
            if (m_RefreshCD > 0)
            {
                RefreshBtn();
            }

            if (m_ArenaMgr.CheckValidTime() != m_InValidTime)
            {
                RefreshUI();
            }
        }
    }
}