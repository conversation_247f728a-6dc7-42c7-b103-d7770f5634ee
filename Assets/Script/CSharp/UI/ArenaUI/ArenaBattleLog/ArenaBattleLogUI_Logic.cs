using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class ArenaBattleLogUI
    {
        private LDNetArenaMgr m_ArenaMgr;
        protected override void OnInitImp()
        {
            m_ArenaMgr = Global.gApp.gSystemMgr.gArenaMgr;
            m_btn_close.button.AddListener(TouchClose);
            
            m_BattleLog.CacheInstanceList();
            GetData();
        }

        public override void OnFreshUI()
        {
            RefreshUI();
        }

        private void GetData()
        {
            m_ArenaMgr.SendBattleLogReq();
        }

        private void RefreshUI()
        {
            m_BattleLog.CacheInstanceList();
            List<LDArenaBattleRecord> logs = m_ArenaMgr.GetBattleRecords();
            for (int i = logs.Count - 1; i >= 0; i--)
            {
                LDArenaBattleRecord t = logs[i];
                ArenaBattleLogUI_BattleLog itemUI = m_BattleLog.GetInstance(true);
                itemUI.RefreshUI(t);
            }

            m_Empty.gameObject.SetActive(logs.Count <= 0);
        }

        protected override void OnCloseImp()
        {

        }
    }
}