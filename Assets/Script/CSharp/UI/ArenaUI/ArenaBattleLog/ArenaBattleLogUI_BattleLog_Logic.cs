using System;

namespace LD
{
    public partial class ArenaBattleLogUI_BattleLog
    {
        private LDNetArenaMgr m_ArenaMgr;
        private LDArenaBattleRecord m_LogData;

        public void RefreshUI(LDArenaBattleRecord log)
        {
            m_ArenaMgr = Global.gApp.gSystemMgr.gArenaMgr;
            m_LogData = log;

            m_View_Btn.button.AddListener(OnView);

            RefreshInfo();
        }

        private void RefreshInfo()
        {
            m_Win_BG.gameObject.SetActive(false);
            m_Fail_BG.gameObject.SetActive(false);
            m_ArrowUp.gameObject.SetActive(false);
            m_ArrowDown.gameObject.SetActive(false);
            m_Rank_Txt.text.SetText(string.Empty);
            m_Rank_Num.text.SetText(string.Empty);

            if (m_LogData.Attacker.IsMySelf())
            {
                if (m_LogData.Attacker.FighterId == m_LogData.WinnerId) // 挑战胜利
                {
                    m_Win_BG.gameObject.SetActive(true);
                    m_Result_Txt.text.SetTips(56021);
                    m_Rank_Txt.text.SetTips(56025);
                    m_ArrowUp.gameObject.SetActive(true);
                    m_Rank_Num.text.SetText(Math.Abs(m_LogData.AddRank));
                }
                else
                {
                    m_Fail_BG.gameObject.SetActive(true);
                    m_Result_Txt.text.SetTips(56022);
                }
            }
            else if (m_LogData.Defender.IsMySelf())
            {
                if (m_LogData.Defender.FighterId == m_LogData.WinnerId) // 防守成功
                {
                    m_Win_BG.gameObject.SetActive(true);
                    m_Result_Txt.text.SetTips(56023);
                }
                else
                {
                    m_Fail_BG.gameObject.SetActive(true);
                    m_Result_Txt.text.SetTips(56024);
                    m_Rank_Txt.text.SetTips(56026);
                    m_ArrowDown.gameObject.SetActive(true);
                    m_Rank_Num.text.SetText(Math.Abs(m_LogData.AddRank));
                }
            }
            else
            {
                Global.LogError($"本条对战记录里  没有玩家本人");
                gameObject.SetActive(false);
            }

            LDArenaFighter other = m_LogData.GetOther();
            var head = LDUIPrefabTools.InitOtherRoleHead(m_RoleHead.gameObject, other.HeadInfo);
            head.SetClickCallBack((a) => { });
            m_LevelNode.gameObject.SetActive(other.Level > 0);
            m_Level_Txt.text.SetText(other.Level);
            m_Power.text.SetText(other.GetPowerStr());
            m_PlayerName.text.SetText(other.Name);
        }

        private void OnView()
        {
            m_ArenaMgr.SendPlayerFormationGetReq(m_LogData.GetOther());
        }
    }
}