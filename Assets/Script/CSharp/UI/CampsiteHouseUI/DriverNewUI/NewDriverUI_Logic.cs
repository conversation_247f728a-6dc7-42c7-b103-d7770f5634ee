namespace LD
{
    public partial class NewDriverUI
    {
        private LDRewardUIData m_RewardUIData;

        protected override void OnInitImp()
        {
        }

        protected override void OnCloseImp()
        {
            if (m_RewardUIData != null)
            {
                Global.gApp.gUiMgr.CheckExternRewardDataUI(m_RewardUIData);
            }
        }

        public override void OnFreshUI()
        {
        }

        public void InitView(LDRewardUIData rewardUIData, LDCommonItem itemData)
        {
            m_RewardUIData = rewardUIData;
            var driverCfg = itemData.DriverCfg;
            m_txt_name.text.SetTips(driverCfg.name);
            m_img_rank.gameObject.SetActive(driverCfg.rarity == 2);
           LoadSprite(m_quaBG.image, LDUIResTools.GetDriverQualityBG3(driverCfg.quality));

            // m_img_icon.image.SetSprite(LoadSprite(driverCfg.bigIcon));
            m_img_icon.gameObject.SetActive(false);
            LDUIPrefabTools.GetDriverSpineObj(driverCfg, m_driverSpineParent.rectTransform,this);

            m_info.text.SetText(Global.gApp.gSystemMgr.gCampsiteMgr.GetDriverShowSkillTips(driverCfg.id));
            var comboWeaponItem = new LDCommonItem(driverCfg.ComboWeapon);
            LoadSprite(m_skill_icon.image, comboWeaponItem.Icon);
        }

        public void InitView(LDCommonItem itemData)
        {
            m_RewardUIData = null;
            var driverCfg = itemData.DriverCfg;
            m_txt_name.text.SetTips(driverCfg.name);
            m_img_rank.gameObject.SetActive(driverCfg.rarity == 2);
            LoadSprite(m_quaBG.image, LDUIResTools.GetDriverQualityBG3(driverCfg.quality));

            // m_img_icon.image.SetSprite(LoadSprite(driverCfg.bigIcon));
            m_img_icon.gameObject.SetActive(false);
            LDUIPrefabTools.GetDriverSpineObj(driverCfg, m_driverSpineParent.rectTransform,this);

            m_info.text.SetText(Global.gApp.gSystemMgr.gCampsiteMgr.GetDriverShowSkillTips(driverCfg.id));
            var comboWeaponItem = new LDCommonItem(driverCfg.ComboWeapon);
            LoadSprite(m_skill_icon.image, comboWeaponItem.Icon);
        }
    }
}