using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    [RequireComponent(typeof(LayoutElement))]
    public class LDAutoFixedTextUI: MonoBehaviour
    {
        private void Start()
        {
            LayoutElement layoutElement = GetComponent<LayoutElement>();
            layoutElement.enabled = false;
            LayoutRebuilder.ForceRebuildLayoutImmediate(transform as RectTransform);
            if (layoutElement != null)
            {
                RectTransform rect = gameObject.GetComponent<RectTransform>();
                float maxWidth = rect.sizeDelta.x;
                if (maxWidth > layoutElement.preferredWidth)
                {
                    layoutElement.enabled = true;
                }
                else
                {
                    layoutElement.enabled = false;
                }
            }
        }
    }
}
