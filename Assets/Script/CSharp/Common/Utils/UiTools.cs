using DG.Tweening;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEngine.Experimental.Rendering;
using UnityEngine.UI;

namespace LD
{
    public class UiTools
    {
        private static string[] m_Unit = new string[] { "k", "m", "b", "t", "aa", "bb", "cc", "dd", "ee", "ff", "gg" };

        public static Vector2 WorldToRectPos(GameObject node, Vector3 worldPos, RectTransform parentRectTsf = null)
        {
            Vector2 screenPoint = Global.gApp.CurFightScene.BattleCamera.MainCameraCmpt.WorldToScreenPoint(worldPos);
            if (parentRectTsf == null)
            {
                Canvas parentCanvas = node.GetComponentInParent<Canvas>();
                parentRectTsf = parentCanvas.GetComponent<RectTransform>();
            }

            Vector2 areaPos;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(parentRectTsf, screenPoint,
                Global.gApp.gUICameraCmpt, out areaPos);
            return areaPos;
        }

        public static Vector3 UINodeWorldPos(RectTransform node)
        {
            Vector2 screenPosition = RectTransformUtility.WorldToScreenPoint(Global.gApp.gUICameraCmpt, node.position);

            Vector3 worldPosition =
                Global.gApp.CurFightScene.BattleCamera.MainCameraCmpt.ScreenToWorldPoint(screenPosition);
            return worldPosition;
        }

        public static Vector2 WorldToScreen(Vector3 pos)
        {
            Vector2 screenPoint = Global.gApp.CurFightScene.BattleCamera.MainCameraCmpt.WorldToScreenPoint(pos);
            return screenPoint;
        }

        public static Vector2 WorldToLocalPointInRectangle(Vector3 worldPoint, RectTransform targetRt)
        {
            Vector2 screenPoint = RectTransformUtility.WorldToScreenPoint(Global.gApp.gUICameraCmpt, worldPoint);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(targetRt, screenPoint, Global.gApp.gUICameraCmpt,
                out Vector2 localPoint);
            return localPoint;
        }

        public static string FormateMoneyByItemId(int itemId)
        {
            long itemCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(itemId);
            return FormateMoney(itemCount);
        }

        public static string FormateMoneyRechargeCoin()
        {
            long itemCount = Global.gApp.gSystemMgr.gExchangeShopMgr.Data.TimeLimitedShopCoinCount;
            return FormateMoney(itemCount);
        }

        public static string FormatMoneyActivityCoin(int activityId, int itemId)
        {
            long itemCount = Global.gApp.gSystemMgr.gActivityShopMgr.GetActivityCoin(activityId, itemId);
            return FormateMoney(itemCount);
        }

        public static string FormatMoneyCircleActivityCoin(int activityId, int itemId)
        {
            long itemCount = Global.gApp.gSystemMgr.gCircleActivityMgr.GetActivityCoin(activityId, itemId);
            return FormateMoney(itemCount);
        }

        public static string FormatMoneyDeepExplore(int itemId)
        {
            int activityId =
                Global.gApp.gSystemMgr.gCircleActivityMgr.GetSeasonActivityId(LDCircleActivitySubUIType.deepExplore);
            if (Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(activityId) is LDDeepExploreActivityInfo
                activityInfo)
            {
                if (activityInfo.activityCoin.TryGetValue(itemId, out int num))
                {
                    return FormateMoney(num);
                }
            }

            return FormateMoney(0);
        }

        public static string FormateMoney(double cur, double moneyDst)
        {
            return FormateMoney(cur) + "/" + FormateMoney(moneyDst);
        }

        public static string FormateMoney(string moneyStr)
        {
            double money = LDParseTools.DoubleParse(moneyStr);
            return FormateMoney(money);
        }

        public static string FormateMoney(double money, bool RmRemainder = false)
        {
            if (money < 10000d)
            {
                money = EZMath.IDDoubleToInt(Math.Ceiling(money - 0.01d));
                return money.ToString("f0");
            }

            double n = Math.Log(money, 10);
            int ni = (int)(n);
            int index = ni / 3;
            int remainder = ni % 3;
            string result;
            if (index > m_Unit.Length)
            {
                result = ((money / Math.Pow(10, index)).ToString("f0") + m_Unit[m_Unit.Length - 1]);
                return result;
            }

            double tmp = Math.Pow(10, index * 3);
            double showVal = money / tmp;
            if (showVal >= 10)
            {
                if (RmRemainder)
                {
                    result = RemoveRemainder(showVal.ToString(), (3 - remainder)) + m_Unit[index - 1];
                }
                else
                {
                    result = ((showVal).ToString("f" + (3 - remainder)) + m_Unit[index - 1]);
                }
            }
            else
            {
                if ((int)(showVal * 1000) % 10 == 0)
                {
                    if (RmRemainder)
                    {
                        result = RemoveRemainder(showVal.ToString(), (2 - remainder)) + m_Unit[index - 1];
                    }
                    else
                    {
                        result = ((showVal).ToString("f" + (2 - remainder)) + m_Unit[index - 1]);
                    }
                }
                else
                {
                    if (RmRemainder)
                    {
                        result = RemoveRemainder(showVal.ToString(), (3 - remainder)) + m_Unit[index - 1];
                    }
                    else
                    {
                        result = ((showVal).ToString("f" + (3 - remainder)) + m_Unit[index - 1]);
                    }
                }
            }

            return result;
        }

        public static string RemoveRemainder(string num, int needCount)
        {
            int dotIndex = num.IndexOf('.');
            int lastCount = 0;
            if (num.Length > dotIndex)
            {
                lastCount = num.Length - dotIndex - 1;
            }

            if (lastCount <= needCount)
            {
                needCount = lastCount;
            }

            int allCount = dotIndex + 1 + needCount;
            if (allCount > num.Length)
            {
                allCount = num.Length;
            }

            return num.Substring(0, allCount);
        }

        public static string Localize(int tipsId, params object[] args)
        {
            try
            {
                if (tipsId <= 0) return String.Empty;
                string tipsContent = Global.gApp.gGameData.GetTipsInCurLanguage(tipsId);
                if (args == null || args.Length == 0)
                {
                    return ApplyBidiAlgorithm(tipsContent);;
                }

                return ApplyBidiAlgorithm(string.Format(tipsContent, args));
            }
            catch (Exception)
            {
                Debug.LogError("tipsID + args error" + tipsId);
                return string.Empty;
            }
        }


        private static bool IsRTL(char c)
        {
            // 检查字符是否属于RTL字符集
            return (c >= 0x0591 && c <= 0x07FF) || (char.IsPunctuation(c) || char.IsSymbol(c)) ; // 包含希伯来语和阿拉伯语字符
        }

        private class LDStringClas
        {
            public string ColorStr = "<color=#{0}>{1}</color>";
            public List<string> Segments = new List<string>();
            public string Content;
            public string ColorVal;
            public string GetString()
            {
                Segments.Reverse();
                if (!string.IsNullOrEmpty(ColorVal))
                {
                    return string.Format(ColorStr, ColorVal, string.Join("", Segments));
                }
                else
                {
                    return string.Join("", Segments);
                }
            }
        }

        private static string ApplyBidiAlgorithm(string input)
        {
            // return ArabicHtmlFormatter.FormatHtml(input);
            // return RTLSupport.GetRTLText(input);
            // return ArabicSupport.ArabicFixer.Fix(input);
            return input;
            
            // string input = text;
            // string pattern = @"<color=#([A-Fa-f0-9]{6,8})>(.*?)";
            // string pattern2 = @"<color=#{0}>{1}</color>";
            // text = "<color=#FF0000>Bruto</color> adcd <color=#FFFDFF>Brdfdsuto</color>";
            List<LDStringClas> stringListlist = new List<LDStringClas>();
            string RealFinalText = string.Empty;
            string[] textArray = input.Split("\n");
            int index = 0;
            foreach (string text in textArray)
            {
                stringListlist.Clear();
                if (textArray.Length > 1 && index > 0)
                {
                   RealFinalText += "\n";
                }

                index++;

                string pattern = @"(<color=[^>]*>.*?</color>|[^<]+)";
                MatchCollection matches = Regex.Matches(text, pattern);

                foreach (Match OutMatch in matches)
                {
                    string pattern2 = @"<color=#(?<color>[A-Fa-f0-9]{6})>(?<text>.*?)</color>";
                    Match match = Regex.Match(OutMatch.Value, pattern2);
                    LDStringClas stringClas = new LDStringClas();
                    stringListlist.Add(stringClas);
                    if (match.Success)
                    {
                        string color = match.Groups["color"].Value; // 提取颜色值
                        string text2 = match.Groups["text"].Value; // 提取文本
                        stringClas.Content = text2;
                        stringClas.ColorVal = color;
                    }
                    else
                    {
                        stringClas.Content = OutMatch.Value;
                    }
                }

                foreach (LDStringClas stringClas in stringListlist)
                {
                    string currentSegment = string.Empty;
                    bool isInRTL = false;
                    foreach (char c in stringClas.Content)
                    {
                        if (IsRTL(c) != isInRTL)
                        {
                            isInRTL = IsRTL(c);
                            // 如果是数字或符号，直接添加到当前段
                            if (currentSegment.Length > 0)
                            {
                                stringClas.Segments.Add(currentSegment);
                                currentSegment = string.Empty;
                            }

                            currentSegment += c;
                        }
                        else
                        {
                            currentSegment += c;
                        }
                    }

                    // if (stringClas.Segments.Count > 0)
                    {
                        stringClas.Segments.Add(currentSegment);
                    }
                }

                foreach (LDStringClas stringClas in stringListlist)
                {
                    // 反转所有的RTL段
                    for (int i = 0; i < stringClas.Segments.Count; i++)
                    {
                        if (IsRTL(stringClas.Segments[i][0]))
                        {
                            stringClas.Segments[i] = ReverseString(stringClas.Segments[i]);
                        }
                    }
                }

                stringListlist.Reverse();
                String finalText = string.Empty;
                foreach (LDStringClas stringClas in stringListlist)
                {
                    finalText += stringClas.GetString();
                }

                RealFinalText += finalText;
            }

            // 生成最终文本
            return RealFinalText;
        }

        private static string ReverseString(string str)
        {
            char[] charArray = str.ToCharArray();
            Array.Reverse(charArray);
            return new string(charArray);
        }

        

        //public static string GetLanguage()
        //{
        //    string lan = MachineLanguage();
        //    string[] pms = lan.Split('-');
        //    if (pms[0] != null && !pms[0].Equals(GameConstVal.EmepyStr))
        //    {
        //        return pms[0];
        //    }
        //    else
        //    {
        //        return "en";
        //    }
        //}


        //本机语言
        public static string MachineLanguage()
        {
            switch (Application.systemLanguage)
            {
                case SystemLanguage.Afrikaans:
                    return "af";
                case SystemLanguage.Arabic:
                    return "ar";
                case SystemLanguage.Basque:
                    return "eu";
                case SystemLanguage.Belarusian:
                    return "be";
                case SystemLanguage.Bulgarian:
                    return "bg";
                case SystemLanguage.Catalan:
                    return "ca";
                case SystemLanguage.Chinese:
                    return "zh";
                case SystemLanguage.Czech:
                    return "cs";
                case SystemLanguage.Danish:
                    return "da";
                case SystemLanguage.Dutch:
                    return "nl";
                case SystemLanguage.English:
                    return "en";
                case SystemLanguage.Estonian:
                    return "et";
                case SystemLanguage.Faroese:
                    return "fo";
                case SystemLanguage.Finnish:
                    return "fu";
                case SystemLanguage.French:
                    return "fr";
                case SystemLanguage.German:
                    return "de";
                case SystemLanguage.Greek:
                    return "el";
                case SystemLanguage.Hebrew:
                    return "he";
                case SystemLanguage.Icelandic:
                    return "is";
                case SystemLanguage.Indonesian:
                    return "ina";
                case SystemLanguage.Italian:
                    return "it";
                case SystemLanguage.Japanese:
                    return "ja";
                case SystemLanguage.Korean:
                    return "ko";
                case SystemLanguage.Latvian:
                    return "lv";
                case SystemLanguage.Lithuanian:
                    return "lt";
                case SystemLanguage.Norwegian:
                    return "nn";
                case SystemLanguage.Polish:
                    return "pl";
                case SystemLanguage.Portuguese:
                    return "pt";
                case SystemLanguage.Romanian:
                    return "ro";
                case SystemLanguage.Russian:
                    return "ru";
                case SystemLanguage.SerboCroatian:
                    return "sr";
                case SystemLanguage.Slovak:
                    return "sk";
                case SystemLanguage.Slovenian:
                    return "sl";
                case SystemLanguage.Spanish:
                    return "es";
                case SystemLanguage.Swedish:
                    return "sv";
                case SystemLanguage.Thai:
                    return "th";
                case SystemLanguage.Turkish:
                    return "tr";
                case SystemLanguage.Ukrainian:
                    return "uk";
                case SystemLanguage.Vietnamese:
                    return "vi";
                case SystemLanguage.ChineseSimplified:
                    return "zh-CN";
                case SystemLanguage.ChineseTraditional:
                    return "zh-TW";
                case SystemLanguage.Hungarian:
                    return "hu";
                case SystemLanguage.Unknown:
                    return "unknown";
            }

            ;
            return "";
        }

        public static void AdapterMaskWithTopMask(GameObject targetNode)
        {
            Mask mask = targetNode.GetComponentInParent<Mask>(true);
            while (mask != null && mask.transform.parent != null && mask.transform.parent.GetComponentInParent<Mask>(true) != null)
            {
                mask = mask.transform.parent.GetComponentInParent<Mask>();
            }

            if (mask != null)
            {
                AdapterMask(mask.rectTransform, mask.gameObject);
            }
            else
            {
                RectMask2D mask2d = targetNode.GetComponentInParent<RectMask2D>(true);
                while (mask2d.transform.parent != null && mask2d.transform.parent.GetComponentInParent<RectMask2D>(true) != null)
                {
                    mask2d = mask2d.transform.parent.GetComponentInParent<RectMask2D>(true);
                }

                if (mask2d != null)
                {
                    AdapterMask(mask2d.rectTransform, mask2d.gameObject);
                }
            }
        }

        public static RenderTexture CreatureTexture(int scale, bool fromDIY = false)
        {
            RenderTextureDescriptor renderTextureDes;
            if (fromDIY)
            {
                renderTextureDes = new RenderTextureDescriptor((int)(GameAdapterUtils.RealWidth / scale), (int)(GameAdapterUtils.RealHeight / scale));
            }
            else
            {
                renderTextureDes = new RenderTextureDescriptor((int)(GameAdapterUtils.DesignWidth / scale), (int)(GameAdapterUtils.DesignHeight / scale));
            }

            renderTextureDes.dimension = UnityEngine.Rendering.TextureDimension.Tex2D;
            renderTextureDes.depthStencilFormat = UnityEngine.Experimental.Rendering.GraphicsFormat.None;
            renderTextureDes.useMipMap = false;
            renderTextureDes.useDynamicScale = false;
            renderTextureDes.enableRandomWrite = false;
            renderTextureDes.sRGB = true;
            RenderTexture renderTex = new RenderTexture(renderTextureDes);
            renderTex.stencilFormat = UnityEngine.Experimental.Rendering.GraphicsFormat.None;
            renderTex.format = RenderTextureFormat.ARGBHalf;
            renderTex.graphicsFormat = GraphicsFormat.R16G16B16A16_SFloat;
            return renderTex;
        }

        public static void AdapterMask(Transform maskNode, GameObject targetNode)
        {
            RectTransform maskRect = null;
            Mask mask = maskNode.gameObject.GetComponentInParent<Mask>(true);
            if (mask != null)
            {
                maskRect = mask.rectTransform;
            }
            else
            {
                RectMask2D mask2d = maskNode.gameObject.GetComponentInParent<RectMask2D>();
                if (mask2d != null)
                {
                    maskRect = mask2d.rectTransform;
                }
            }

            if (maskRect != null)
            {
                Vector3[] corners = new Vector3[4];
                maskRect.GetWorldCorners(corners);
                float minX = corners[0].x;
                float minY = corners[0].y;
                float maxX = corners[2].x;
                float maxY = corners[2].y;
                Renderer[] allRenders = targetNode.GetComponentsInChildren<Renderer>(true);
                foreach (Renderer render in allRenders)
                {
                    foreach (Material material in render.materials)
                    {
                        material.SetFloat("_MinX", minX);
                        material.SetFloat("_MinY", minY);
                        material.SetFloat("_MaxX", maxX);
                        material.SetFloat("_MaxY", maxY);
                    }
                }
            }
        }

        public static Tweener TweenFromTo(long start, long end, float duration, Action<long> onUpdate)
        {
            Tweener t = DOTween.To(() => start, x => start = x, end, duration);
            t.OnUpdate(() => onUpdate(start));
            return t;
        }

        public static Tweener TweenFromTo(float start, float end, float duration, Action<float> onUpdate)
        {
            Tweener t = DOTween.To(() => start, x => start = x, end, duration).SetEase(Ease.Linear);
            t.OnUpdate(() => onUpdate(start));
            return t;
        }

        // 判断是否需要打开快速购买钻石界面
        public static bool TryOpenBuyDiamondUI(long needCount)
        {
            long curCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(LDSpecialItemId.Diamond);
            if (needCount > curCount)
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.QuickBuyDiamondUI);
                return true;
            }

            return false;
        }

        // 判断是否需要打开快速购买金币界面
        public static bool TryOpenBuyGoldUI(long needCount)
        {
            long curCount = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(LDSpecialItemId.Gold);
            if (needCount > curCount)
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.QuickBuyGoldUI);
                return true;
            }

            return false;
        }

        /// <summary>
        /// 设置物体layer
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="layer"></param>
        public static void SetGameObjectLayer(GameObject obj, int layer)
        {
            var children = obj.GetComponentsInChildren<Transform>();
            foreach (Transform child in children)
            {
                child.gameObject.layer = layer;
            }
        }

        /// <summary>
        /// 修改UI面板的特效层级
        /// </summary>
        /// <param name="uiGameObject"></param>
        public static void RefreshParticleSystemOrder(GameObject uiGameObject)
        {
            var canvases = uiGameObject.GetComponentsInChildren<Canvas>();
            if (canvases != null)
            {
                foreach (Canvas canvas in canvases)
                {
                    ParticleSystem[] particleSystems = canvas.gameObject.GetComponentsInChildren<ParticleSystem>();
                    Renderer renderer;
                    foreach (ParticleSystem _ps in particleSystems)
                    {
                        if (_ps.TryGetComponent(out renderer))
                        {
                            int selfOrder = renderer.sortingOrder % 5;
                            renderer.sortingOrder = canvas.sortingOrder + selfOrder;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 设置特效层级
        /// </summary>
        /// <param name="parentUI"></param>
        /// <param name="gameObject"></param>
        public static void RefreshParticalOrder(LDBaseUI parentUI, GameObject gameObject)
        {
            Renderer[] renders = gameObject.GetComponentsInChildren<Renderer>(true);
            parentUI.GetMaxOrder(out _, out int order);
            foreach (Renderer render in renders)
            {
                render.sortingOrder = 3 + order;
            }
        }

        /// <summary>
        /// 屏蔽战力弹出
        /// </summary>
        public static void PreventFightPowerUI()
        {
            Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.ShieldFightPowerUI);
        }

        /// <summary>
        /// 解除阻止战力弹出
        /// </summary>
        public static void CancelPreventFightPowerUI()
        {
            Global.gApp.gUiMgr.CloseUI(LDUICfg.ShieldFightPowerUI);
        }

        /// <summary>
        /// 计算战斗力
        /// </summary>
        /// <param name="attributeItems"></param>
        /// <returns></returns>
        public static double CalculateFightPower(Dictionary<int, LDAttributeItem> attributeItems)
        {
            double power = 0;
            //计算出属性提供战力
            //先计算没有依赖的属性
            Dictionary<int, double> propertyFightPowerMap = new();
            //先计算没有依赖的属性
            foreach (KeyValuePair<int, LDAttributeItem> pair in attributeItems)
            {
                var confAttr = pair.Value.AttrCfg;
                var scoreRelatedAttr = confAttr.scoreRelatedAttr;
                if (scoreRelatedAttr.Length <= 0)
                {
                    double fightPower = pair.Value.GetValue() * confAttr.score;
                    power += fightPower;
                    propertyFightPowerMap.Add(pair.Key, fightPower);
                }
            }

            //有依赖关系的属性
            foreach (KeyValuePair<int, LDAttributeItem> pair in attributeItems)
            {
                var confAttr = pair.Value.AttrCfg;
                var scoreRelatedAttr = confAttr.scoreRelatedAttr;
                if (scoreRelatedAttr.Length > 0)
                {
                    double scroeRelate = 0f;
                    for (int i = 0; i < scoreRelatedAttr.Length; i++)
                    {
                        var relaterPower = propertyFightPowerMap.ContainsKey(scoreRelatedAttr[i]) ? propertyFightPowerMap[scoreRelatedAttr[i]] : 0f;
                        scroeRelate += relaterPower;
                    }

                    double fightPower = pair.Value.GetValue() * confAttr.score * scroeRelate;
                    propertyFightPowerMap.Add(pair.Key, fightPower);
                    power += fightPower;
                }
            }

            return power;
        }

        /// <summary>
        /// 计算战斗力
        /// </summary>
        /// <param name="attributeItems"></param>
        /// <returns></returns>
        public static double CalculateFightPower(Dictionary<int, LDAttrAddition> attributeItems)
        {
            double power = 0;
            //计算出属性提供战力
            //先计算没有依赖的属性
            Dictionary<int, double> propertyFightPowerMap = new();
            //先计算没有依赖的属性
            foreach (KeyValuePair<int, LDAttrAddition> pair in attributeItems)
            {
                var confAttr = pair.Value.Cfg;
                var scoreRelatedAttr = confAttr.scoreRelatedAttr;
                if (scoreRelatedAttr.Length <= 0)
                {
                    double fightPower = pair.Value.Val * confAttr.score;
                    power += fightPower;
                    propertyFightPowerMap.Add(pair.Key, fightPower);
                }
            }

            //有依赖关系的属性
            foreach (KeyValuePair<int, LDAttrAddition> pair in attributeItems)
            {
                var confAttr = pair.Value.Cfg;
                var scoreRelatedAttr = confAttr.scoreRelatedAttr;
                if (scoreRelatedAttr.Length > 0)
                {
                    double scroeRelate = 0f;
                    for (int i = 0; i < scoreRelatedAttr.Length; i++)
                    {
                        var relaterPower = propertyFightPowerMap.ContainsKey(scoreRelatedAttr[i]) ? propertyFightPowerMap[scoreRelatedAttr[i]] : 0f;
                        scroeRelate += relaterPower;
                    }

                    double fightPower = pair.Value.Val * confAttr.score * scroeRelate;
                    propertyFightPowerMap.Add(pair.Key, fightPower);
                    power += fightPower;
                }
            }

            return power;
        }

        /// <summary>
        /// 打印属性
        /// </summary>
        /// <param name="attrs"></param>
        public static void LogAttr(Dictionary<int, LDAttributeItem> attrs)
        {
            System.Text.StringBuilder info = new System.Text.StringBuilder();
            info.AppendLine($"当前战斗属性: ");

            foreach (var item in attrs)
            {
                info.AppendLine(item.Value.ToString());
            }

            LD.Global.Log(info.ToString());
        }

        public static void LogAttr(List<LDEquipAttrAddition> attrs)
        {
            System.Text.StringBuilder info = new System.Text.StringBuilder();
            info.AppendLine($"装备属性: ");

            foreach (LDEquipAttrAddition item in attrs)
            {
                info.AppendLine(item.ToString());
            }

            LD.Global.Log(info.ToString());
        }
    }
}