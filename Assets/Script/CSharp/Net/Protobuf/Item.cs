// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: C2SProto/Item.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace LD.Protocol {

  /// <summary>Holder for reflection information generated from C2SProto/Item.proto</summary>
  public static partial class ItemReflection {

    #region Descriptor
    /// <summary>File descriptor for C2SProto/Item.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ItemReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChNDMlNQcm90by9JdGVtLnByb3RvInsKCEl0ZW1JbmZvEgoKAmlkGAEgASgD",
            "EgwKBHR5cGUYAiABKAkSDgoGY2ZnX2lkGAMgASgFEg0KBWNvdW50GAQgASgD",
            "Eg0KBWV4dHJhGAUgASgFEhAKCHNvdXJjZUlkGAYgASgFEhUKDXNvdXJjZVF1",
            "YWxpdHkYByABKAUiUgoOUmV3YXJkUmVzcG9uc2USHQoKaXRlbV9pbmZvcxgB",
            "IAMoCzIJLkl0ZW1JbmZvEg4KBnNvdXJjZRgCIAEoCRIRCglzaG93X3R5cGUY",
            "AyABKAUiMAoPQ29uc3VtZVJlc3BvbnNlEh0KCml0ZW1faW5mb3MYASADKAsy",
            "CS5JdGVtSW5mbyJjCglQcm9wc0luZm8SEAoIcHJvcHNfaWQYASABKAMSDgoG",
            "Y2ZnX2lkGAIgASgFEg0KBWNvdW50GAMgASgDEhIKCnVzZWRfdGltZXMYBCAB",
            "KAUSEQoJZXhwaXJlX2F0GAUgASgDIjQKEVByb3BzU3luY1Jlc3BvbnNlEh8K",
            "C3Byb3BzX2luZm9zGAEgAygLMgouUHJvcHNJbmZvIjwKFUl0ZW1EaWFtb25k",
            "QnV5UmVxdWVzdBIRCglpdGVtQ2ZnSWQYASABKAUSEAoIYnV5Q291bnQYAiAB",
            "KAUiGAoWSXRlbURpYW1vbmRCdXlSZXNwb25zZSJCCg5JdGVtVXNlUmVxdWVz",
            "dBIOCgZpdGVtSWQYASABKAMSEAoIdXNlQ291bnQYAiABKAUSDgoGcGFyYW1z",
            "GAMgAygJIhEKD0l0ZW1Vc2VSZXNwb25zZSI6ChJJdGVtQ29tcG9zZVJlcXVl",
            "c3QSDgoGaXRlbUlkGAEgASgDEhQKDGNvbXBvc2VUaW1lcxgCIAEoBSIVChNJ",
            "dGVtQ29tcG9zZVJlc3BvbnNlIioKF0l0ZW1FeHBpcmVSZWdhaW5SZXF1ZXN0",
            "Eg8KB2l0ZW1JZHMYASADKAMiGgoYSXRlbUV4cGlyZVJlZ2FpblJlc3BvbnNl",
            "IiUKFEl0ZW1FbmVyZ3lCdXlSZXF1ZXN0Eg0KBXRpbWVzGAEgASgFIhcKFUl0",
            "ZW1FbmVyZ3lCdXlSZXNwb25zZSIrChpJdGVtRW5lcmd5U3RvcmdlQnV5UmVx",
            "dWVzdBINCgV0aW1lcxgBIAEoBSIdChtJdGVtRW5lcmd5U3RvcmdlQnV5UmVz",
            "cG9uc2UiEwoRRW5lcmd5SW5mb1JlcXVlc3QiNQoSRW5lcmd5SW5mb1Jlc3Bv",
            "bnNlEh8KCmVuZXJneUluZm8YASABKAsyCy5FbmVyZ3lJbmZvInQKCkVuZXJn",
            "eUluZm8SGAoQcmVjb3ZlclN0YXJ0VGltZRgBIAEoAxIVCg10b2RheUJ1eVRp",
            "bWVzGAIgASgFEhoKEnJlbWFpblN0b3JhZ2VUaW1lcxgDIAEoBRIZChF0b2Rh",
            "eVdhdGNoQWRUaW1lcxgEIAEoBUIjChNjb20uZ29sZGVuLnByb3RvY29sqgIL",
            "TEQuUHJvdG9jb2xiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemInfo), global::LD.Protocol.ItemInfo.Parser, new[]{ "Id", "Type", "CfgId", "Count", "Extra", "SourceId", "SourceQuality" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.RewardResponse), global::LD.Protocol.RewardResponse.Parser, new[]{ "ItemInfos", "Source", "ShowType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ConsumeResponse), global::LD.Protocol.ConsumeResponse.Parser, new[]{ "ItemInfos" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PropsInfo), global::LD.Protocol.PropsInfo.Parser, new[]{ "PropsId", "CfgId", "Count", "UsedTimes", "ExpireAt" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.PropsSyncResponse), global::LD.Protocol.PropsSyncResponse.Parser, new[]{ "PropsInfos" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemDiamondBuyRequest), global::LD.Protocol.ItemDiamondBuyRequest.Parser, new[]{ "ItemCfgId", "BuyCount" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemDiamondBuyResponse), global::LD.Protocol.ItemDiamondBuyResponse.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemUseRequest), global::LD.Protocol.ItemUseRequest.Parser, new[]{ "ItemId", "UseCount", "Params" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemUseResponse), global::LD.Protocol.ItemUseResponse.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemComposeRequest), global::LD.Protocol.ItemComposeRequest.Parser, new[]{ "ItemId", "ComposeTimes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemComposeResponse), global::LD.Protocol.ItemComposeResponse.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemExpireRegainRequest), global::LD.Protocol.ItemExpireRegainRequest.Parser, new[]{ "ItemIds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemExpireRegainResponse), global::LD.Protocol.ItemExpireRegainResponse.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemEnergyBuyRequest), global::LD.Protocol.ItemEnergyBuyRequest.Parser, new[]{ "Times" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemEnergyBuyResponse), global::LD.Protocol.ItemEnergyBuyResponse.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemEnergyStorgeBuyRequest), global::LD.Protocol.ItemEnergyStorgeBuyRequest.Parser, new[]{ "Times" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.ItemEnergyStorgeBuyResponse), global::LD.Protocol.ItemEnergyStorgeBuyResponse.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.EnergyInfoRequest), global::LD.Protocol.EnergyInfoRequest.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.EnergyInfoResponse), global::LD.Protocol.EnergyInfoResponse.Parser, new[]{ "EnergyInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::LD.Protocol.EnergyInfo), global::LD.Protocol.EnergyInfo.Parser, new[]{ "RecoverStartTime", "TodayBuyTimes", "RemainStorageTimes", "TodayWatchAdTimes" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  /// 物品信息封装
  /// </summary>
  public sealed partial class ItemInfo : pb::IMessage<ItemInfo> {
    private static readonly pb::MessageParser<ItemInfo> _parser = new pb::MessageParser<ItemInfo>(() => new ItemInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemInfo(ItemInfo other) : this() {
      id_ = other.id_;
      type_ = other.type_;
      cfgId_ = other.cfgId_;
      count_ = other.count_;
      extra_ = other.extra_;
      sourceId_ = other.sourceId_;
      sourceQuality_ = other.sourceQuality_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemInfo Clone() {
      return new ItemInfo(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private long id_;
    /// <summary>
    /// 物品的唯一ID，纯展示协议里该值为空
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 2;
    private string type_ = "";
    /// <summary>
    /// 物品类型
    /// 	item: 道具
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Type {
      get { return type_; }
      set {
        type_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cfg_id" field.</summary>
    public const int CfgIdFieldNumber = 3;
    private int cfgId_;
    /// <summary>
    /// 物品模板ID，对于type为"item"的类型，对应的sub_type为item表里的ID
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CfgId {
      get { return cfgId_; }
      set {
        cfgId_ = value;
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int CountFieldNumber = 4;
    private long count_;
    /// <summary>
    /// 数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    /// <summary>Field number for the "extra" field.</summary>
    public const int ExtraFieldNumber = 5;
    private int extra_;
    /// <summary>
    /// 额外参数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Extra {
      get { return extra_; }
      set {
        extra_ = value;
      }
    }

    /// <summary>Field number for the "sourceId" field.</summary>
    public const int SourceIdFieldNumber = 6;
    private int sourceId_;
    /// <summary>
    ///来源 是谁转化来的
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SourceId {
      get { return sourceId_; }
      set {
        sourceId_ = value;
      }
    }

    /// <summary>Field number for the "sourceQuality" field.</summary>
    public const int SourceQualityFieldNumber = 7;
    private int sourceQuality_;
    /// <summary>
    ///来源 是谁转化来的
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int SourceQuality {
      get { return sourceQuality_; }
      set {
        sourceQuality_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Type != other.Type) return false;
      if (CfgId != other.CfgId) return false;
      if (Count != other.Count) return false;
      if (Extra != other.Extra) return false;
      if (SourceId != other.SourceId) return false;
      if (SourceQuality != other.SourceQuality) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0L) hash ^= Id.GetHashCode();
      if (Type.Length != 0) hash ^= Type.GetHashCode();
      if (CfgId != 0) hash ^= CfgId.GetHashCode();
      if (Count != 0L) hash ^= Count.GetHashCode();
      if (Extra != 0) hash ^= Extra.GetHashCode();
      if (SourceId != 0) hash ^= SourceId.GetHashCode();
      if (SourceQuality != 0) hash ^= SourceQuality.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (Type.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Type);
      }
      if (CfgId != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(CfgId);
      }
      if (Count != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Count);
      }
      if (Extra != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(Extra);
      }
      if (SourceId != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(SourceId);
      }
      if (SourceQuality != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(SourceQuality);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Id);
      }
      if (Type.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Type);
      }
      if (CfgId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(CfgId);
      }
      if (Count != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Count);
      }
      if (Extra != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Extra);
      }
      if (SourceId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SourceId);
      }
      if (SourceQuality != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(SourceQuality);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemInfo other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0L) {
        Id = other.Id;
      }
      if (other.Type.Length != 0) {
        Type = other.Type;
      }
      if (other.CfgId != 0) {
        CfgId = other.CfgId;
      }
      if (other.Count != 0L) {
        Count = other.Count;
      }
      if (other.Extra != 0) {
        Extra = other.Extra;
      }
      if (other.SourceId != 0) {
        SourceId = other.SourceId;
      }
      if (other.SourceQuality != 0) {
        SourceQuality = other.SourceQuality;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 18: {
            Type = input.ReadString();
            break;
          }
          case 24: {
            CfgId = input.ReadInt32();
            break;
          }
          case 32: {
            Count = input.ReadInt64();
            break;
          }
          case 40: {
            Extra = input.ReadInt32();
            break;
          }
          case 48: {
            SourceId = input.ReadInt32();
            break;
          }
          case 56: {
            SourceQuality = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class RewardResponse : pb::IMessage<RewardResponse> {
    private static readonly pb::MessageParser<RewardResponse> _parser = new pb::MessageParser<RewardResponse>(() => new RewardResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<RewardResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RewardResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RewardResponse(RewardResponse other) : this() {
      itemInfos_ = other.itemInfos_.Clone();
      source_ = other.source_;
      showType_ = other.showType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public RewardResponse Clone() {
      return new RewardResponse(this);
    }

    /// <summary>Field number for the "item_infos" field.</summary>
    public const int ItemInfosFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.ItemInfo> _repeated_itemInfos_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.ItemInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.ItemInfo> itemInfos_ = new pbc::RepeatedField<global::LD.Protocol.ItemInfo>();
    /// <summary>
    /// 奖励的物品信息列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.ItemInfo> ItemInfos {
      get { return itemInfos_; }
    }

    /// <summary>Field number for the "source" field.</summary>
    public const int SourceFieldNumber = 2;
    private string source_ = "";
    /// <summary>
    /// 奖励来源
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public string Source {
      get { return source_; }
      set {
        source_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "show_type" field.</summary>
    public const int ShowTypeFieldNumber = 3;
    private int showType_;
    /// <summary>
    /// 物品展示规则
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int ShowType {
      get { return showType_; }
      set {
        showType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as RewardResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(RewardResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!itemInfos_.Equals(other.itemInfos_)) return false;
      if (Source != other.Source) return false;
      if (ShowType != other.ShowType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= itemInfos_.GetHashCode();
      if (Source.Length != 0) hash ^= Source.GetHashCode();
      if (ShowType != 0) hash ^= ShowType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      itemInfos_.WriteTo(output, _repeated_itemInfos_codec);
      if (Source.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Source);
      }
      if (ShowType != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(ShowType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += itemInfos_.CalculateSize(_repeated_itemInfos_codec);
      if (Source.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Source);
      }
      if (ShowType != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ShowType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(RewardResponse other) {
      if (other == null) {
        return;
      }
      itemInfos_.Add(other.itemInfos_);
      if (other.Source.Length != 0) {
        Source = other.Source;
      }
      if (other.ShowType != 0) {
        ShowType = other.ShowType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            itemInfos_.AddEntriesFrom(input, _repeated_itemInfos_codec);
            break;
          }
          case 18: {
            Source = input.ReadString();
            break;
          }
          case 24: {
            ShowType = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ConsumeResponse : pb::IMessage<ConsumeResponse> {
    private static readonly pb::MessageParser<ConsumeResponse> _parser = new pb::MessageParser<ConsumeResponse>(() => new ConsumeResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ConsumeResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ConsumeResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ConsumeResponse(ConsumeResponse other) : this() {
      itemInfos_ = other.itemInfos_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ConsumeResponse Clone() {
      return new ConsumeResponse(this);
    }

    /// <summary>Field number for the "item_infos" field.</summary>
    public const int ItemInfosFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.ItemInfo> _repeated_itemInfos_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.ItemInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.ItemInfo> itemInfos_ = new pbc::RepeatedField<global::LD.Protocol.ItemInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.ItemInfo> ItemInfos {
      get { return itemInfos_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ConsumeResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ConsumeResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!itemInfos_.Equals(other.itemInfos_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= itemInfos_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      itemInfos_.WriteTo(output, _repeated_itemInfos_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += itemInfos_.CalculateSize(_repeated_itemInfos_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ConsumeResponse other) {
      if (other == null) {
        return;
      }
      itemInfos_.Add(other.itemInfos_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            itemInfos_.AddEntriesFrom(input, _repeated_itemInfos_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class PropsInfo : pb::IMessage<PropsInfo> {
    private static readonly pb::MessageParser<PropsInfo> _parser = new pb::MessageParser<PropsInfo>(() => new PropsInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PropsInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PropsInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PropsInfo(PropsInfo other) : this() {
      propsId_ = other.propsId_;
      cfgId_ = other.cfgId_;
      count_ = other.count_;
      usedTimes_ = other.usedTimes_;
      expireAt_ = other.expireAt_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PropsInfo Clone() {
      return new PropsInfo(this);
    }

    /// <summary>Field number for the "props_id" field.</summary>
    public const int PropsIdFieldNumber = 1;
    private long propsId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long PropsId {
      get { return propsId_; }
      set {
        propsId_ = value;
      }
    }

    /// <summary>Field number for the "cfg_id" field.</summary>
    public const int CfgIdFieldNumber = 2;
    private int cfgId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CfgId {
      get { return cfgId_; }
      set {
        cfgId_ = value;
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int CountFieldNumber = 3;
    private long count_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long Count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    /// <summary>Field number for the "used_times" field.</summary>
    public const int UsedTimesFieldNumber = 4;
    private int usedTimes_;
    /// <summary>
    /// 已经使用的次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int UsedTimes {
      get { return usedTimes_; }
      set {
        usedTimes_ = value;
      }
    }

    /// <summary>Field number for the "expire_at" field.</summary>
    public const int ExpireAtFieldNumber = 5;
    private long expireAt_;
    /// <summary>
    /// 道具过期时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long ExpireAt {
      get { return expireAt_; }
      set {
        expireAt_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PropsInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PropsInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (PropsId != other.PropsId) return false;
      if (CfgId != other.CfgId) return false;
      if (Count != other.Count) return false;
      if (UsedTimes != other.UsedTimes) return false;
      if (ExpireAt != other.ExpireAt) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (PropsId != 0L) hash ^= PropsId.GetHashCode();
      if (CfgId != 0) hash ^= CfgId.GetHashCode();
      if (Count != 0L) hash ^= Count.GetHashCode();
      if (UsedTimes != 0) hash ^= UsedTimes.GetHashCode();
      if (ExpireAt != 0L) hash ^= ExpireAt.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (PropsId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(PropsId);
      }
      if (CfgId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(CfgId);
      }
      if (Count != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Count);
      }
      if (UsedTimes != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(UsedTimes);
      }
      if (ExpireAt != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(ExpireAt);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (PropsId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(PropsId);
      }
      if (CfgId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(CfgId);
      }
      if (Count != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Count);
      }
      if (UsedTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(UsedTimes);
      }
      if (ExpireAt != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ExpireAt);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PropsInfo other) {
      if (other == null) {
        return;
      }
      if (other.PropsId != 0L) {
        PropsId = other.PropsId;
      }
      if (other.CfgId != 0) {
        CfgId = other.CfgId;
      }
      if (other.Count != 0L) {
        Count = other.Count;
      }
      if (other.UsedTimes != 0) {
        UsedTimes = other.UsedTimes;
      }
      if (other.ExpireAt != 0L) {
        ExpireAt = other.ExpireAt;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            PropsId = input.ReadInt64();
            break;
          }
          case 16: {
            CfgId = input.ReadInt32();
            break;
          }
          case 24: {
            Count = input.ReadInt64();
            break;
          }
          case 32: {
            UsedTimes = input.ReadInt32();
            break;
          }
          case 40: {
            ExpireAt = input.ReadInt64();
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 道具同步包
  /// </summary>
  public sealed partial class PropsSyncResponse : pb::IMessage<PropsSyncResponse> {
    private static readonly pb::MessageParser<PropsSyncResponse> _parser = new pb::MessageParser<PropsSyncResponse>(() => new PropsSyncResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<PropsSyncResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PropsSyncResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PropsSyncResponse(PropsSyncResponse other) : this() {
      propsInfos_ = other.propsInfos_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public PropsSyncResponse Clone() {
      return new PropsSyncResponse(this);
    }

    /// <summary>Field number for the "props_infos" field.</summary>
    public const int PropsInfosFieldNumber = 1;
    private static readonly pb::FieldCodec<global::LD.Protocol.PropsInfo> _repeated_propsInfos_codec
        = pb::FieldCodec.ForMessage(10, global::LD.Protocol.PropsInfo.Parser);
    private readonly pbc::RepeatedField<global::LD.Protocol.PropsInfo> propsInfos_ = new pbc::RepeatedField<global::LD.Protocol.PropsInfo>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<global::LD.Protocol.PropsInfo> PropsInfos {
      get { return propsInfos_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as PropsSyncResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(PropsSyncResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!propsInfos_.Equals(other.propsInfos_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= propsInfos_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      propsInfos_.WriteTo(output, _repeated_propsInfos_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += propsInfos_.CalculateSize(_repeated_propsInfos_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(PropsSyncResponse other) {
      if (other == null) {
        return;
      }
      propsInfos_.Add(other.propsInfos_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            propsInfos_.AddEntriesFrom(input, _repeated_propsInfos_codec);
            break;
          }
        }
      }
    }

  }

  /// <summary>
  /// 物品道具直接购买
  /// </summary>
  public sealed partial class ItemDiamondBuyRequest : pb::IMessage<ItemDiamondBuyRequest> {
    private static readonly pb::MessageParser<ItemDiamondBuyRequest> _parser = new pb::MessageParser<ItemDiamondBuyRequest>(() => new ItemDiamondBuyRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemDiamondBuyRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemDiamondBuyRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemDiamondBuyRequest(ItemDiamondBuyRequest other) : this() {
      itemCfgId_ = other.itemCfgId_;
      buyCount_ = other.buyCount_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemDiamondBuyRequest Clone() {
      return new ItemDiamondBuyRequest(this);
    }

    /// <summary>Field number for the "itemCfgId" field.</summary>
    public const int ItemCfgIdFieldNumber = 1;
    private int itemCfgId_;
    /// <summary>
    /// 道具配置id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int ItemCfgId {
      get { return itemCfgId_; }
      set {
        itemCfgId_ = value;
      }
    }

    /// <summary>Field number for the "buyCount" field.</summary>
    public const int BuyCountFieldNumber = 2;
    private int buyCount_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int BuyCount {
      get { return buyCount_; }
      set {
        buyCount_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemDiamondBuyRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemDiamondBuyRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ItemCfgId != other.ItemCfgId) return false;
      if (BuyCount != other.BuyCount) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (ItemCfgId != 0) hash ^= ItemCfgId.GetHashCode();
      if (BuyCount != 0) hash ^= BuyCount.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (ItemCfgId != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(ItemCfgId);
      }
      if (BuyCount != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(BuyCount);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (ItemCfgId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ItemCfgId);
      }
      if (BuyCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(BuyCount);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemDiamondBuyRequest other) {
      if (other == null) {
        return;
      }
      if (other.ItemCfgId != 0) {
        ItemCfgId = other.ItemCfgId;
      }
      if (other.BuyCount != 0) {
        BuyCount = other.BuyCount;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ItemCfgId = input.ReadInt32();
            break;
          }
          case 16: {
            BuyCount = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ItemDiamondBuyResponse : pb::IMessage<ItemDiamondBuyResponse> {
    private static readonly pb::MessageParser<ItemDiamondBuyResponse> _parser = new pb::MessageParser<ItemDiamondBuyResponse>(() => new ItemDiamondBuyResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemDiamondBuyResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemDiamondBuyResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemDiamondBuyResponse(ItemDiamondBuyResponse other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemDiamondBuyResponse Clone() {
      return new ItemDiamondBuyResponse(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemDiamondBuyResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemDiamondBuyResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemDiamondBuyResponse other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    }

  }

  public sealed partial class ItemUseRequest : pb::IMessage<ItemUseRequest> {
    private static readonly pb::MessageParser<ItemUseRequest> _parser = new pb::MessageParser<ItemUseRequest>(() => new ItemUseRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemUseRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemUseRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemUseRequest(ItemUseRequest other) : this() {
      itemId_ = other.itemId_;
      useCount_ = other.useCount_;
      params_ = other.params_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemUseRequest Clone() {
      return new ItemUseRequest(this);
    }

    /// <summary>Field number for the "itemId" field.</summary>
    public const int ItemIdFieldNumber = 1;
    private long itemId_;
    /// <summary>
    /// 物品唯一id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long ItemId {
      get { return itemId_; }
      set {
        itemId_ = value;
      }
    }

    /// <summary>Field number for the "useCount" field.</summary>
    public const int UseCountFieldNumber = 2;
    private int useCount_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int UseCount {
      get { return useCount_; }
      set {
        useCount_ = value;
      }
    }

    /// <summary>Field number for the "params" field.</summary>
    public const int ParamsFieldNumber = 3;
    private static readonly pb::FieldCodec<string> _repeated_params_codec
        = pb::FieldCodec.ForString(26);
    private readonly pbc::RepeatedField<string> params_ = new pbc::RepeatedField<string>();
    /// <summary>
    /// 附带参数 比如自选宝箱需要使用
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<string> Params {
      get { return params_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemUseRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemUseRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ItemId != other.ItemId) return false;
      if (UseCount != other.UseCount) return false;
      if(!params_.Equals(other.params_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (ItemId != 0L) hash ^= ItemId.GetHashCode();
      if (UseCount != 0) hash ^= UseCount.GetHashCode();
      hash ^= params_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (ItemId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(ItemId);
      }
      if (UseCount != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(UseCount);
      }
      params_.WriteTo(output, _repeated_params_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (ItemId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ItemId);
      }
      if (UseCount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(UseCount);
      }
      size += params_.CalculateSize(_repeated_params_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemUseRequest other) {
      if (other == null) {
        return;
      }
      if (other.ItemId != 0L) {
        ItemId = other.ItemId;
      }
      if (other.UseCount != 0) {
        UseCount = other.UseCount;
      }
      params_.Add(other.params_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ItemId = input.ReadInt64();
            break;
          }
          case 16: {
            UseCount = input.ReadInt32();
            break;
          }
          case 26: {
            params_.AddEntriesFrom(input, _repeated_params_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class ItemUseResponse : pb::IMessage<ItemUseResponse> {
    private static readonly pb::MessageParser<ItemUseResponse> _parser = new pb::MessageParser<ItemUseResponse>(() => new ItemUseResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemUseResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemUseResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemUseResponse(ItemUseResponse other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemUseResponse Clone() {
      return new ItemUseResponse(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemUseResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemUseResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemUseResponse other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    }

  }

  public sealed partial class ItemComposeRequest : pb::IMessage<ItemComposeRequest> {
    private static readonly pb::MessageParser<ItemComposeRequest> _parser = new pb::MessageParser<ItemComposeRequest>(() => new ItemComposeRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemComposeRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemComposeRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemComposeRequest(ItemComposeRequest other) : this() {
      itemId_ = other.itemId_;
      composeTimes_ = other.composeTimes_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemComposeRequest Clone() {
      return new ItemComposeRequest(this);
    }

    /// <summary>Field number for the "itemId" field.</summary>
    public const int ItemIdFieldNumber = 1;
    private long itemId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long ItemId {
      get { return itemId_; }
      set {
        itemId_ = value;
      }
    }

    /// <summary>Field number for the "composeTimes" field.</summary>
    public const int ComposeTimesFieldNumber = 2;
    private int composeTimes_;
    /// <summary>
    /// 合成次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int ComposeTimes {
      get { return composeTimes_; }
      set {
        composeTimes_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemComposeRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemComposeRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (ItemId != other.ItemId) return false;
      if (ComposeTimes != other.ComposeTimes) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (ItemId != 0L) hash ^= ItemId.GetHashCode();
      if (ComposeTimes != 0) hash ^= ComposeTimes.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (ItemId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(ItemId);
      }
      if (ComposeTimes != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(ComposeTimes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (ItemId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ItemId);
      }
      if (ComposeTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(ComposeTimes);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemComposeRequest other) {
      if (other == null) {
        return;
      }
      if (other.ItemId != 0L) {
        ItemId = other.ItemId;
      }
      if (other.ComposeTimes != 0) {
        ComposeTimes = other.ComposeTimes;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            ItemId = input.ReadInt64();
            break;
          }
          case 16: {
            ComposeTimes = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ItemComposeResponse : pb::IMessage<ItemComposeResponse> {
    private static readonly pb::MessageParser<ItemComposeResponse> _parser = new pb::MessageParser<ItemComposeResponse>(() => new ItemComposeResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemComposeResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemComposeResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemComposeResponse(ItemComposeResponse other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemComposeResponse Clone() {
      return new ItemComposeResponse(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemComposeResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemComposeResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemComposeResponse other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    }

  }

  public sealed partial class ItemExpireRegainRequest : pb::IMessage<ItemExpireRegainRequest> {
    private static readonly pb::MessageParser<ItemExpireRegainRequest> _parser = new pb::MessageParser<ItemExpireRegainRequest>(() => new ItemExpireRegainRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemExpireRegainRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemExpireRegainRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemExpireRegainRequest(ItemExpireRegainRequest other) : this() {
      itemIds_ = other.itemIds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemExpireRegainRequest Clone() {
      return new ItemExpireRegainRequest(this);
    }

    /// <summary>Field number for the "itemIds" field.</summary>
    public const int ItemIdsFieldNumber = 1;
    private static readonly pb::FieldCodec<long> _repeated_itemIds_codec
        = pb::FieldCodec.ForInt64(10);
    private readonly pbc::RepeatedField<long> itemIds_ = new pbc::RepeatedField<long>();
    /// <summary>
    /// 物品id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public pbc::RepeatedField<long> ItemIds {
      get { return itemIds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemExpireRegainRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemExpireRegainRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!itemIds_.Equals(other.itemIds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= itemIds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      itemIds_.WriteTo(output, _repeated_itemIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      size += itemIds_.CalculateSize(_repeated_itemIds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemExpireRegainRequest other) {
      if (other == null) {
        return;
      }
      itemIds_.Add(other.itemIds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            itemIds_.AddEntriesFrom(input, _repeated_itemIds_codec);
            break;
          }
        }
      }
    }

  }

  public sealed partial class ItemExpireRegainResponse : pb::IMessage<ItemExpireRegainResponse> {
    private static readonly pb::MessageParser<ItemExpireRegainResponse> _parser = new pb::MessageParser<ItemExpireRegainResponse>(() => new ItemExpireRegainResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemExpireRegainResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemExpireRegainResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemExpireRegainResponse(ItemExpireRegainResponse other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemExpireRegainResponse Clone() {
      return new ItemExpireRegainResponse(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemExpireRegainResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemExpireRegainResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemExpireRegainResponse other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    }

  }

  /// <summary>
  /// 体力钻石购买
  /// </summary>
  public sealed partial class ItemEnergyBuyRequest : pb::IMessage<ItemEnergyBuyRequest> {
    private static readonly pb::MessageParser<ItemEnergyBuyRequest> _parser = new pb::MessageParser<ItemEnergyBuyRequest>(() => new ItemEnergyBuyRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemEnergyBuyRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyBuyRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyBuyRequest(ItemEnergyBuyRequest other) : this() {
      times_ = other.times_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyBuyRequest Clone() {
      return new ItemEnergyBuyRequest(this);
    }

    /// <summary>Field number for the "times" field.</summary>
    public const int TimesFieldNumber = 1;
    private int times_;
    /// <summary>
    /// 购买次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Times {
      get { return times_; }
      set {
        times_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemEnergyBuyRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemEnergyBuyRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Times != other.Times) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Times != 0) hash ^= Times.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Times != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Times);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Times != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Times);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemEnergyBuyRequest other) {
      if (other == null) {
        return;
      }
      if (other.Times != 0) {
        Times = other.Times;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Times = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ItemEnergyBuyResponse : pb::IMessage<ItemEnergyBuyResponse> {
    private static readonly pb::MessageParser<ItemEnergyBuyResponse> _parser = new pb::MessageParser<ItemEnergyBuyResponse>(() => new ItemEnergyBuyResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemEnergyBuyResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyBuyResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyBuyResponse(ItemEnergyBuyResponse other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyBuyResponse Clone() {
      return new ItemEnergyBuyResponse(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemEnergyBuyResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemEnergyBuyResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemEnergyBuyResponse other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    }

  }

  public sealed partial class ItemEnergyStorgeBuyRequest : pb::IMessage<ItemEnergyStorgeBuyRequest> {
    private static readonly pb::MessageParser<ItemEnergyStorgeBuyRequest> _parser = new pb::MessageParser<ItemEnergyStorgeBuyRequest>(() => new ItemEnergyStorgeBuyRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemEnergyStorgeBuyRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyStorgeBuyRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyStorgeBuyRequest(ItemEnergyStorgeBuyRequest other) : this() {
      times_ = other.times_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyStorgeBuyRequest Clone() {
      return new ItemEnergyStorgeBuyRequest(this);
    }

    /// <summary>Field number for the "times" field.</summary>
    public const int TimesFieldNumber = 1;
    private int times_;
    /// <summary>
    /// 购买次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int Times {
      get { return times_; }
      set {
        times_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemEnergyStorgeBuyRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemEnergyStorgeBuyRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Times != other.Times) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (Times != 0) hash ^= Times.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (Times != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Times);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (Times != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Times);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemEnergyStorgeBuyRequest other) {
      if (other == null) {
        return;
      }
      if (other.Times != 0) {
        Times = other.Times;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Times = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  public sealed partial class ItemEnergyStorgeBuyResponse : pb::IMessage<ItemEnergyStorgeBuyResponse> {
    private static readonly pb::MessageParser<ItemEnergyStorgeBuyResponse> _parser = new pb::MessageParser<ItemEnergyStorgeBuyResponse>(() => new ItemEnergyStorgeBuyResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<ItemEnergyStorgeBuyResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyStorgeBuyResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyStorgeBuyResponse(ItemEnergyStorgeBuyResponse other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public ItemEnergyStorgeBuyResponse Clone() {
      return new ItemEnergyStorgeBuyResponse(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as ItemEnergyStorgeBuyResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(ItemEnergyStorgeBuyResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(ItemEnergyStorgeBuyResponse other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    }

  }

  public sealed partial class EnergyInfoRequest : pb::IMessage<EnergyInfoRequest> {
    private static readonly pb::MessageParser<EnergyInfoRequest> _parser = new pb::MessageParser<EnergyInfoRequest>(() => new EnergyInfoRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<EnergyInfoRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EnergyInfoRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EnergyInfoRequest(EnergyInfoRequest other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EnergyInfoRequest Clone() {
      return new EnergyInfoRequest(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as EnergyInfoRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(EnergyInfoRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(EnergyInfoRequest other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    }

  }

  public sealed partial class EnergyInfoResponse : pb::IMessage<EnergyInfoResponse> {
    private static readonly pb::MessageParser<EnergyInfoResponse> _parser = new pb::MessageParser<EnergyInfoResponse>(() => new EnergyInfoResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<EnergyInfoResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EnergyInfoResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EnergyInfoResponse(EnergyInfoResponse other) : this() {
      energyInfo_ = other.energyInfo_ != null ? other.energyInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EnergyInfoResponse Clone() {
      return new EnergyInfoResponse(this);
    }

    /// <summary>Field number for the "energyInfo" field.</summary>
    public const int EnergyInfoFieldNumber = 1;
    private global::LD.Protocol.EnergyInfo energyInfo_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public global::LD.Protocol.EnergyInfo EnergyInfo {
      get { return energyInfo_; }
      set {
        energyInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as EnergyInfoResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(EnergyInfoResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(EnergyInfo, other.EnergyInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (energyInfo_ != null) hash ^= EnergyInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (energyInfo_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(EnergyInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (energyInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(EnergyInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(EnergyInfoResponse other) {
      if (other == null) {
        return;
      }
      if (other.energyInfo_ != null) {
        if (energyInfo_ == null) {
          EnergyInfo = new global::LD.Protocol.EnergyInfo();
        }
        EnergyInfo.MergeFrom(other.EnergyInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (energyInfo_ == null) {
              EnergyInfo = new global::LD.Protocol.EnergyInfo();
            }
            input.ReadMessage(EnergyInfo);
            break;
          }
        }
      }
    }

  }

  public sealed partial class EnergyInfo : pb::IMessage<EnergyInfo> {
    private static readonly pb::MessageParser<EnergyInfo> _parser = new pb::MessageParser<EnergyInfo>(() => new EnergyInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pb::MessageParser<EnergyInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::LD.Protocol.ItemReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EnergyInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EnergyInfo(EnergyInfo other) : this() {
      recoverStartTime_ = other.recoverStartTime_;
      todayBuyTimes_ = other.todayBuyTimes_;
      remainStorageTimes_ = other.remainStorageTimes_;
      todayWatchAdTimes_ = other.todayWatchAdTimes_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public EnergyInfo Clone() {
      return new EnergyInfo(this);
    }

    /// <summary>Field number for the "recoverStartTime" field.</summary>
    public const int RecoverStartTimeFieldNumber = 1;
    private long recoverStartTime_;
    /// <summary>
    /// 体力恢复计算时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public long RecoverStartTime {
      get { return recoverStartTime_; }
      set {
        recoverStartTime_ = value;
      }
    }

    /// <summary>Field number for the "todayBuyTimes" field.</summary>
    public const int TodayBuyTimesFieldNumber = 2;
    private int todayBuyTimes_;
    /// <summary>
    /// 今日购买次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int TodayBuyTimes {
      get { return todayBuyTimes_; }
      set {
        todayBuyTimes_ = value;
      }
    }

    /// <summary>Field number for the "remainStorageTimes" field.</summary>
    public const int RemainStorageTimesFieldNumber = 3;
    private int remainStorageTimes_;
    /// <summary>
    /// 剩余存储的次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int RemainStorageTimes {
      get { return remainStorageTimes_; }
      set {
        remainStorageTimes_ = value;
      }
    }

    /// <summary>Field number for the "todayWatchAdTimes" field.</summary>
    public const int TodayWatchAdTimesFieldNumber = 4;
    private int todayWatchAdTimes_;
    /// <summary>
    /// 今日观看广告次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int TodayWatchAdTimes {
      get { return todayWatchAdTimes_; }
      set {
        todayWatchAdTimes_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override bool Equals(object other) {
      return Equals(other as EnergyInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public bool Equals(EnergyInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (RecoverStartTime != other.RecoverStartTime) return false;
      if (TodayBuyTimes != other.TodayBuyTimes) return false;
      if (RemainStorageTimes != other.RemainStorageTimes) return false;
      if (TodayWatchAdTimes != other.TodayWatchAdTimes) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override int GetHashCode() {
      int hash = 1;
      if (RecoverStartTime != 0L) hash ^= RecoverStartTime.GetHashCode();
      if (TodayBuyTimes != 0) hash ^= TodayBuyTimes.GetHashCode();
      if (RemainStorageTimes != 0) hash ^= RemainStorageTimes.GetHashCode();
      if (TodayWatchAdTimes != 0) hash ^= TodayWatchAdTimes.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void WriteTo(pb::CodedOutputStream output) {
      if (RecoverStartTime != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(RecoverStartTime);
      }
      if (TodayBuyTimes != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(TodayBuyTimes);
      }
      if (RemainStorageTimes != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(RemainStorageTimes);
      }
      if (TodayWatchAdTimes != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(TodayWatchAdTimes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public int CalculateSize() {
      int size = 0;
      if (RecoverStartTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(RecoverStartTime);
      }
      if (TodayBuyTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TodayBuyTimes);
      }
      if (RemainStorageTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RemainStorageTimes);
      }
      if (TodayWatchAdTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(TodayWatchAdTimes);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(EnergyInfo other) {
      if (other == null) {
        return;
      }
      if (other.RecoverStartTime != 0L) {
        RecoverStartTime = other.RecoverStartTime;
      }
      if (other.TodayBuyTimes != 0) {
        TodayBuyTimes = other.TodayBuyTimes;
      }
      if (other.RemainStorageTimes != 0) {
        RemainStorageTimes = other.RemainStorageTimes;
      }
      if (other.TodayWatchAdTimes != 0) {
        TodayWatchAdTimes = other.TodayWatchAdTimes;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    public void MergeFrom(pb::CodedInputStream input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            RecoverStartTime = input.ReadInt64();
            break;
          }
          case 16: {
            TodayBuyTimes = input.ReadInt32();
            break;
          }
          case 24: {
            RemainStorageTimes = input.ReadInt32();
            break;
          }
          case 32: {
            TodayWatchAdTimes = input.ReadInt32();
            break;
          }
        }
      }
    }

  }

  #endregion

}

#endregion Designer generated code
