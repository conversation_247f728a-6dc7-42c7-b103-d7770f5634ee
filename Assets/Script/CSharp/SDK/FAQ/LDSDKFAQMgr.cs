using OneMT.SDK;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDSDKFAQMgr
    {
        public void Init()
        {

        }

        public void showFAQ()
        {
            if (RuntimeSettings.UseSDK)
            {
                OneMTFAQ.showFAQ();
            }
        }        
        public void showMyIssues()
        {
            if (RuntimeSettings.UseSDK)
            {
                OneMTFAQ.showMyIssues("GameSettings");
            }
        }    
        public void showFAQSection()
        {
            if (RuntimeSettings.UseSDK)
            {
                OneMTFAQ.showFAQSection("1001");
            }
        }       
        public void showSingleFAQ()
        {
            if (RuntimeSettings.UseSDK)
            {
                OneMTFAQ.showSingleFAQ("10011");
            }
        }     
        
        public void registerUnreadMessageListener()
        {
            if (RuntimeSettings.UseSDK)
            {
                Action<bool, int> callback = delegate (bool isSuccess, int unreadCount)
                {
                };
                OneMTFAQ.registerUnreadMessageListener(callback);
            }
        }      
        public void getFaqUnreadMessage()
        {
            if (RuntimeSettings.UseSDK)
            {
                Action<bool, int> callback = delegate (bool isSuccess, int unreadCount)
                {
                };
                OneMTFAQ.getFaqUnreadMessage(callback);
            }
        }       
        
        public void showAccountLogout()
        {
            if (RuntimeSettings.UseSDK)
            {
                OneMTFAQ.showAccountLogout("gameSettings");
            }
        }    
        public void showCustomerServiceSpecificView()
        {
            if (RuntimeSettings.UseSDK)
            {
                  OneMTFAQ.showCustomerServiceSpecificView("submit", "mall");
            }
        }
    }
}
