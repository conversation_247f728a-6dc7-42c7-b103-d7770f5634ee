using Google.Protobuf;
using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetEnhancementUnlockMgr
    {
        public LDNetEnhancementUnlockDTO Data { get; protected set; } = new LDNetEnhancementUnlockDTO();

        protected override void InitNetImp()
        {

        }

        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            return null;
        }






        protected override void ClearNetDataImp()
        {
            Data = new LDNetEnhancementUnlockDTO();
        }
    }
}