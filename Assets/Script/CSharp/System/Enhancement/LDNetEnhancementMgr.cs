using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDEnhancementLevelNode
    {
        public int Level = -1; //  等级
        public bool IsUpgrade = false; // 升阶
        public string Reward = string.Empty; // 发奖励
        public int QuaCfgId = -1;
    }

    public class LDEnhancementSlotId
    {
        public const int Hp = 1;
        public const int Atk = 2;
        public const int Def = 3;

        public static List<int> Ids = new List<int> { Hp, Atk, Def };
    }

    public partial class LDNetEnhancementMgr : LDNetDataMgr
    {
        private List<LDEnhancementLevelNode> m_LevelNodes = new List<LDEnhancementLevelNode>();
        private Dictionary<int, List<LDEnhancementLevelNode>> m_LevelNodesGroup = new Dictionary<int, List<LDEnhancementLevelNode>>();
        private int m_ClickLvUpBtnTimes;

        protected override void InitImp()
        {

        }

        // 等级节点  
        public List<LDEnhancementLevelNode> GetLevelNodes()
        {
            if (m_LevelNodes.Count > 0)
            {
                return m_LevelNodes;
            }

            Dictionary<int, LDEnhancementLevelNode> m_dic = new Dictionary<int, LDEnhancementLevelNode>();

            EnhancementQualityItem[] quaCfgs = EnhancementQuality.Data.items;
            foreach (EnhancementQualityItem cfg in quaCfgs)
            {
                int level = cfg.lvmax * LDEnhancementSlotId.Ids.Count;
                if (!m_dic.ContainsKey(level))
                {
                    m_dic[level] = new LDEnhancementLevelNode { Level = level };
                }

                m_dic[level].IsUpgrade = true;
                m_dic[level].QuaCfgId = cfg.id;
            }

            EnhancementNodeRewardsItem[] rwdCfgs = EnhancementNodeRewards.Data.items;
            foreach (EnhancementNodeRewardsItem cfg in rwdCfgs)
            {
                int level = cfg.lv;
                if (!m_dic.ContainsKey(level))
                {
                    m_dic[level] = new LDEnhancementLevelNode { Level = level };
                }

                m_dic[level].Reward = cfg.item;
                m_dic[level].QuaCfgId = GetQuaCfgIdByLv(level);
            }

            m_LevelNodes.AddRange(m_dic.Values);
            m_LevelNodes.Sort((a, b) => a.Level - b.Level);

            return m_LevelNodes;
        }

        // 获取属性总加成
        public Dictionary<int, LDAttrAddition> GetAttrInfo()
        {
            Dictionary<int, LDAttrAddition> info = new Dictionary<int, LDAttrAddition>();
            List<LDAttrAddition> quaAttrInfo = GetTotalQuaAttrAdditions();
            foreach (int slotId in LDEnhancementSlotId.Ids)
            {
                int lv = Data.GetSlotLv(slotId);
                LDAttrAddition attr = GetAttrAdditionByLv(lv, slotId);
                if (attr == null)
                {
                    continue;
                }
                foreach (LDAttrAddition quaAttr in quaAttrInfo)
                {
                    if (attr.IsSameAttr(quaAttr))
                    {
                        attr.AddVal(quaAttr.Val);
                    }
                }
                info.Add(slotId, attr);
            }
            return info;
        }

        // 获取等级对应的属性加成
        public LDAttrAddition GetAttrAdditionByLv(int lv, int slotId)
        {
            EnhancementLvItem cfg = EnhancementLv.Data.Get(lv);
            if (cfg == null)
            {
                return null;
            }
            if (lv == 0)
            {
                return null;
            }

            if (slotId == LDEnhancementSlotId.Hp && !string.IsNullOrEmpty(cfg.slot_1))
            {
                return new LDAttrAddition(cfg.slot_1);
            }
            if (slotId == LDEnhancementSlotId.Atk && !string.IsNullOrEmpty(cfg.slot_2))
            {
                return new LDAttrAddition(cfg.slot_2);
            }
            if (slotId == LDEnhancementSlotId.Def && !string.IsNullOrEmpty(cfg.slot_3))
            {
                return new LDAttrAddition(cfg.slot_3);
            }
            return null;
        }

        // 根据等级获取阶位Id
        public int GetQuaCfgIdByLv(int lv)
        {
            float val = (float)lv / LDEnhancementSlotId.Ids.Count;
            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            for (int i = 0; i < cfgs.Length; i++)
            {
                EnhancementQualityItem cfg = cfgs[i];
                if (i == cfgs.Length - 1)
                {
                    return cfg.id;
                }

                if (val >= cfg.lvmax && val < cfgs[i + 1].lvmax)
                {
                    return cfg.id;
                }
            }
            return cfgs[0].id;
        }

        // 获取当前的阶位Id
        public int GetCurQuaId()
        {
            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            for (int i = cfgs.Length - 1; i >= 0; i--)
            {
                EnhancementQualityItem cfg = cfgs[i];
                if (Data.IsReachLv(cfg.lvmax))
                {
                    return cfg.id;
                }
            }
            return cfgs[0].id;
        }

        // 获取当前总等级
        public int GetTotalLevel()
        {
            int totalLevel = Data.SlotLevel1 + Data.SlotLevel2 + Data.SlotLevel3;
            return totalLevel;
        }

        // 获取当前阶级的属性总加成
        public List<LDAttrAddition> GetTotalQuaAttrAdditions()
        {
            List<LDAttrAddition> attrData = new List<LDAttrAddition>();
            int quaId = GetCurQuaId();

            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            foreach (EnhancementQualityItem cfg in cfgs)
            {
                foreach (string attr in cfg.attrReward)
                {
                    LDAttrAddition add = new LDAttrAddition(attr);
                    bool has = false;
                    foreach (LDAttrAddition data in attrData)
                    {
                        if (data.IsSameAttr(add))
                        {
                            data.AddVal(add.Val);
                            has = true;
                        }
                    }
                    if (!has)
                    {
                        attrData.Add(add);
                    }
                }

                if (cfg.id >= quaId)
                {
                    break;
                }
            }

            return attrData;
        }

        // 获取节点组
        public Dictionary<int,List<LDEnhancementLevelNode>> GetLevelNodesGroup()
        {
            if (m_LevelNodesGroup.Count > 0)
            {
                return m_LevelNodesGroup;
            }
            
            List<LDEnhancementLevelNode> allNodes = GetLevelNodes();
            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            
            for (int i = 0; i < cfgs.Length; i++)
            {
                if (i == cfgs.Length - 1)
                {
                    break;
                }

                EnhancementQualityItem cfg = cfgs[i];
                int curLvMax = cfg.lvmax * LDEnhancementSlotId.Ids.Count;
                int nextLvMax = cfgs[i + 1].lvmax * LDEnhancementSlotId.Ids.Count;
                
                foreach (LDEnhancementLevelNode node in allNodes)
                {
                    if (node.Level >= curLvMax && node.Level <= nextLvMax)
                    {
                        if (!m_LevelNodesGroup.ContainsKey(nextLvMax))
                        {
                            m_LevelNodesGroup.Add(nextLvMax, new List<LDEnhancementLevelNode>());
                        }
                        m_LevelNodesGroup[nextLvMax].Add(node);
                    }
                }
            }
            
            return m_LevelNodesGroup;
        }
        
        // 获取当前的节点数据
        public List<LDEnhancementLevelNode> GetCurNodes()
        {
            int curMaxLv = GetCurEnhanceMaxLv();
            Dictionary<int, List<LDEnhancementLevelNode>> group = GetLevelNodesGroup();
            foreach (KeyValuePair<int, List<LDEnhancementLevelNode>> info in group)
            {
                if (info.Key == curMaxLv)
                {
                    return info.Value;
                }
            }
            return new List<LDEnhancementLevelNode>();
        }
        
        // 获取阶位名称
        public string GetLevelName(int quaId)
        {
            EnhancementQualityItem cfg = EnhancementQuality.Data.Get(quaId);
            if (cfg.smallRank == 0)
            {
                return UiTools.Localize(cfg.bigRankName);
            }
            return UiTools.Localize(cfg.name, cfg.smallRank);
        }

        // 获取槽位的等级上限
        public int GetSlotLvMax(int quaId)
        {
            EnhancementQualityItem curCfg = EnhancementQuality.Data.Get(quaId);
            if (EnhancementQuality.Data.TryGet(quaId + 1, out EnhancementQualityItem cfg, false))
            {
                return cfg.lvmax - curCfg.lvmax;
            }
            return 0;
        }

        // 获取升级预览加成值
        public long GetSlotUpValue(int slotId)
        {
            int lv = Data.GetSlotLv(slotId);
            EnhancementLvItem[] cfgs = EnhancementLv.Data.items;
            if (lv < 0)
            {
                return 0;
            }
            if (lv >= cfgs[cfgs.Length - 1].id)
            {
                return 0;
            }

            EnhancementLvItem cfg = EnhancementLv.Data.Get(lv);
            EnhancementLvItem nextCfg = EnhancementLv.Data.Get(lv + 1);
            if (slotId == LDEnhancementSlotId.Hp)
            {
                return (new LDAttrAddition(nextCfg.slot_1)).Val - (new LDAttrAddition(cfg.slot_1)).Val;
            }
            if (slotId == LDEnhancementSlotId.Atk)
            {
                return (new LDAttrAddition(nextCfg.slot_2)).Val - (new LDAttrAddition(cfg.slot_2)).Val;
            }
            if (slotId == LDEnhancementSlotId.Def)
            {
                return (new LDAttrAddition(nextCfg.slot_3)).Val - (new LDAttrAddition(cfg.slot_3)).Val;
            }

            return 0;
        }

        // 获取大阶分组数据
        public Dictionary<int, List<EnhancementQualityItem>> GetBigRankCfgs()
        {
            Dictionary<int, List<EnhancementQualityItem>> result = new Dictionary<int, List<EnhancementQualityItem>>();
            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            foreach (EnhancementQualityItem cfg in cfgs)
            {
                if (cfg.bigRank == 0)
                {
                    continue;
                }

                if (!result.ContainsKey(cfg.bigRank))
                {
                    result[cfg.bigRank] = new List<EnhancementQualityItem>();
                }
                result[cfg.bigRank].Add(cfg);
            }
            return result;
        }

        // 获取下一个可以解锁的阶位ID
        public int GetNextCanUnlockQuaId()
        {
            int quaId = GetCurQuaId();
            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            foreach (EnhancementQualityItem cfg in cfgs)
            {
                if (quaId >= cfg.id && Data.UnlockId < cfg.id)
                {
                    return cfg.id;
                }
            }
            return -1;
        }
        
        // 获取当前强化等级上限
        public int GetCurEnhanceMaxLv()
        {
            if (EnhancementQuality.Data.TryGet(Data.UnlockId + 1, out EnhancementQualityItem cfg, false)) 
            {
                return cfg.lvmax * LDEnhancementSlotId.Ids.Count;
            }
            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            return cfgs[cfgs.Length - 1].lvmax * LDEnhancementSlotId.Ids.Count;
        }

        // 获取已解锁特权
        public List<string> GetPrivileges()
        {
            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            List<string> privileges = new List<string>();
            foreach (EnhancementQualityItem cfg in cfgs)
            {
                if (cfg.id <= Data.UnlockId && !string.IsNullOrEmpty(cfg.privilegeEffect))
                {
                    privileges.Add(cfg.privilegeEffect);
                }
            }
            return privileges;
        }

        // 获取已解锁的词条
        public List<List<int>> GetCitiaoInfo(int unLockId)
        {
            List<List<int>> info = new List<List<int>>();
            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            foreach (EnhancementQualityItem cfg in cfgs)
            {
                if (cfg.id <= unLockId && cfg.citiaoEffect.Length > 0)
                {
                    info.Add(new List<int>(cfg.citiaoEffect));
                }
            }
            return info;
        }

        // 获取已解锁的词条
        public List<int> GetCitiaoIds(List<List<int>> datas, int passType)
        {
            List<int> skillData = new List<int>();
            foreach (List<int> citiaoInfos in datas)
            {
                for (int i = 1; i < citiaoInfos.Count; i++)
                {
                    if(citiaoInfos[i] == (int)LDPassType.All || citiaoInfos[i] == passType)
                    {
                        skillData.Add(citiaoInfos[0]);
                        break;
                    }
                }
            }
            return skillData;
        }
        
        // 获取当前可解锁的大阶ID
        public int GetCurCanUnlockBigRank()
        {
            int nextQuaId = GetNextCanUnlockQuaId();
            if (nextQuaId < 0)
            {
                return -1;
            }

            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            foreach (EnhancementQualityItem cfg in cfgs)
            {
                if (cfg.id == nextQuaId)
                {
                    return cfg.bigRank;
                }
            }
            return -1;
        }

        // 左右切换按钮红点信息
        public void GetChangeBtnRedInfo(int bigRank, out bool leftRed, out bool rightRed)
        {
            if (!CanUnlockQuaLevel())
            {
                leftRed = false;
                rightRed = false;
                return;
            }

            int curBigRank = GetCurCanUnlockBigRank();
            leftRed = bigRank > curBigRank;
            rightRed = bigRank < curBigRank;
        }
        
        // 检测 单击升级 长按提示
        public void CheckLvUpLongPressTips()
        {
            m_ClickLvUpBtnTimes++;
            if (m_ClickLvUpBtnTimes == 5) // 第5次检测一下就行了 不重要
            {
                long time = Global.gApp.gSystemMgr.gLocalDataMgr.GetLongVal(true, LDLocalDataKeys.Enhancement_LvBtnLongPressTips, 0);
                if (DateTimeUtil.SameDay(time, DateTimeUtil.GetServerTime()))
                {
                    return;
                }
                
                int val = Global.gApp.gSystemMgr.gLocalDataMgr.GetIntVal(true, LDLocalDataKeys.Enhancement_LvBtnLongPressTips_NoTips, 0);
                if (val <= 0)
                {
                    Global.gApp.gUiMgr.OpenUIAsync<LongPressTipsUI>(LDUICfg.LongPressTipsUI).SetLoadedCall(ui =>
                    {
                        ui?.RefreshUI(LongPressTipsUI.Type_EnhancementLvUp);
                    });
                    Global.gApp.gSystemMgr.gLocalDataMgr.SetLongVal(true, LDLocalDataKeys.Enhancement_LvBtnLongPressTips, DateTimeUtil.GetServerTime());
                }
            }
        }
        
        // 存储单机升级长按提示
        public void SaveLvUpBtnLongPressState(bool enable)
        {
            Global.gApp.gSystemMgr.gLocalDataMgr.SetIntVal(true, LDLocalDataKeys.Enhancement_LvBtnLongPressTips_NoTips, enable ? 1 : 0);
        }



        // 是否满级
        public bool IsTotalLevelMax()
        {
            EnhancementQualityItem[] cfgs = EnhancementQuality.Data.items;
            EnhancementQualityItem lastCfg = cfgs[cfgs.Length - 1];
            bool isReachLv = Data.IsReachLv(lastCfg.lvmax);
            return isReachLv;
        }
        
        // 当前是否达到等级限制
        public bool IsCurLevelMax()
        {
            return GetTotalLevel() >= GetCurEnhanceMaxLv();
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            if (GetTotalLevel() > 1) // 2025.3.31版本：解锁条件6-3改为7-3  之前已升级过的玩家不拦截
            {
                return true;
            }
            return CheckModuleOpen(LDSystemEnum.MechaEnhancement, isShowLockTips);
        }

        // 当前可解锁
        public bool CanUnlockQuaLevel()
        {
            int nextQuaId = GetNextCanUnlockQuaId();
            if (nextQuaId < 0)
            {
                return false;
            }

            EnhancementQualityItem quaCfg = EnhancementQuality.Data.Get(nextQuaId);
            LDCommonItem commonItem = new LDCommonItem(quaCfg.unlockItem);
            long cnt = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(commonItem.Id);
            return cnt >= commonItem.Num;
        }

        // 当前可升级
        public bool CanLevelUp()
        {
            if (IsCurLevelMax())
            {
                return false;
            }
            
            if (IsTotalLevelMax())
            {
                return false;
            }

            foreach (int slotId in LDEnhancementSlotId.Ids)
            {
                int lv = Data.GetSlotLv(slotId);
                EnhancementLvItem lvCfg = EnhancementLv.Data.Get(lv);
                LDCommonItem item = new LDCommonItem(lvCfg.cost);
                long curNum = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(item.Id);
                if (curNum >= item.Num)
                {
                    return true;
                }
            }
            return false;
        }

        public bool IsCanGuide()
        {
            if (GetTotalLevel() <= 1)
            {
                return true;
            }

            return false;
        }

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (!IsUnlock())
            {
                return LDRedTipsState.None;
            }

            if (sys == LDSystemEnum.Custom_Enhancement_Level)
            {
                if (CanLevelUp())
                {
                    return LDRedTipsState.Arrow;
                }
            }

            if(sys == LDSystemEnum.Custom_Enhancement_Unlock)
            {
                if (!Global.gApp.gSystemMgr.gEnhancementUnlockMgr.IsUnlock())
                {
                    return LDRedTipsState.None;
                }

                if (CanUnlockQuaLevel())
                {
                    return LDRedTipsState.RedPoint;
                }
            }

            return LDRedTipsState.None;
        }

        protected override void DestroyImp()
        {

        }
    }
}