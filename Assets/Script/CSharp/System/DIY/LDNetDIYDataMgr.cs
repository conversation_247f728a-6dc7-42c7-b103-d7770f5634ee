using Google.Protobuf;
using LD.Protocol;
using LitJson;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetDIYDataMgr : LDNetDataMgr
    {

        private LDDIYData m_CoveData = null;
        protected override void InitImp()
        {
        }


        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.DIY, isShowLockTips);
        }

        public LDDIYData GetDIYData()
        {
            LDNetMechaDTOItem mechaDTOItem = Global.gApp.gSystemMgr.gMechaDataMgr.GetCurBattleMecha();
            return GetDIYData(mechaDTOItem.MechaId);
        }
        public LDDIYData GetDIYData(int mechaId)
        {
            LDDIYData DIYData = null;
            if (Data.DIYData.Count > 0)
            {
                foreach (LDDIYData diyData in Data.DIYData)
                {
                    if (mechaId == diyData.BodyId)
                    {
                        DIYData = diyData;
                        break;
                    }
                }
            }
            if (DIYData == null)
            {
                DIYData = new LDDIYData();
                DIYData.BodyId = mechaId;
                Data.DIYData.Add(DIYData);
            }
            DIYData.BodySkinId = Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetCurMechaSkinId(mechaId);
            if (DIYData.BodySkinId > 0)
            {
                DIYData.HidBodySkinId = Global.gApp.gSystemMgr.gMechaSkinDataMgr.ISMechaSkineHide(DIYData.BodySkinId) ? 1 : 0;
            }
            foreach (LDDIYPartData item in DIYData.Data)
            {
                item.SId = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartSkinId(item.PId);
                if (item.SId > 0)
                {
                    item.HSId = Global.gApp.gSystemMgr.gMechaPartSkinePart.ISPartSkineHide(item.SId) ? 1 : 0;
                }
            }
            return DIYData;
        }
        public void CalcMechaSkin(LDDIYData DIYData)
        {
            DIYData.BodySkinId = Global.gApp.gSystemMgr.gMechaSkinDataMgr.GetCurMechaSkinId(DIYData.BodyId);
            if (DIYData.BodySkinId > 0)
            {
                DIYData.HidBodySkinId = Global.gApp.gSystemMgr.gMechaSkinDataMgr.ISMechaSkineHide(DIYData.BodySkinId) ? 1 : 0;
            }
            foreach (LDDIYPartData item in DIYData.Data)
            {
                item.SId = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartSkinId(item.PId);
                if (item.SId > 0)
                {
                    item.HSId = Global.gApp.gSystemMgr.gMechaPartSkinePart.ISPartSkineHide(item.SId) ? 1 : 0;
                }
            }
        }
        public bool BlockEquipment(int blockItemId)
        {
            LDDIYData DIYData = GetDIYData();
            foreach (LDDIYPartData item in DIYData.Data)
            {
                if (item.PId == blockItemId)
                {
                    return true;
                }
            }
            return false;
        }
        public void CoverDIYData(DiyModel newDiyData)
        {
            CoverDIYData(LDDIYData.Convert(newDiyData));
        }
        private void CoverDIYData(LDDIYData newDiyData)
        {
            if (newDiyData != null)
            {
                int diyIndex = -1;
                foreach (LDDIYData data in Data.DIYData)
                {
                    diyIndex++;
                    if (newDiyData.BodyId == data.BodyId)
                    {
                        break;
                    }
                }
                if (diyIndex >= 0)
                {
                    Data.DIYData[diyIndex] = newDiyData;
                }
            }
        }

        protected override void DestroyImp()
        {
        }
    }
}
