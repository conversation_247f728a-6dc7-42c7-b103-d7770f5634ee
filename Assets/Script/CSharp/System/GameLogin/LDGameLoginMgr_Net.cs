using Google.Protobuf;
using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDGameLoginMgr
    {
        public LDNetGDPRInfo GDPRInfo { get; private set; } = new LDNetGDPRInfo();
        protected override void InitNetImp()
        {
            AddNetHandler(Protocol.opcode.KickoutResponse);
            AddNetHandler(Protocol.opcode.LoginResponse);
            AddNetHandler(Protocol.opcode.PlayerAssembleFinishResponse);
            AddNetHandler(opcode.CdailyRefreshEventResponse);
        }

        public override IMessage HandleNetMsg(int code, Protocol.opcode s2c_msgId, byte[] data)
        {
            //  踢人 要弹出ui 。暂停战斗。等等 等等。踢人下线
            if (s2c_msgId == Protocol.opcode.KickoutResponse)
            {
                Global.gApp.gNetMgr.NetContent.DisposeNet();
                KickoutResponse kickoutResponse = KickoutResponse.Parser.ParseFrom(data);
                if (kickoutResponse.Reson == 1)
                {
                    // 账号顶替
                    Global.gApp.gUiMgr.OpenUIAsync<ConfirmUI>(LDUICfg.KickOutConfirm).SetLoadedCall(baseUI =>
                    {
                        baseUI.SetInfo(() => { Global.gApp.QuitGame(); }, Global.gApp.QuitGame, 69887, 91148);
                        baseUI.Button_1.gameObject.SetActive(false);
                    });
                }
                else if (kickoutResponse.Reson == 2)
                {
                    //GM 踢人
                    Global.gApp.gUiMgr.OpenUIAsync<ConfirmUI>(LDUICfg.KickOutConfirm).SetLoadedCall(baseUI =>
                    {
                        baseUI.SetInfo(() => { Global.gApp.QuitGame(); }, Global.gApp.QuitGame, 69888, 91148);
                        baseUI.Button_1.gameObject.SetActive(false);
                    });
                }
                else if (kickoutResponse.Reson == 3)
                {
                    // ShowConfirm---
                    Global.gApp.gUiMgr.OpenUIAsync<ConfirmUI>(LDUICfg.KickOutConfirm).SetLoadedCall(baseUI =>
                    {
                        baseUI.SetInfo(() => { Global.gApp.QuitGame(); }, Global.gApp.QuitGame, 69889, 91148);
                        baseUI.Button_1.gameObject.SetActive(false);
                    });
                }

                Global.gApp.gNetMgr.IsKickout = true;

                return kickoutResponse;
            }
            else if (s2c_msgId == Protocol.opcode.LoginResponse)
            {
                MainUtils.OMTReportEvent("1010320");
                Protocol.LoginResponse loginResponse = Protocol.LoginResponse.Parser.ParseFrom(data);
                Global.gApp.gSystemMgr.gRoleMgr.Data.SetPlayerId(loginResponse.PlayerId);
                GDPRInfo.SyncBanPlayers(loginResponse.GdprBanPlayers);
                DateTimeUtil.FreshServerTime(loginResponse.ServerTime);
                DateTimeUtil.OpenServerTime = loginResponse.ServerOpenTime;
                DateTimeUtil.ServerTimeZone = loginResponse.Zone;
                if (!loginResponse.IsBaned)
                {
                    Global.gApp.gToastMgr.ShowGameTips(91154);
                    TryPoorNetUI();
                }
                else
                {
                    Global.gApp.gNetMgr.NetContent.DisposeNet();
                    Global.gApp.gUiMgr.CloseUI(LDUICfg.PoorNetworkUI);
                    RemoveLoginRequestCheck();
                    string des = Global.gApp.gGameData.GetExpireTimeTips((long)loginResponse.UnbanAt * 1000);
                    string tips = Global.gApp.gGameData.GetTipsInCurLanguage(69897);
                    Global.gApp.gToastMgr.ShowGameTips(string.Format(tips, des));
                }

                return loginResponse;
            }
            else if (s2c_msgId == opcode.PlayerAssembleFinishResponse)
            {
                MainUtils.OMTReportEvent("1010330");
                RemoveLoginRequestCheck();
                Protocol.PlayerAssembleFinishResponse resp = Protocol.PlayerAssembleFinishResponse.Parser.ParseFrom(data);
                Global.gApp.gToastMgr.ShowGameTips(91153);
                Global.gApp.gSystemMgr.SetLoginState(true);

                Global.gApp.gFrameTimerMgrRender.AddFrameTimer(2, 1, (_, _) => { Global.gApp.gSdkMgr.gOneMTMgr.online(); });

                Global.gApp.gFrameTimerMgrRender.AddFrameTimer(4, 1, (_, _) =>
                {
                    Global.gApp.gUiMgr.CloseUI(LDUICfg.PoorNetworkUI);
                    if (Global.gApp.CurScene is LoginScene)
                    {
                        Global.gApp.gGameCtrl.TryChangeToMainScene();
                    }

                    CheckToAddCrossDay();
                    Global.gApp.gSystemMgr.gChatMgr.SendChatPrivateMsgConfirmRequest();
                    Global.gApp.gSystemMgr.gExpeditionMgr.CheckToGetLayer();
                    LDNetActivityInfo timeLimitedRechargeInfo = Global.gApp.gSystemMgr.gTimeLimitedRechargeMgr.Data.m_ActivityInfo;
                    if (timeLimitedRechargeInfo != null)
                    {
                        Global.gApp.gSystemMgr.gExchangeShopMgr.SendExchangeShopReqNotOpen(LDExchangeShopType.Activity, timeLimitedRechargeInfo.ActivityId);
                    }

                    Global.gApp.gSystemMgr.gDIYPackMgr.CheckDiyPack();
                    Global.gApp.gSystemMgr.gPaymentMgr.Data.LoadUseProxyCurrency();
                });


                return resp;
            }
            else if (s2c_msgId == opcode.CdailyRefreshEventResponse)
            {
                //跨天消息
                CDailyRefreshEventResponse info = CDailyRefreshEventResponse.Parser.ParseFrom(data);
                Global.Log($"client ServerTime:{DateTimeUtil.GetServerTime()}  :{DateTimeUtil.GetFormatTime(DateTimeUtil.GetServerTime())}");
                Global.Log($"Server ServerTime:{info.CurTime}   :{DateTimeUtil.GetFormatTime(info.CurTime)}");
                OnCrossDay();
                return info;
            }

            return null;
        }

        /// <summary>
        /// 强制链接的前提是  没有正在重连。。正在重连你强联没意义
        /// </summary>
        public void TryForceLoginGameServer()
        {
            Global.gApp.gTimerMgr.AddTimer(0.2f, 1, (float a, bool b) => { ForceLoginGameServer(); });
        }

        public void ForceLoginGameServer()
        {
            if (!Global.gApp.gNetMgr.NetContent.Connecting())
            {
                Global.gApp.gNetMgr.NetContent.DisposeNet();
                LoginGameServer();
            }
        }

        public void ForceDisposeGameServer()
        {
            if (!Global.gApp.gNetMgr.NetContent.Connecting())
            {
                Global.gApp.gNetMgr.NetContent.DisposeNet();
            }
        }

        public void LoginGameServer()
        {
            if (Global.gApp.gNetMgr.NetContent.CanConnect())
            {
                Debug.Log("LoginGame  LoginGame ");
                int port = Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerItem().port;
                string host = Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerItem().host;
                if (port > 0 && !string.IsNullOrEmpty(host))
                {
                    Global.gApp.gSystemMgr.SetLoginState(false);
                    Global.gApp.gNetMgr.Connect(OnConnect, Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerItem());
                }
                else
                {
                    Debug.LogError("host " + host + " port " + port);
                }
            }
        }

        // 链接服务器成功。需要 走game服登录流程
        private void OnConnect(bool sucess)
        {
            Debug.Log("OnConnect sucess  " + sucess);
            if (sucess)
            {
                SendLoginRequest();
            }
        }

        private void SendLoginRequest()
        {
            MainUtils.OMTReportEvent("1010310");
            Global.gApp.gSystemMgr.SetLoginState(false);
            LoginRequest loginRequest = new LoginRequest();
            LDServerListItem ServerItem = Global.gApp.gSystemMgr.gLoginAccountMgr.GetCurServerItem();
            loginRequest.ClientId = Global.gApp.gSystemMgr.gLoginAccountMgr.AccountID;
            loginRequest.DeviceId = Global.gApp.gSdkMgr.gAccountMgr.DeviceID;
            loginRequest.ServerId = (int)ServerItem.id;

            RuntimePlatform platform = Application.platform;
            if (platform == RuntimePlatform.WindowsEditor || platform == RuntimePlatform.OSXEditor)
            {
                loginRequest.Platform = "editor";
                loginRequest.Channel = "0";
            }
            else if (platform == RuntimePlatform.Android)
            {
                loginRequest.Platform = "android";
                loginRequest.Channel = "2";
            }
            else if (platform == RuntimePlatform.IPhonePlayer)
            {
                loginRequest.Platform = "ios";
                loginRequest.Channel = "1";
            }
            // webgl
            else
            {
            }

            loginRequest.SubChannel = Global.gApp.gSdkMgr.gOneMTMgr.GetChannel();
            loginRequest.BundleId = Global.gApp.gSdkMgr.gOneMTMgr.GetBundleId();

            loginRequest.Ltid = Global.gApp.gSdkMgr.gAccountMgr.UserID;
            loginRequest.Token = Global.gApp.gSystemMgr.gLoginAccountMgr.AccountData.data.token;
            loginRequest.Device = Global.gApp.gSdkMgr.gOneMTMgr.deviceModel();
            loginRequest.DeviceOs = Global.gApp.gSdkMgr.gOneMTMgr.OSVersion();

            string nativeVersion = RuntimeSettings.GeNativeVerition().GetResVer();
            string curVerson = RuntimeSettings.GeCurVerition().GetResVer();
            loginRequest.ClientVersion = curVerson;
            loginRequest.NativeVersion = nativeVersion;
            loginRequest.FakeRate = 0;
            // TODO: dqx 多渠道 处理

            loginRequest.Lang = Global.gApp.gGameData.GetSystemCurLanguage();
            loginRequest.LangCode = RuntimeSettings.RomateResVersion.MainVersionData.client_ip; //CS.UnityEngine.Application.systemLanguage:ToString()
            loginRequest.ClientIp = RuntimeSettings.RomateResVersion.MainVersionData.client_ip; //CS.UnityEngine.Application.systemLanguage:ToString()
            loginRequest.CountryCode = Global.gApp.gSystemMgr.GetOriCountryCode(); // 应该是打包传进来的。注意一下 我们到时候就搞分支。把差异化的东西搞到一起
            loginRequest.ClientLang = 1;
            loginRequest.StandardLangCode = Global.gApp.gSdkMgr.gOneMTMgr.deviceLanguage();

            TryPoorNetUI();

            Send(opcode.LoginRequest, loginRequest);
            StartLoginRequestCheck();
        }

        private int m_LoginTimerId = -1;

        private void RemoveLoginRequestCheck()
        {
            Global.gApp.gTimerMgr.RemoveTimer(m_LoginTimerId);
        }

        private void StartLoginRequestCheck()
        {
            RemoveLoginRequestCheck();
            StartLoginRequestCheckImp();
        }

        private int m_TryTimes = 0;

        private void StartLoginRequestCheckImp()
        {
            m_LoginTimerId = Global.gApp.gTimerMgr.AddTimer(10, 1, (float a, bool b) =>
            {
                // 网络连接成功并且没收到 PlayerAssembleFinishResponse
                if (Global.gApp.gNetMgr.CanSendMsg() && !PlayerAssembleFinishResponse)
                {
                    LDSDKEvent.SendNetError("PlayerAssembleFinishResponseError");
                    m_TryTimes++;
                    if (m_TryTimes < 2)
                    {
                        ForceLoginGameServer();
                    }
                    else
                    {
                        m_TryTimes = 0;
                        ForceDisposeGameServer();
                    }
                }
            });
        }

        private void TryPoorNetUI()
        {
            if (Global.gApp.CurScene is LoginScene)
            {
                Global.gApp.gUiMgr.OpenUIAsync(LDUICfg.PoorNetworkUI);
            }
        }

        protected override void ClearNetDataImp()
        {
            GDPRInfo = new LDNetGDPRInfo();
        }

        private void CheckToAddCrossDay()
        {
            // long distanceTime = (long)DateTimeUtil.GetCrossDaySec(23, 59, 59) * 1000 + 1000;
            // AddServerTimer(LDSystemServerTimerKey.ServerCrossday, distanceTime, OnCrossDay);
        }

        private void OnCrossDay()
        {
            Global.Log($"跨天跨天跨天跨天跨天跨天跨天跨天跨天跨天跨天跨天跨天跨天跨天跨天跨天");
            Global.gApp.gMsgDispatcher.Broadcast(MsgIds.OnServerCrossDay);

            Global.gApp.gSystemMgr.gTimeLimitedRechargeMgr.RefreshRedOnCrossDay();
            Global.gApp.gSystemMgr.CheckNewUnlockSystem();
        }
    }
}