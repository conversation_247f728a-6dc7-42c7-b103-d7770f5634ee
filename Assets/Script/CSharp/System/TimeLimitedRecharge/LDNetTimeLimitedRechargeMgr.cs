using Google.Protobuf;
using LD.Protocol;

namespace LD
{
    public partial class LDNetTimeLimitedRechargeMgr : LDNetDataMgr
    {
        protected override void InitImp()
        {
        }

        protected override void DestroyImp()
        {
        }

        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.TimeLimitedRecharge, isShowLockTips);
        }

        public override LDRedTipsState GetRedState(LDSystemEnum sys)
        {
            if (Data.m_ActivityInfo == null)
                return LDRedTipsState.None;
            if (sys == LDSystemEnum.Custom_TimeLimitedRecharge)
            {
                if (!Data.m_ActivityInfo?.IsOpen() ?? false)
                {
                    return LDRedTipsState.None;
                }

                TimeLimitedRechargeRewardItem[] all = TimeLimitedRechargeReward.Data.items;
                float allRecharge = Data.m_Recharge;

                for (int i = 0; i < all.Length; i++)
                {
                    TimeLimitedRechargeRewardItem item = all[i];
                    if (item.activityID == Data.m_ActivityInfo.ActivityId)
                    {
                        bool isUnlock = allRecharge >= item.rechargeLevel01;
                        bool isGet = Data.m_RewardIds.Contains(item.id);
                        if (isUnlock && !isGet)
                        {
                            return LDRedTipsState.RedPoint;
                        }
                    }
                }
            }

            if (sys == LDSystemEnum.Custom_TimeLimitedRechargeShop)
            {
                if (Data.m_ActivityInfo != null)
                {
                    LDRedTipsState red = (Global.gApp.gSystemMgr.gActivityShopMgr.GetRedState(Data.m_ActivityInfo.ActivityId));
                    if (red == LDRedTipsState.RedPoint)
                        return red;
                }
            }

            return LDRedTipsState.None;
        }

        public bool CheckIsShowBtn()
        {
            if (Data.m_ActivityInfo != null)
            {
                return Data.m_ActivityInfo.IsOpen();
            }

            return false;
        }

        public void RefreshRedOnCrossDay()
        {
            if (Data.m_ActivityInfo != null)
            {
                Global.gApp.gSystemMgr.gRedTipMgr.FreshRedTipsLink(LDSystemEnum.Custom_TimeLimitedRecharge);
            }
        }

        public ExchangeShopItem GetExchangeShopInfo(int activityId)
        {
            var items = ExchangeShop.Data.items;
            foreach (ExchangeShopItem shopItem in items)
            {
                if (shopItem.activityID == activityId && shopItem.consumeItems.Length > 0)
                {
                    return shopItem;
                }
            }

            return null;
        }
    }
}