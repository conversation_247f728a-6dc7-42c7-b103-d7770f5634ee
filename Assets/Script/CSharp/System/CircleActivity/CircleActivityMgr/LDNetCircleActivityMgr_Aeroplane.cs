using System;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetCircleActivityMgr
    {
        /// <summary>
        /// 飞行棋进度奖励是否领取
        /// </summary>
        /// <returns></returns>
        public bool CheckAeroplaneProgressRewardReceived(int activityId, int id, int loopTimes)
        {
            if (GetCircleActivityInfo(activityId) is LDFlyChessActivityInfo activityInfo)
            {
                int roundReward = GetRoundRewardId(loopTimes, id);
                return activityInfo.roundReward.Contains(roundReward);
            }

            return false;
        }

        /// <summary>
        /// 飞行棋进度额外奖励是否领取
        /// </summary>
        /// <returns></returns>
        public bool CheckAeroplaneProgressExtraRewardReceived(int activityId, int id, int loopTimes)
        {
            if (GetCircleActivityInfo(activityId) is LDFlyChessActivityInfo activityInfo)
            {
                int roundReward = GetRoundRewardId(loopTimes, id);
                return activityInfo.extraRoundReward.Contains(roundReward);
            }

            return false;
        }


        public FlyChessCycleItem GetCurFlyChessCycleCfg(int activityId)
        {
            FlyChessCycleItem cfg = null;
            foreach (FlyChessCycleItem cycleItem in FlyChessCycle.Data.items)
            {
                if (cycleItem.activityID != activityId) continue;

                return cycleItem;
            }

            return null;
        }

        public int GetAeroplaneRoundValue(int curRound, int activityId, int loopTimes)
        {
            FlyChessCycleItem cfgMain = GetCurFlyChessCycleCfg(activityId);

            if (curRound > (cfgMain.loopTime + 1) * cfgMain.loopRound)
            {
                return curRound - cfgMain.loopTime * cfgMain.loopRound;
            }

            return curRound - cfgMain.loopRound * (loopTimes -1);
        }

        public int GetAeroplaneLoopTimesValue(int curRound, int activityId)
        {
            FlyChessCycleItem cfgMain = GetCurFlyChessCycleCfg(activityId);

            int loopTimes = Mathf.CeilToInt((float)(curRound + 1) / cfgMain.loopRound);
            loopTimes = Math.Min(loopTimes, cfgMain.loopTime + 1);

            return loopTimes;
        }

        public int GetAeroplaneRewardShowLoopTimes(int curRound, int activityId)
        {
            int realLoopTimes = GetAeroplaneLoopTimesValue(curRound, activityId);
            if (GetCircleActivityInfo(activityId) is LDFlyChessActivityInfo activityInfo)
            {
                FlyChessCycleItem m_FlyChessCycleCfg = null;
                foreach (FlyChessCycleItem cycleItem in FlyChessCycle.Data.items)
                {
                    if (cycleItem.activityID == activityId)
                    {
                        m_FlyChessCycleCfg = cycleItem;
                        break;
                    }
                }

                for (int idx = 1; idx <= realLoopTimes; idx++)
                {
                    bool isHave = GetIsHaveAeroplaneProgressReward(activityId, activityInfo, m_FlyChessCycleCfg, idx);
                    if (isHave)
                    {
                        return idx;
                    }
                }
            }
            return realLoopTimes;
        }

        #region 红点


        private LDRedTipsState GetAllRedStateInFlyChess()
        {
            int activityId = GetSeasonActivityId(LDCircleActivitySubUIType.flyChess);
            if (activityId == 0)
            {
                return LDRedTipsState.None;
            }

            List<string> openList = GetOpenChildUIList(activityId);
            if (openList.Count == 0)
            {
                return LDRedTipsState.None;
            }

            LDCircleActivityInfoBase activityInfo = Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(activityId);

            if (activityInfo == null)
            {
                return LDRedTipsState.None;
            }

            LDRedTipsState flyChessState = GetRedStateFlyChess(activityId);
            if (flyChessState != LDRedTipsState.None)
            {
                return flyChessState;
            }

            LDRedTipsState questState = GetRedStateQuest(activityId);
            if (questState != LDRedTipsState.None)
            {
                return questState;
            }

            LDRedTipsState rankState = GetRedStateRankList(activityId);
            if (rankState != LDRedTipsState.None)
            {
                return rankState;
            }

            LDRedTipsState PurchaseState = GetRedStatePurchase(activityId);
            if (PurchaseState != LDRedTipsState.None)
            {
                return PurchaseState;
            }

            LDRedTipsState BattlePassState = GetRedStateBattlePass(activityId);
            if (BattlePassState != LDRedTipsState.None)
                return BattlePassState;

            return LDRedTipsState.None;
        }

        public LDRedTipsState GetRedStateFlyChess(int activityId)
        {
            if (Global.gApp.gSystemMgr.gCircleActivityMgr.GetCircleActivityInfo(activityId) is LDFlyChessActivityInfo ActivityInfo)
            {
                //骰子红点
                FlyChessCycleItem m_FlyChessCycleCfg = null;
                foreach (FlyChessCycleItem cycleItem in FlyChessCycle.Data.items)
                {
                    if (cycleItem.activityID == activityId)
                    {
                        m_FlyChessCycleCfg = cycleItem;
                        break;
                    }
                }
                if (m_FlyChessCycleCfg == null)
                    return LDRedTipsState.None;

                LDCommonItem consumeItem = new LDCommonItem(m_FlyChessCycleCfg.Consumeitme);
                long bagNum = Global.gApp.gSystemMgr.gBagMgr.GetItemCount(consumeItem.Id);
                if (bagNum >= consumeItem.Num)
                {
                    return LDRedTipsState.RedPoint;
                }

                //进度奖红点
                int loopTimes = GetAeroplaneLoopTimesValue(ActivityInfo.circle, activityId);
                if (GetIsHaveAeroplaneProgressReward(activityId, ActivityInfo, m_FlyChessCycleCfg, loopTimes))
                {
                    return LDRedTipsState.RedPoint;
                }

                //战令红点
                LDRedTipsState BattlePassState = GetRedStateBattlePass(activityId);
                if (BattlePassState != LDRedTipsState.None)
                    return BattlePassState;

                return LDRedTipsState.None;
            }
            return LDRedTipsState.None;
        }

        public bool GetIsHaveAeroplaneProgressReward(int activityId, LDFlyChessActivityInfo activityInfo, FlyChessCycleItem m_FlyChessCycleCfg, int loopTimes)
        {
            List<int> m_RoundRewardIds = new List<int>();
            m_RoundRewardIds.AddRange(m_FlyChessCycleCfg.RoundReward);
            foreach (int roundRewardId in m_RoundRewardIds)
            {
                FlyChessRoundItem cfgRound = FlyChessRound.Data.Get(roundRewardId);
                int m_CurRound = GetAeroplaneRoundValue(activityInfo.circle, activityId, loopTimes);

                if (cfgRound.reward != "")
                {
                    bool isSelect = Global.gApp.gSystemMgr.gCircleActivityMgr.CheckAeroplaneProgressRewardReceived(activityId, cfgRound.id, loopTimes - 1); //

                    if (m_CurRound >= cfgRound.round && !isSelect)
                    {
                        return true;
                    }
                }

                if (!string.IsNullOrEmpty(cfgRound.extraReward))
                {
                    if (m_CurRound >= cfgRound.round)
                    {
                        bool isSelect = Global.gApp.gSystemMgr.gCircleActivityMgr.CheckAeroplaneProgressExtraRewardReceived(activityId, cfgRound.id, loopTimes - 1);
                        if (!isSelect)
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        #endregion
    }
}