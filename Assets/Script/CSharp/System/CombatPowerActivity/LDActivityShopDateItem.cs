using System.Collections.Generic;

namespace LD
{
    public class LDActivityShopDateItem
    {
        public int Id;
        public int ActivityId;
        public int ADid;
        public int MallGoodsId;
        public int Inventory;
        public string[] Rewards;
        public int SuperValue;

        public LDActivityShopDateItem(NewCombatPowerPackItem item)
        {
            this.Id = item.id;
            this.ActivityId = item.activityID;
            this.ADid = item.ADid;
            this.MallGoodsId = item.mallGoodsId;
            this.Inventory = item.inventory;
            this.Rewards = item.Rewards;
            this.SuperValue = item.superValue;
        }

        public LDActivityShopDateItem(DiyActivityPackItem item)
        {
            this.Id = item.id;
            this.ActivityId = item.activityID;
            this.ADid = item.ADid;
            this.MallGoodsId = item.mallGoodsId;
            this.Inventory = item.inventory;
            this.Rewards = item.Reward;
            this.SuperValue = item.superValue;
        }

        public static List<LDActivityShopDateItem> GetDiyActivityShowItem(int activityId)
        {
            DiyActivityPackItem[] items = DiyActivityPack.Data.items;


            List<LDActivityShopDateItem> temp = new List<LDActivityShopDateItem>();
            List<LDActivityShopDateItem> tempReward = new List<LDActivityShopDateItem>();

            for (int i = 0; i < items.Length; i++)
            {
                DiyActivityPackItem cfgItem = items[i];
                if (cfgItem.activityID != activityId)
                    continue;
                LDActivityShopDateItem cfg = new LDActivityShopDateItem(cfgItem);

                if (cfg.ADid > 0)
                {
                    var adCfg = AD.Data.Get(cfg.ADid);
                    if (adCfg != null)
                    {
                        var adInfo = Global.gApp.gSystemMgr.gAdvertiseMgr.Data.GetAdvertiseInfo(adCfg.adPlacementld);
                        var adcfg = AD.Data.Get(cfg.ADid);
                        if (adInfo == null || adInfo.AdvertiseCount < adcfg.adCount)
                        {
                            temp.Add(cfg);
                        }
                        else
                        {
                            tempReward.Add(cfg);
                        }
                    }
                    else
                    {
                        temp.Add(cfg);
                    }
                }
                else
                {
                    var goodsInfo = Global.gApp.gSystemMgr.gPaymentMgr.Data.GetCurShopInfo(cfg.ActivityId, cfg.Id);
                    if (goodsInfo == null)
                    {
                        temp.Add(cfg);
                    }
                    else
                    {
                        if (goodsInfo.Count >= cfg.Inventory)
                        {
                            tempReward.Add(cfg);
                        }
                        else
                        {
                            temp.Add(cfg);
                        }
                    }
                }
            }

            temp.AddRange(tempReward);

            return temp;
        }

        public static List<LDActivityShopDateItem> GetNewCombatPowerActivityShowItem(int activityId)
        {
            NewCombatPowerPackItem[] items = NewCombatPowerPack.Data.items;


            List<LDActivityShopDateItem> temp = new List<LDActivityShopDateItem>();
            List<LDActivityShopDateItem> tempReward = new List<LDActivityShopDateItem>();

            for (int i = 0; i < items.Length; i++)
            {
                NewCombatPowerPackItem cfgItem = items[i];
                if (cfgItem.activityID != activityId)
                    continue;
                LDActivityShopDateItem cfg = new LDActivityShopDateItem(cfgItem);

                if (cfg.ADid > 0)
                {
                    var adCfg = AD.Data.Get(cfg.ADid);
                    if (adCfg != null)
                    {
                        var adInfo = Global.gApp.gSystemMgr.gAdvertiseMgr.Data.GetAdvertiseInfo(adCfg.adPlacementld);
                        var adcfg = AD.Data.Get(cfg.ADid);
                        if (adInfo == null || adInfo.AdvertiseCount < adcfg.adCount)
                        {
                            temp.Add(cfg);
                        }
                        else
                        {
                            tempReward.Add(cfg);
                        }
                    }
                    else
                    {
                        temp.Add(cfg);
                    }
                }
                else
                {
                    var goodsInfo = Global.gApp.gSystemMgr.gPaymentMgr.Data.GetCurShopInfo(cfg.ActivityId, cfg.Id);
                    if (goodsInfo == null)
                    {
                        temp.Add(cfg);
                    }
                    else
                    {
                        if (goodsInfo.Count >= cfg.Inventory)
                        {
                            tempReward.Add(cfg);
                        }
                        else
                        {
                            temp.Add(cfg);
                        }
                    }
                }
            }

            temp.AddRange(tempReward);

            return temp;
        }

        public static List<LDActivityShopDateItem> GetShowItem(int activityId)
        {
            string activityName = LDActivityShopDateItem.GetActivityName(activityId);
            switch (activityName)
            {
                case "DIYActivity":
                case "BattlePowerActivity":
                    return GetDiyActivityShowItem(activityId);
                case "NewCombatPower":
                    return GetNewCombatPowerActivityShowItem(activityId);
            }

            return new();
        }

        public static string GetActivityName(int activityId)
        {
            OpenServerActivity.Data.TryGet(activityId, out OpenServerActivityItem cfg, false);
            if (cfg != null)
            {
                return cfg.activityType;
            }

            OpenTimeActivity.Data.TryGet(activityId, out OpenTimeActivityItem cfg1, false);
            if (cfg1 != null)
            {
                return cfg1.activityType;
            }

            return "";
        }
    }
}