using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public class LDCommonTools
    {
        public static int GetItemId(string clpiStr)
        {
            string[] args = LDCommonTools.Split(clpiStr);
            return LDParseTools.IntParse(args[1]);
        }
        public static string[] Split(string info, string symbol = "_")
        {
            if (!string.IsNullOrEmpty(info))
            {
                string[] args = info.Split(symbol);
                return args;
            }

            return new string[0];
        }

        public static List<int> SplitToIntList(string info, string symbol = "_")
        {
            if (!string.IsNullOrEmpty(info))
            {
                string[] args = Split(info, symbol);
                List<int> argsInts = new List<int>(args.Length);
                foreach (string _ar in args)
                {
                    if (!LDParseTools.TryIntParse(_ar, out int val))
                    {
                        Global.LogError($"字符串切割 转int错误  参数 = {info}");
                    }

                    argsInts.Add(val);
                }

                return argsInts;
            }
            else
            {
                return new List<int>();
            }
        }

        public static string[] SplitStringList(string info)
        {
            string[] args = Split(info, ",");
            return args;
        }

        public static List<LDCommonItem> SplitToCommonItemList(string info)
        {
            List<LDCommonItem> vs = new List<LDCommonItem>();
            if (!string.IsNullOrEmpty(info))
            {
                string[] args = SplitStringList(info);
                foreach (string item in args)
                {
                    if (!string.IsNullOrEmpty(item))
                    {
                        vs.Add(new LDCommonItem(item));
                    }
                }
            }

            return vs;
        }


        public static void DestoryChildren(Transform tf)
        {
            if (tf == null)
            {
                return;
            }

            for (int i = 0; i < tf.childCount; i++)
            {
                Global.gApp.gResMgr.DestroyGameObj(tf.GetChild(i).gameObject);
            }
        }

        public static void DestoryChildren(GameObject go)
        {
            DestoryChildren(go.transform);
        }

        /// <summary>
        /// 位运算
        /// </summary>
        /// <param name="number"></param>
        /// <param name="position"></param>
        /// <returns></returns>
        public static bool IsBitSet(int number, int position)
        {
            return (number & (1 << position)) != 0;
        }

        // 解析Drop表配置的奖励
        public static List<LDCommonItem> GetDropRewards(string dropStr)
        {
            string[] lines = LDCommonTools.Split(dropStr);
            int dropId = LDParseTools.IntParse(lines[1]);
            List<LDCommonItem> rewards = new List<LDCommonItem>();
            if (Drop.Data.TryGet(dropId, out DropItem cfg))
            {
                if (!string.IsNullOrEmpty(cfg.reward))
                {
                    string[] rewardStrs = Split(cfg.reward, ",");
                    foreach (string str in rewardStrs)
                    {
                        string[] pars = Split(str);
                        if (pars.Length > 2)
                        {
                            LDCommonItem data = new LDCommonItem(pars[0], LDParseTools.IntParse(pars[1]), LDParseTools.LongParse(pars[2]));
                            rewards.Add(data);
                        }
                    }
                }
            }

            return rewards;
        }

        // 转换成协议用的道具结构
        public static Protocol.ItemInfo ToItemInfo(LDCommonItem item)
        {
            Protocol.ItemInfo itemInfo = new Protocol.ItemInfo();
            itemInfo.Type = item.Type;
            itemInfo.CfgId = item.Id;
            itemInfo.Count = item.Num;
            return itemInfo;
        }

        public static List<Protocol.ItemInfo> ToItemInfoList(List<LDCommonItem> items)
        {
            List<Protocol.ItemInfo> itemInfos = new List<Protocol.ItemInfo>();
            if (items == null || items.Count <= 0)
            {
                return itemInfos;
            }

            foreach (LDCommonItem commonItem in items)
            {
                itemInfos.Add(ToItemInfo(commonItem));
            }

            return itemInfos;
        }


        // Fisher-Yates Shuffle 算法
        public static void Shuffle<T>(List<T> list)
        {
            System.Random random = new System.Random();
            int n = list.Count;

            for (int i = n - 1; i > 0; i--)
            {
                // 生成一个 [0, i] 范围内的随机索引
                int j = random.Next(0, i + 1);

                // 交换 list[i] 和 list[j]
                (list[i], list[j]) = (list[j], list[i]);
            }
        }

        public static void SetLocalPosX(Transform tf, float x)
        {
            if (!tf)
            {
                return;
            }

            Vector3 pos = tf.localPosition;
            pos.x = x;
            tf.localPosition = pos;
        }

        public static void SetLocalPosY(Transform tf, float y)
        {
            if (!tf)
            {
                return;
            }

            Vector3 pos = tf.localPosition;
            pos.y = y;
            tf.localPosition = pos;
        }

        public static void ScrollVeiwVerticalJumpTo(GameObject listNode, int moveToIndex)
        {
            ScrollRect scrollRect = listNode.GetComponent<ScrollRect>();
            if (scrollRect == null)
            {
                Global.LogError($"scrollRect component is null");
                return;
            }
            
            LayoutGroup[] layouts = listNode.gameObject.transform.GetComponentsInChildren<LayoutGroup>();
            foreach (LayoutGroup layout in layouts)
            {
                LayoutRebuilder.ForceRebuildLayoutImmediate(layout.rectTransform());
            }

            RectTransform contentRt = scrollRect.content;
            VerticalLayoutGroup verticalLayoutGroup = contentRt.GetComponent<VerticalLayoutGroup>();
            float itemHeight = 0f;
            Transform itemTf = contentRt.GetChild(0);
            if (itemTf != null)
            {
                itemHeight = itemTf.rectTransform().rect.height;
            }

            float posY = moveToIndex == 0 ? 0 : (itemHeight + verticalLayoutGroup.spacing) * moveToIndex + verticalLayoutGroup.padding.top;
            float maxPosY = contentRt.rect.height - contentRt.parent.rectTransform().rect.height;
            posY = Mathf.Min(maxPosY, posY);
            posY = Mathf.Max(0, posY);
            SetLocalPosY(contentRt, posY);
        }

        public static void ScrollVeiwHorizontalJumpTo(GameObject listNode, int moveToIndex)
        {
            ScrollRect scrollRect = listNode.GetComponent<ScrollRect>();
            if (scrollRect == null)
            {
                Global.LogError($"scrollRect component is null");
                return;
            }
            
            LayoutGroup[] layouts = listNode.gameObject.transform.GetComponentsInChildren<LayoutGroup>();
            foreach (LayoutGroup layout in layouts)
            {
                LayoutRebuilder.ForceRebuildLayoutImmediate(layout.rectTransform());
            }

            RectTransform contentRt = scrollRect.content;
            var horizontalLayoutGroup = contentRt.GetComponent<HorizontalLayoutGroup>();
            float itemWidth = 0f;
            Transform itemTf = contentRt.GetChild(0);
            if (itemTf != null)
            {
                itemWidth = itemTf.rectTransform().rect.width;
            }

            float posX = moveToIndex == 0 ? 0 : (itemWidth + horizontalLayoutGroup.spacing) * moveToIndex + horizontalLayoutGroup.padding.left;
            float maxPosX = contentRt.rect.width - contentRt.parent.rectTransform().rect.width;
            posX *= -1;
            maxPosX *= -1;
            posX = Mathf.Max(maxPosX, posX);
            posX = Mathf.Min(0, posX);
            SetLocalPosX(contentRt, posX);
        }
        
        // 获取字符串 到最后一个非数字字符
        public static string GetSubstringFitstNotDigit(string input)
        {
            string result = "";
            foreach (char c in input)
            {
                if (char.IsDigit(c)) break;
                result += c;
            }
            return result;
        }

        public static Transform FindNode(Transform parent, string path)
        {
            Transform node = parent.Find(path);
            if (node == null)
            {
                Global.LogError($"{path} not found in {parent.name}");
            }

            return node;
        }

    }
}