using System;
using System.Collections.Generic;
using System.Linq;
using LD.Protocol;

namespace LD
{
    public class LDNetTeamDTO
    {
        public int ChallengeTimes; //挑战次数
        private int PassMaxDifficult; //当前解锁最大难度PassMaxDifficult
        public int SceneId; //
        public long NextWeekRefreshTime;
        public List<int> PassHistoryMissions = new();
        public int AssistDifficultId;

        private int ChallengeMissionId; //当前房间选择的挑战关卡

        public bool ShowInviteState = false; //显示邀请状态
        public List<LDNetInviteInfo> BeInviteInfos = new();

        public LDNetRoomInfo RoomInfo;
        public LDNetServerInfo ServerInfo;

        public List<long> InvitedPlayerIds = new();

        public void UpdateRoomInfo(RoomInfo roomInfo)
        {
            if (RoomInfo == null)
            {
                RoomInfo = new LDNetRoomInfo(roomInfo);
            }
            else
            {
                RoomInfo.UpdateInfo(roomInfo);
            }
        }

        public void ClearRoomInfo()
        {
            RoomInfo = null;
            Global.gApp.gSystemMgr.gChatMgr.ClearTeamRed();
        }

        public void AddPassMissionHistory(int missionId)
        {
            if (!PassHistoryMissions.Contains(missionId))
            {
                PassHistoryMissions.Add(missionId);
            }
        }

        public bool CheckIsPassHistory(int missionId)
        {
            return PassHistoryMissions.Contains(missionId);
        }

        #region 挑战关卡信息

        public int GetPassMaxDifficuty()
        {
            return this.PassMaxDifficult;
        }

        public void ChangeUnlockDifficulty(int difficulty)
        {
            this.PassMaxDifficult = difficulty;
        }


        public int GetCurMissionId()
        {
            if (ChallengeMissionId <= 0)
            {
                if (this.RoomInfo != null)
                {
                    ChallengeMissionId = this.RoomInfo.RoomBriefInfo.MissionId;
                }
                else
                {
                    var passedMissionIndex = 0;
                    var sceneCfg = TeamInstance.Data.Get(SceneId);
                    if (sceneCfg != null)
                    {
                        var power = Global.gApp.gSystemMgr.gRoleMgr.GetHistoryMaxPowerNum();
                        for (int i = sceneCfg.difficultyGroup.Length - 1; i >= 0; i--)
                        {
                            var missionCfg = TeamInstanceMission.Data.Get(sceneCfg.difficultyGroup[i]);
                            if (power >= missionCfg.unlockPowerNum)
                            {
                                passedMissionIndex = i;
                                break;
                            }
                        }

                        ChallengeMissionId = sceneCfg.difficultyGroup[passedMissionIndex];
                    }
                }
            }

            return ChallengeMissionId;
        }

        public bool CheckMissionIsUnlock(int missionId, long power)
        {
            var cfg = TeamInstanceMission.Data.Get(missionId);
            if (cfg != null)
            {
                bool isUnlock = power >= cfg.unlockPowerNum;
                return isUnlock;
            }

            return false;
        }

        public void ChangeCurChallengeMissionId(int missionId)
        {
            if (this.RoomInfo != null)
            {
                this.RoomInfo.RoomBriefInfo.MissionId = missionId;
            }

            ChallengeMissionId = missionId;
        }

        #endregion

        #region 被邀请信息

        public bool AddInviteInfo(LDNetInviteInfo inviteInfo)
        {
            foreach (LDNetInviteInfo ldNetInviteInfo in BeInviteInfos)
            {
                if (ldNetInviteInfo.InvitePlayer.Role.BriefRole.PlayerId == inviteInfo.InvitePlayer.Role.BriefRole.PlayerId &&
                    ldNetInviteInfo.Room.RoomBriefInfo.RoomId == inviteInfo.Room.RoomBriefInfo.RoomId)
                {
                    if (!ldNetInviteInfo.Room.RoomBriefInfo.MissionId.Equals(inviteInfo.Room.RoomBriefInfo.MissionId))
                    {
                        ldNetInviteInfo.Room.RoomBriefInfo.MissionId = inviteInfo.Room.RoomBriefInfo.MissionId;
                    }

                    return true;
                }
            }

            BeInviteInfos.Insert(0, inviteInfo);
            return false;
        }

        public void RemoveInviteInfo(long invitePlayer, long roomId)
        {
            foreach (LDNetInviteInfo info in BeInviteInfos)
            {
                if (info.InvitePlayer.Role.BriefRole.PlayerId == invitePlayer && info.Room.RoomBriefInfo.RoomId == roomId)
                {
                    BeInviteInfos.Remove(info);
                    break;
                }
            }
        }

        public void ClearBeInviteInfo()
        {
            BeInviteInfos.Clear();
        }

        #endregion

        #region 邀请数据

        public void AddInvitePlayerIds(List<long> ids)
        {
            InvitedPlayerIds.AddRange(ids);
        }

        public void AddInvitePlayerIds(long id)
        {
            InvitedPlayerIds.Add(id);
        }

        public void ClearInvitePlayerIds()
        {
            InvitedPlayerIds.Clear();
        }


        public bool CheckIsBeInvited(long playerId)
        {
            return InvitedPlayerIds.Contains(playerId);
        }

        #endregion

        #region 刷新 时间问题

        private int GetRefreshOffsetTime()
        {
            var globalCfg = GlobalCfg.Data.Get(80119);
            int beforeTime = globalCfg.valueInt * DateTimeUtil.Minute_Mills;
            return beforeTime;
        }

        /// <summary>
        /// 等待刷新中
        /// </summary>
        /// <returns></returns>
        public bool IsCooling()
        {
            var nextTime = this.NextWeekRefreshTime;
            int beforeTime = GetRefreshOffsetTime();
            var coolingBeginTime = nextTime - beforeTime;
            if (coolingBeginTime <= DateTimeUtil.GetServerTime())
            {
                return true;
            }

            if (this.NextWeekRefreshTime + beforeTime - DateTimeUtil.GetServerTime() >= 7 * DateTimeUtil.Day_Mills)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 刷新时间
        /// </summary>
        /// <returns></returns>
        public long GetRefreshTime()
        {
            var nextTime = this.NextWeekRefreshTime;
            int beforeTime = GetRefreshOffsetTime();
            var curTime = DateTimeUtil.GetServerTime();
            var coolingBeginTime = nextTime - beforeTime;
            if (curTime > coolingBeginTime && curTime < nextTime + beforeTime)
            {
                return nextTime + beforeTime;
            }
            else
            {
                return (this.NextWeekRefreshTime + beforeTime - 7 * DateTimeUtil.Day_Mills);
            }
        }

        /// <summary>
        /// 真实刷新时间
        /// </summary>
        /// <returns></returns>
        public long GetRealRefreshTime()
        {
            var nextTime = this.NextWeekRefreshTime;
            int beforeTime = GetRefreshOffsetTime();
            var curTime = DateTimeUtil.GetServerTime();
            if (this.NextWeekRefreshTime + beforeTime - curTime >= 7 * DateTimeUtil.Day_Mills)
            {
                return this.NextWeekRefreshTime + beforeTime - 7 * DateTimeUtil.Day_Mills;
            }
            else
            {
                return this.NextWeekRefreshTime - beforeTime;
            }
        }

        #endregion
    }


    public class LDNetRoomBriefInfo
    {
        public long RoomId;
        public long CreateTime;
        public int CombatId;
        public int MissionId;
        public long OwnerId;

        public LDNetRoomBriefInfo()
        {
        }

        public LDNetRoomBriefInfo(RoomBriefInfo roomInfo)
        {
            UpdateInfo(roomInfo);
        }

        public void UpdateInfo(RoomBriefInfo roomInfo)
        {
            RoomId = roomInfo.RoomId;
            CreateTime = roomInfo.CreateTime;
            // CombatId = roomInfo.CombatId;
            MissionId = roomInfo.MissionId;
            OwnerId = roomInfo.OwnerId;
        }

        public Protocol.RoomBriefInfo Convert()
        {
            RoomBriefInfo roomBriefInfo = new RoomBriefInfo();
            roomBriefInfo.RoomId = RoomId;
            roomBriefInfo.CreateTime = CreateTime;
            roomBriefInfo.MissionId = MissionId;
            roomBriefInfo.OwnerId = OwnerId;
            return roomBriefInfo;
        }
    }


    public class LDNetFighter
    {
        public LDRoleInfoDTO BriefRole;
        public List<LDDIYData> Model;
        public List<LDNetRoleMechaInfo> MechaInfos;
        public List<LdNetRoleUAVInfo> UAVInfos = new List<LdNetRoleUAVInfo>();
        public List<LDNetRoleDiyPartInfo> PartInfos = new List<LDNetRoleDiyPartInfo>();
        public List<LDNetRoleDriverInfo> DriverInfos = new List<LDNetRoleDriverInfo>();
        public LDNetRoleGuildInfo GuildInfo;
        public List<LDNetEquipInfo> EquipInfo = new();
        public List<LDNetRoleHeadBoxInfo> HeadBoxInfos = new();
        public List<LDNetRoleMechaAssistSlotInfo> AssistMechas = new();
        public List<LDNetAircraftInfo> AircraftInfos = new List<LDNetAircraftInfo>();

        public LDNetFighter()
        {
        }

        public LDNetFighter(Fighter fighter)
        {
            BriefRole = new LDRoleInfoDTO(fighter.BriefRole);
            Model = new List<LDDIYData>();
            foreach (var model in fighter.Model)
            {
                Model.Add(LDDIYData.Convert(model));
            }

            MechaInfos = new List<LDNetRoleMechaInfo>();
            foreach (var mecha in fighter.MechaInfo)
            {
                MechaInfos.Add(new LDNetRoleMechaInfo(mecha));
            }

            foreach (var uav in fighter.UavInfo)
            {
                UAVInfos.Add(new LdNetRoleUAVInfo(uav));
            }

            foreach (var part in fighter.Parts)
            {
                PartInfos.Add(new LDNetRoleDiyPartInfo(part));
            }

            foreach (var driver in fighter.Drivers)
            {
                DriverInfos.Add(new LDNetRoleDriverInfo(driver));
            }

            if (fighter.GuildInfo != null)
            {
                GuildInfo = new LDNetRoleGuildInfo(fighter.GuildInfo);
            }

            if (fighter.EquipInfo != null)
            {
                foreach (EquipInfo info in fighter.EquipInfo)
                {
                    EquipInfo.Add(new LDNetEquipInfo(info));
                }
            }

            if (fighter.HeadBox != null)
            {
                foreach (RoleHeadBoxInfo boxInfo in fighter.HeadBox)
                {
                    HeadBoxInfos.Add(new LDNetRoleHeadBoxInfo() { HeadBox = boxInfo.HeadBox });
                }
            }

            if (fighter.AssistMechas != null)
            {
                foreach (var assistMecha in fighter.AssistMechas)
                {
                    this.AssistMechas.Add(new LDNetRoleMechaAssistSlotInfo(assistMecha.SlotId, assistMecha.MechaId, assistMecha.Level, assistMecha.SkinId, assistMecha.Hide));
                }
            }

            if (fighter.AircraftInfos != null)
            {
                foreach (AircraftInfo aircraftInfo in fighter.AircraftInfos)
                {
                    AircraftInfos.Add(new LDNetAircraftInfo(aircraftInfo));
                }
            }

            ResetModeSkin();
        }

        public Fighter Convert()
        {
            Fighter fighter = new Fighter();
            fighter.BriefRole = BriefRole.Convert();
            foreach (var model in Model)
            {
                fighter.Model.Add(model.Convert());
            }

            foreach (var mecha in MechaInfos)
            {
                fighter.MechaInfo.Add(mecha.Convert());
            }

            foreach (var uav in UAVInfos)
            {
                fighter.UavInfo.Add(uav.Convert());
            }

            foreach (var part in PartInfos)
            {
                fighter.Parts.Add(part.Convert());
            }

            foreach (var driver in DriverInfos)
            {
                fighter.Drivers.Add(driver.Convert());
            }

            if (GuildInfo != null)
            {
                fighter.GuildInfo = GuildInfo.Convert();
            }

            foreach (LDNetEquipInfo info in EquipInfo)
            {
                fighter.EquipInfo.Add(info.Convert());
            }

            return fighter;
        }

        public LDNetRoleMechaInfo GetMechaInfo()
        {
            int mechaId = Model.Count > 0 ? Model[0].BodyId : -1;
            foreach (LDNetRoleMechaInfo mechaInfo in MechaInfos)
            {
                if (mechaInfo.CfgId == mechaId)
                {
                    return mechaInfo;
                }
            }

            return null;
        }

        public void ResetModeSkin()
        {
            if (!(Model.Count > 0))
                return;
            foreach (LDNetRoleMechaInfo mechaInfo in MechaInfos)
            {
                if (Model[0].BodyId == mechaInfo.CfgId)
                {
                    Model[0].BodySkinId = mechaInfo.SkinId;
                    Model[0].HidBodySkinId = mechaInfo.HideSkin ? 1 : 0;
                    break;
                }
            }

            foreach (LDNetRoleDiyPartInfo netPartInfo in PartInfos)
            {
                foreach (LDDIYPartData partData in Model[0].Data)
                {
                    {
                        if (partData.PId == netPartInfo.CfgId)
                        {
                            partData.SId = netPartInfo.SkinId;
                            partData.HSId = netPartInfo.HideSkin;
                            break;
                        }
                    }
                }
            }
        }

        public List<LDNetAttrEffect> GetAllExtraEffect()
        {
            Dictionary<int, LDNetAttrEffect> temp = new();

            #region 装备效果

            List<LDNetAttrEffect> allEquipTempEffect = new();
            foreach (LDNetEquipInfo netEquipInfo in EquipInfo)
            {
                allEquipTempEffect.AddRange(netEquipInfo.EquipEffects);
            }

            foreach (LDNetAttrEffect effect in allEquipTempEffect)
            {
                if (temp.TryGetValue(effect.EffectId, out var tempEffect))
                {
                    tempEffect.EffectValue += effect.EffectValue;
                }
                else
                {
                    temp.Add(effect.EffectId, new LDNetAttrEffect(effect.EffectId, effect.EffectValue));
                }
            }

            #endregion


            #region 头像带来的效果

            var headEffect = Global.gApp.gSystemMgr.gAttrEffectMgr.GetHeadBoxEffect(HeadBoxInfos);
            foreach (KeyValuePair<int, LDNetAttrEffect> effect in headEffect)
            {
                if (temp.TryGetValue(effect.Key, out var tempEffect))
                {
                    tempEffect.EffectValue += effect.Value.EffectValue;
                }
                else
                {
                    temp.Add(effect.Key, effect.Value);
                }
            }

            #endregion


            return temp.Values.ToList();
        }

        public List<LDNetAttrEffect> GetAircraftEffect()
        {
            Dictionary<int, LDNetAttrEffect> temp = new();
            List<LDNetAttrEffect> allTempEffect = new();
            foreach (LDNetAircraftInfo info in AircraftInfos)
            {
                allTempEffect.AddRange(info.AircraftRefinementInfo.Effects);
            }

            foreach (LDNetAttrEffect effect in allTempEffect)
            {
                if (temp.TryGetValue(effect.EffectId, out var tempEffect))
                {
                    tempEffect.EffectValue += effect.EffectValue;
                }
                else
                {
                    temp.Add(effect.EffectId, new LDNetAttrEffect(effect.EffectId, effect.EffectValue));
                }
            }

            return temp.Values.ToList();
        }

        public List<LDNetAttrEffect> GetValidateEffect(int gameType, List<LDNetAttrEffect> allEffect)
        {
            return Global.gApp.gSystemMgr.gAttrEffectMgr.GetValidateEffect(gameType, allEffect);
        }


        public List<AssistBattleInfo> GetAssistBattleInfos()
        {
            List<AssistBattleInfo> battleInfoList = new();
            var assistMechaList = AssistMecha.Data.items;
            foreach (var assistMecha in this.AssistMechas)
            {
                if (assistMecha.MechaId > 0)
                {
                    AssistBattleInfo info = new();
                    info.MechaId = assistMecha.MechaId;
                    info.SlotId = assistMecha.SlotId;
                    info.SkinId = assistMecha.SkinId;
                    info.HideSkin = assistMecha.HideSkin ? 1 : 0;

                    var assistLv = Global.gApp.gSystemMgr.gAssistMgr.GetAssistMechaSkill(assistMecha.MechaId, assistMecha.Level);
                    if (assistLv != null)
                    {
                        foreach (string s in assistLv.ciTiaoID)
                        {
                            info.CitiaoList.Add(LDParseTools.IntParse(s));
                        }
                    }

                    foreach (AssistMechaItem item in assistMechaList)
                    {
                        if (item.mechaID == assistMecha.MechaId)
                        {
                            info.AssistMechaId = item.id;
                            // info.AdmissionCD = item.AdmissionCD;
                            // info.Duration = item.duration;
                            break;
                        }
                    }

                    battleInfoList.Add(info);
                }
            }

            battleInfoList.Sort((a, b) => (int)(a.SlotId - b.SlotId));
            return battleInfoList;
        }

        public long GetMaxPower()
        {
            long power = -1;
            foreach (LDNetRoleMechaInfo mechaInfo in MechaInfos)
            {
                if (mechaInfo.Power > power)
                {
                    power = mechaInfo.Power;
                }
            }

            foreach (LDNetEquipInfo equipInfo in EquipInfo)
            {
                power += (long)equipInfo.GetEquipPower();
            }

            return Math.Max(power, 0);
        }

        public string GetMaxPowerStr()
        {
            return UiTools.FormateMoney(GetMaxPower());
        }
    }

    public class LDNetShowFigher
    {
        public LDNetFighter Fighter;
        public LDRoleAirCraftFormation Formation;

        public LDNetShowFigher(Fighter fighter, RoleAirCraftFormation info)
        {
            Sync(fighter, info);
        }

        public void Sync(Fighter fighter, RoleAirCraftFormation info)
        {
            Fighter = new LDNetFighter(fighter);
            Formation = new LDRoleAirCraftFormation(info);
        }
    }

    public class LDNetRoomRole
    {
        public LDNetFighter Role;
        public bool AIRole = false;

        public LDNetRoomRole()
        {
        }

        public LDNetRoomRole(RoomRole role)
        {
            Role = new LDNetFighter(role.Role);
            AIRole = (role.Type == 1);
        }

        public RoomRole Convert()
        {
            RoomRole roomRole = new RoomRole();
            roomRole.Role = Role.Convert();
            return roomRole;
        }
    }

    public class LDNetRoomInfo
    {
        public LDNetRoomBriefInfo RoomBriefInfo;
        public List<LDNetRoomRole> Roles = new List<LDNetRoomRole>();
        public int Status; //房间状态 1在战斗中
        public LDNetServerInfo BsInfo; //如果是战斗状态，会有战斗服的地址

        public LDNetRoomInfo()
        {
        }

        public LDNetRoomInfo(RoomInfo roomInfo)
        {
            UpdateInfo(roomInfo);
        }

        public void UpdateInfo(RoomInfo roomInfo)
        {
            if (RoomBriefInfo == null)
            {
                RoomBriefInfo = new LDNetRoomBriefInfo(roomInfo.RoomBrief);
            }
            else
            {
                RoomBriefInfo.UpdateInfo(roomInfo.RoomBrief);
            }

            Roles.Clear();
            foreach (var role in roomInfo.Roles)
            {
                Roles.Add(new LDNetRoomRole(role));
            }

            Status = roomInfo.Status;
            if (roomInfo.BsInfo != null)
            {
                BsInfo = new LDNetServerInfo(roomInfo.BsInfo.ServerId, roomInfo.BsInfo.Ip, roomInfo.BsInfo.Port);
            }
        }

        public void AddMember(LDNetRoomRole role)
        {
            Roles.Add(role);
        }

        public void KickoutMember(long memberId)
        {
            foreach (LDNetRoomRole role in Roles)
            {
                if (role.Role.BriefRole.PlayerId == memberId)
                {
                    Roles.Remove(role);
                    break;
                }
            }
        }

        public LDNetRoomRole GetRoomRole(bool isOwner)
        {
            foreach (LDNetRoomRole role in Roles)
            {
                if ((role.Role.BriefRole.PlayerId == RoomBriefInfo.OwnerId && isOwner) ||
                    role.Role.BriefRole.PlayerId != RoomBriefInfo.OwnerId && !isOwner)
                {
                    return role;
                }
            }

            return null;
        }

        public LDNetRoomRole GetRoomRole(long uid)
        {
            foreach (LDNetRoomRole role in Roles)
            {
                if (role.Role.BriefRole.PlayerId == uid)
                {
                    return role;
                }
            }

            return null;
        }

        public bool IsLeader()
        {
            return Global.gApp.gSystemMgr.gRoleMgr.GetPlayerId() == RoomBriefInfo.OwnerId;
        }

        public Protocol.RoomInfo Convert()
        {
            RoomInfo roomInfo = new RoomInfo();
            roomInfo.RoomBrief = RoomBriefInfo.Convert();
            foreach (LDNetRoomRole role in Roles)
            {
                roomInfo.Roles.Add(role.Convert());
            }

            return roomInfo;
        }
    }

    public class LDNetInviteInfo
    {
        public LDNetRoomRole InvitePlayer;
        public LDNetRoomInfo Room;

        public LDNetInviteInfo()
        {
        }

        public LDNetInviteInfo(RoomRole invite, RoomInfo room)
        {
            InvitePlayer = new LDNetRoomRole(invite);
            Room = new LDNetRoomInfo(room);
        }

        public LDNetInviteInfo(LDNetRoomRole invitePlayer, LDNetRoomInfo room)
        {
            InvitePlayer = invitePlayer;
            Room = room;
        }
    }

    public class LDNetRoleHeadBoxInfo
    {
        public int HeadBox;
    }

    #region 战斗

    public class LDNetServerInfo
    {
        public int ServerId;
        public string Ip;
        public int Port;

        public LDNetServerInfo(int serverId, string ip, int port)
        {
            ServerId = serverId;
            Ip = ip;
            Port = port;
        }
    }

    /// <summary>
    /// 伤害
    /// </summary>
    public class LDNetCBattleDamage
    {
        public long PlayerId;
        public long Damage;

        public LDNetCBattleDamage(long playerId, long damage)
        {
            PlayerId = playerId;
            Damage = damage;
        }

        public GVECBattleDamage Convert()
        {
            GVECBattleDamage damage = new GVECBattleDamage();
            damage.PlayerId = PlayerId;
            damage.Damage = Damage;
            return damage;
        }
    }

    /// <summary>
    /// 战斗结果
    /// </summary>
    public class LDNetGVECBattleResult
    {
        public int Result;
        public List<LDNetCBattleDamage> Damages;
        public long UseTime;

        public LDNetGVECBattleResult(int result, List<LDNetCBattleDamage> damages, long useTime)
        {
            Result = result;
            Damages = damages;
            UseTime = useTime;
        }

        public GVECBattleResult Convert()
        {
            GVECBattleResult result = new GVECBattleResult();
            result.Result = Result;
            result.UseTime = UseTime;
            foreach (LDNetCBattleDamage damage in Damages)
            {
                result.Damages.Add(damage.Convert());
            }

            return result;
        }
    }

    #endregion
}