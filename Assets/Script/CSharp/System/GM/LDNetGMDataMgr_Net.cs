using Google.Protobuf;
using LD.Protocol;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetGMDataMgr
    {
        protected override void InitNetImp()
        {
            AddNetHandler(Protocol.opcode.GmResponse);
            AddNetHandlerSymmetry(Protocol.opcode.GmShowRequest, Protocol.opcode.GmShowResponse);
        }
        public override IMessage HandleNetMsg(int code, opcode s2c_msgId, byte[] data)
        {
            if (s2c_msgId == opcode.GmResponse)
            {
                GMResponse gmResponse = GMResponse.Parser.ParseFrom(data);
                return gmResponse;
            }
            else if (s2c_msgId == opcode.GmShowResponse)
            {
                GMShowResponse info = GMShowResponse.Parser.ParseFrom(data);
                GMHelps.Clear();
                foreach (string str in info.Text)
                {
                    GMHelps.Add(str);
                }
                Global.gApp.gMsgDispatcher.SendUIMsg(LDUICfg.GMUI);
                return info;
            }
            return null;
        }
        public void SendGMCommand(string command)
        {
            if (RuntimeSettings.Release)
            {
                return;
            }
            GMRequest req = new GMRequest();
            req.Msg = command;
            Send(opcode.GmRequest, req);
        }
        public void SendGMHelpCommand()
        {
            if (RuntimeSettings.Release)
            {
                return;
            }
            GMShowRequest req = new GMShowRequest();
            Send(opcode.GmShowRequest, req);
        }

        protected override void ClearNetDataImp()
        {

        }
    }
}
