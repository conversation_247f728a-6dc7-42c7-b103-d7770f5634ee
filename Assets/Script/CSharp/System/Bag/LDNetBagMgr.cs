using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public partial class LDNetBagMgr : LDNetDataMgr
    {
        protected override void InitImp()
        {
        }
        /// <summary>
        /// 0=不出现红点；
        ///1=该角色终身首次获取时出现红点；
        //2=该角色每日首次获取时出现红点
        //3=一直有红点
        /// </summary>
        /// <param name="netBagDTOItem"></param>
        private void CoverItem(LDNetBagDTOItem netBagDTOItem)
        {
            Data.NetNormalBagDTOItems[netBagDTOItem.CfgId] = netBagDTOItem;
            if (PlayerAssembleFinishResponse)
            {
                Global.gApp.gSystemMgr.gNetClientDataMgr.CoverNewItems(netBagDTOItem);
            }
        }
        public override bool IsUnlock(bool isShowLockTips = false)
        {
            return CheckModuleOpen(LDSystemEnum.Bag, isShowLockTips);
        }

        public bool ItemValid(int cfgId)
        {
            LDNetBagDTOItem item = Data.GetNetBagDTOItem(cfgId);
            if (item != null)
            {
                if (item.Count > 0)
                {
                    return item.TimeValid();
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        public List<LDNetBagDTOItem> GetNetBagDTOItemsAndSendExpireItems()
        {
            List<LDNetBagDTOItem> netBagDTOItems = new List<LDNetBagDTOItem>();
            // 直接使用这个 在处理
            List<LDNetBagDTOItem> netBagDTOInvalidItems = new List<LDNetBagDTOItem>();

            foreach (KeyValuePair<int,LDNetBagDTOItem> keyValuePair in Data.NetNormalBagDTOItems)
            {
                if (LDCommonItemType.ResItem.Contains(keyValuePair.Value.ItemItem.type)) { continue; }
                if (keyValuePair.Value.Count > 0)
                {
                    if (keyValuePair.Value.TimeValid())
                    {
                        netBagDTOItems.Add(keyValuePair.Value);
                    }
                    else
                    {
                        netBagDTOInvalidItems.Add(keyValuePair.Value);
                    }
                }
            }
            foreach (KeyValuePair<int,Dictionary<long,LDNetBagDTOItem>> itemData in Data.NetBagSpecialDTOItems)
            {
       
                foreach (KeyValuePair<long, LDNetBagDTOItem> keyValuePair in itemData.Value)
                {
                    if (LDCommonItemType.ResItem.Contains(keyValuePair.Value.ItemItem.type)) { continue; }
                    if (keyValuePair.Value.Count > 0)
                    {
                        if (keyValuePair.Value.TimeValid())
                        {
                            netBagDTOItems.Add(keyValuePair.Value);
                        }
                        else
                        {
                            netBagDTOInvalidItems.Add(keyValuePair.Value);
                        }
                    }
                }
            }
            SendItemExpireRegainRequest(netBagDTOInvalidItems);
            netBagDTOItems.Sort();
            return netBagDTOItems;
        }
        public long GetItemCount(int cfgId)
        {
            return Data.GetItemCount(cfgId);
        }
        public string GetItemCountStr(int cfgId)
        {
            return UiTools.FormateMoney(Data.GetItemCount(cfgId));
        }
        public long GetItemPreCount(int cfgId)
        {
            return Data.GetItemPreCount(cfgId);
        }

        public void GetBoxDIYInfo(int piceId,out long curCount,out long dstCount)
        {
            curCount = 0;
            dstCount = -1;
            DIYPartCfgItem item = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetPartCfgItemByPice(piceId);
            if(item != null)
            {
                dstCount = Global.gApp.gSystemMgr.gMechaPartDataMgr.GetQuaChangePiceNeedCount(item.id);
                if(dstCount > 0)
                {
                    curCount = GetItemCount(piceId);
                }
            }
        }

        public void GetBoxMechaInfo(int piceId, out long curCount, out long dstCount)
        {
            curCount = 0;
            dstCount = -1;
            MechaItem item = Global.gApp.gSystemMgr.gMechaDataMgr.GetMechaCfgItemByPice(piceId);
            if (item != null)
            {
                dstCount = Global.gApp.gSystemMgr.gMechaDataMgr.GetQuaChangePiceNeedCount(item.id);
                if (dstCount > 0)
                {
                    curCount = GetItemCount(piceId);
                }
            }
        }
        public override LDRedTipsState GetRedState(LDSystemEnum system)
        {
            foreach (KeyValuePair<int, LDNetBagDTOItem> item in Data.NetNormalBagDTOItems)
            {
                if(Global.gApp.gSystemMgr.gNetClientDataMgr.GetBagItemRedState(item.Value))
                {
                    return LDRedTipsState.RedPoint;
                }
            }
            return LDRedTipsState.None;
        }
        protected override void DestroyImp()
        {
        }
    }
}
