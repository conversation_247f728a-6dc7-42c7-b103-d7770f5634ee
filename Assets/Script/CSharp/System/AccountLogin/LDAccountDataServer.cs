using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDAccountDataServer
    {
        public long createAt = 0;
        public long createTs = 0;
        public long gmPort = 0;
        public bool hide = false;
        public string host = "";
        public long iapPort = 0;
        public int id = 0;
        public string name = string.Empty;
        public long openTimestamp = 0;
        public int order = 0;
        public int port = 0;
        public string proxyIp = string.Empty;
        public long proxyPort = 0;
        public bool proxySwitch = false;
        public int status = 0;
        public long updateAt = 0;
        public long updateTs = 0;
    }
}
