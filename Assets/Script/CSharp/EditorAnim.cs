using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class EditorAnim : MonoBehaviour
    {
        public Animator Animator;
        public string AnimName;
        [Range(0f, 1f)]
        public float Percent = 0;
#if UNITY_EDITOR
        void OnValidate()
        {
            if (!Application.isPlaying && Animator != null && AnimName != null && AnimName != string.Empty)
            { 
                if (Animator.enabled)
                {
                    Animator.Play(AnimName, 0, Percent);
                    Animator.SetLayerWeight(1, 0);
                    Animator.Update(0);
                }
            }
        }
#endif
    }
}
