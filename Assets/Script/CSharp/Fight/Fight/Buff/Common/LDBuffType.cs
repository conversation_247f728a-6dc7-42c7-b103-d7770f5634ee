
using System.Collections.Generic;

namespace LD
{
    public class LDBuffType
    {
        // 增加x 系列伤害101--102-120 系列
        public static int Invincible_1 = 1;              // 无敌 ok
        public static int ExpInc_2 = 2;                      // 经验 提升 Normal ok
        public static int MaxHp_3 = 3;                      // 增加生命上限 MaxHp Control ok
        public static int BodyScale_4 = 4;                  // 角色体型  ok
        public static int MoveSpeed_5 = 5;                  // 移动速度百分比 Normal ok
        public static int AddHp_6 = 6;                          // 恢复生命 Hp Controlok
        public static int Def_7 = 7;                        // 增加 防御 Normal ok
        public static int HpShield_304 = 8;                       // 护盾 Normal ok
        public static int MianShange = 9;                       // 免伤
        public static int GoddessTag = 10;                       // 广矛

        public static int Crit_11 = 11;                        // 暴击
        public static int CritDamageInc_12 = 12;               // 暴击伤害倍率 
        public static int CritHead_13 = 13;               //  爆�^
        public static int HpShield_14 = 14;               //  护盾 有的时候不生效
        public static int AtkShield_15 = 15;               //  抵抗攻击次数护盾
        public static int RemoveShieldVal_16 = 16;               // 清除护盾值 

        public static int NormalAI_Damage_21 = 21;                // 小怪 增伤 Normal ok
        public static int Elite_Damage_22 = 22;               // 精英 增伤 Normal ok
        public static int Boss_Damage_23 = 23;                // boss 增伤 Normal ok
        public static int MianShange_NormalAI = 24;                // 受小怪伤害减免
        public static int MianShange_All = 25;                // 受伤害减免

        public static int AIBeheadedKill_312 = 31;                // 斩杀


        public static int Frozen_Inc_Damage_41 = 41;            // 冰冻 提升伤害
        public static int Burn_Inc_Damage_42 = 42;             // 传染

        public static int MianYIKongZhi = 51;             // 免疫buff


        public static int DamageInc_100 = 100;           // 子弹伤害提升 Normal ok
        public static int BulletScale_101 = 99999;         // 子弹体型 Normal ok
        public static int CitiaoCD_102 = 102;                // 词条冷却
        public static int SubWpnFireCount_103 = 103;         // 次数
        public static int SubWpnMaxClip_104 = 104;           // 副武器弹道 
        public static int CitiaoCD_Time_105 = 105;                // 词条冷却
        public static int DamageIncOnce_106 = 106;           // 子弹伤害提升 Normal ok
        public static int CitiaoCD_Time_107 = 107;           //  词条冷却 一次性
        public static int ClearTriggerCD_108 = 108;           //  清triggercd
        public static int SubWpnFirtDt_109 = 109;           //  副武器开火间隔

        public static int CiitaoCurve_151 = 151;              //散弹数（增加）
        public static int CiitaoCurveTimes_152 = 152;        //散弹数（倍数）
        public static int CiTiaoFireSpeed_153 = 153;        //射速
        public static int CiTiaoFireReload_154 = 154;        //换弹速度
        public static int CiTiaoFireClip_155 = 155;        //弹匣数
        public static int CiTiaoCurveAngle_156 = 156;                 // 散射角度 Normal ok

        public static int CiitaoBullet_157 = 157;              //排数（增加）
        public static int CiitaoBulletTimes_158 = 158;              //排弹数（增加）
        public static int CiTiaoFireClip_159 = 159;              // 弹夹翻倍
        public static int CiitaoBullet_160 = 160;              //排数（增加）1 号武器
        public static int CiitaoBullet_161 = 161;              //排数（增加） 2号武器
        public static int CiTiaoBulletOffset_162 = 162;              //排数 间隔
        public static int CiTiao206RSpeed_163 = 163;              //206 转速提升
        public static int Add301TriggerRate_164 = 164;              //修改301的概率，增加一倍
        public static int CiTIao206Radius_165 = 165;              //修改301的概率，增加一倍
        public static int LightningChainCount_166 = 166;              //增加闪电链数量
        public static int LightningChainPointEffect_167 = 167;              // 修改闪电链端点特效
        public static int LightningChainCitiao215_168 = 168;              // 词条215 加闪电链
        public static int CitiaoTime216_169 = 169;              // 词条215 修改词条216持续时间
        public static int AddTriggerTimes_170 = 170;              // 修改触发次数

        public static int CiTiaoFireUnConsumBullet_181 = 181;        //概率不消耗子弹
        public static int ReduceTriggerTimes_182 = 182;           //  减少触发次数
        public static int AddBulletClip_183 = 183;           // 加子弹 

        public static int Frozen_201 = 201;                         // 冰冻 Frozen
        public static int Frozen_Time_Inc_202 = 202;               // 冰冻 Frozen
        public static int Burn_211 = 211;                           // 燃烧 Time DamageBuff ok
        public static int Paralysis_221 = 221;                      // 麻痹
        public static int Paralysis_TimeInc_222 = 222;              // 麻痹时间提升
        public static int Poisoned_231 = 231;                       // 中毒时间提升


        public static int DamageOnce_401 = 401;             // 次数
        public static int AIHitDamageZengShang_402 = 402;             // 怪物增伤
        public static int AIHitLight_403 = 403;             // 光辉叠层 buff

        public static int UAVClearCD = 501;                         // UAV cd 
        public static int UAVBulletCount = 502;                     // UAV buf
        public static int UAVBulletCountTimes = 503;                // UAV cd 

        public static int BulletTimeInc_601 = 601;             // 子弹时间（百分比，乘算，与改子弹在同一乘区）
        public static int BulletBounce_602 = 602;             // 子弹弹射（固定值，加算）
        public static int BulletScale_603 = 603;             // 子弹缩放
        public static int BulletBezierBounce_604 = 604;             // 贝塞尔弹射

        public static int SummonCountInc_701 = 701;             // 加词条209同时召唤炮塔数量
        public static int SummonAtkRangeInc_702 = 702;           // 增加召唤物炮塔最大索敌范围
        public static int SummonLiveTimeInc_703 = 703;           // 增加召唤物持续时间
        public static int SummonMainTargetDamageInc_704 = 704;    // 对主目标 的增伤
        public static int SummonMainTargetExplode_705 = 705;    // 对主角攻击爆炸标签
        public static int SummonLink_706 = 706;                 // 召唤物链 tag
        public static int SummonRefract_707 = 707;              // 召唤物 折射 次数
        public static int SummonSumExternBullet_708 = 708;      // 召唤物 在炮塔召唤一颗子弹
        public static int SummonSumIncDamageBySummonCount_709 = 709;  // 召唤物额伤害系数问题



        public static int TryggerBuffByHP_1001 = 1001;             // 血量低于 某个值的时候触发一次
        //=================
        public static int Burn_2021 = 99999;                        // 传染

        public static int DamageType102 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType103 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType104 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType105 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType106 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType107 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType108 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType109 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType110 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType111 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType112 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType113 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType114 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType115 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType116 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType117 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType118 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType119 = 99999;              // 子弹伤害类型 Normal ok
        public static int DamageType120 = 99999;              // 子弹伤害类型 Normal ok



        public static int IncAtk_200 = 99999;                      // 增加攻击力

  
        public static int FireSpeed_203 = 99999;                  // 增加攻速 Normal ok
        public static int AtkRange_205 = 99999;                   // 攻击距离 Normal ok
        public static int BulletSpeed_206 = 99999;                // 子弹速度 Normal ok
        public static int BulletCurve_207 = 99999;                // 弹丸弹道 Normal ok
        public static int BulletCurveBaseTimes_2071 = 99999;      // 基础弹丸数翻倍 Normal ok
        public static int BulletCurveIncAndClear_2072 = 99999;     // 弹丸数临时增加 Normal ok

        public static int MaxClip_209 = 99999;                    // 弹夹数   Normal ok
        public static int MaxClip_2091 = 99999;                    // 弹夹倍数   Normal ok
        public static int ReloadClipSpeed_210 = 99999;            // 换弹速度 Normal ok
        public static int ReloadClipSpeedAndClear_2101 = 99999;   // 换弹速度 换弹后 清除 Normal ok
        public static int BulletPenetrate_211 = 9999;            // 子弹穿透 Normal ok
        public static int BulletBounce_212 = 99999;               // 子弹弹射 Normal ok

        public static int BeatBack_214 = 99999;                   // 击退能力 Normal ok
        public static int PickRange_215 = 99999;                  // 拾取范围 Normal ok


        public static int BulletDamage = 99999;                      // 经验 提升 Normal ok



        public static int Crit_2191 = 99999;                      // 暴击 Once
        public static int CritHead_221 = 99999;               //  爆�^



        public static int Curse_303 = 99999;                          // 诅咒 Time DamageBuff ok
        public static int CurseTimeDec_3031 = 99999;                  // 诅咒 Time 减短 
        public static int CurseDamageInc_3022 = 99999;                // 诅咒 伤害 减短 
        public static int CurseDamageExtern_3033 = 99999;             // 被诅咒的敌人，将受到额外的30%伤害 
        public static int Curse_3034 = 99999;                          // 传染


        public static int HeroBulletHitTryAddAIBuff_306 = 399999;      // 子弹打中怪物之后 尝试添加 新的buff Normal ok
        public static int AddDamageBuff_307 = 99999;                  // Time DamageBuff Hp Controlok
        public static int ShiedCD_308 = 99999;                        // Time DamageBuff Hp Controlok
        public static int PenetrateDeadthBullet_309 = 99999;          // 子弹穿透死亡敌人
        public static int NuMouMainBulletDeal_310 = 99999;           // 怒眸将附带武器子弹特效
        public static int NuMouFireSpeed_311 = 99999;                // 怒眸的攻击频率翻倍

        public static int ChangeTargetEffect_313 = 99999;                // 换特效
        public static int Invincible_314 = 99999;                // 免伤2次

        public static int AttrHpV1_101 = 99999;
        public static int AttrHpV2_111 = 99999;
        public static int AttrHpV3_121 = 99999;
        public static int AttrHpV4_131 = 99999;

        public static int AttrAtkV1_102 = 99999;
        public static int AttrAtkV2_112 = 99999;
        public static int AttrAtkV3_122 = 99999;
        public static int AttrAtkV4_132 = 99999;

        public static int AttrDefV1_103 = 99999;
        public static int AttrDefV2_113 = 99999;
        public static int AttrDefV3_123 = 99999;
        public static int AttrDefV4_133 = 99999;


        public static HashSet<int> NormalBuff = new HashSet<int>()
        {
            DamageInc_100,DamageType102,DamageType103,DamageType104,DamageType105,DamageType106,DamageType107,DamageType108,DamageType109,DamageType110,
            DamageType111,DamageType112,DamageType113,DamageType114,DamageType115,DamageType116,DamageType117,DamageType118,DamageType119,DamageType120,
            Elite_Damage_22,Boss_Damage_23,NormalAI_Damage_21,Def_7,FireSpeed_203,MoveSpeed_5,AtkRange_205,BulletSpeed_206,BulletCurve_207,BulletCurveBaseTimes_2071,CiTiaoCurveAngle_156,MaxClip_209,MaxClip_2091,ReloadClipSpeed_210,ReloadClipSpeedAndClear_2101,BulletPenetrate_211,BulletBounce_212,
            BulletScale_101,ExpInc_2,Crit_11,Crit_2191,CritDamageInc_12,CritHead_221,BeatBack_214,PickRange_215,IncAtk_200,CurseTimeDec_3031,CurseDamageInc_3022,CurseDamageExtern_3033,ShiedCD_308,PenetrateDeadthBullet_309,NuMouMainBulletDeal_310,
            NuMouFireSpeed_311,Frozen_Time_Inc_202,Burn_2021,Curse_3034,Invincible_314,BulletCurveIncAndClear_2072,
            CiitaoCurve_151, CiitaoCurveTimes_152, CiTiaoFireSpeed_153, CiTiaoFireReload_154, CiTiaoFireClip_155,CiTiaoCurveAngle_156,CiitaoBullet_157,CiitaoBulletTimes_158,CiTiaoFireClip_159,CiTiaoFireUnConsumBullet_181
        };
    }
}
