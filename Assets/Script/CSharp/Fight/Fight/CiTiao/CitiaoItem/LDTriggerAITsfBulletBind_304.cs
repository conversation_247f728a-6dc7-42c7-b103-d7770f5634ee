using UnityEngine;

namespace LD
{
    public class LDTriggerAITsfBulletBind_304 : LDEventCitiao
    {
        private int BulletRate = -1;
        private double m_DamageParam = 1;
        private int m_BulletCount = 1;
        private int m_BulletId = -1;
        private float m_StartAngleMin = 0;
        private float m_StartAngleMax = 0;
        private float m_DtAngle = 0;
        private float m_Scale = 1;
        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            BulletRate = EZMath.IDDoubleToInt(GetCitiaoParam()[1]);
            m_DamageParam = GetCitiaoParam()[2];
            m_BulletId = EZMath.IDDoubleToInt(GetCitiaoParam()[3]);
            m_BulletCount = EZMath.IDDoubleToInt(GetCitiaoParam()[4]);
            m_StartAngleMin = (float)(GetCitiaoParam()[5]);
            m_StartAngleMax = (float)(GetCitiaoParam()[6]);
            m_DtAngle = (float)(GetCitiaoParam()[7]);
            m_Scale = (float)(GetCitiaoParam()[8]);
        }
        public override void TriggerSucess(int triggerType)
        {
         
        }
        public override void TriggerSucessWithObj(LDHeroPlayer heroPlayer, LDSceneObj AIMonster,LDAtkBullet atkBullet)
        {
            if(!m_Restore && AIMonster != null)
            {
                if (MatchRate())
                {
                    TryAddBullet(AIMonster, atkBullet);
                    base.TriggerSucess(-1);
                }
            }
        }        
        public override void OnTriggerByWeaponSucess(LDBaseWeapon heroWeapon, LDSceneObj monster, LDAtkBullet atkBullet)
        {
            if(!m_Restore && monster != null)
            {
                if (MatchRate())
                {
                    TryAddBullet(monster, atkBullet);
                }
            }
        }
        public override void TriggerSucessWithObj(LDHeroPlayer heroPlayer, LDSceneObj AIMonster)
        {
            if (!m_Restore && AIMonster != null)
            {
                if (MatchRate())
                {
                    TryAddBullet(AIMonster, null);
                }
            }
        }
        private bool MatchRate()
        {
            return RandomMatch(BulletRate * (1 + CitiaoBuffMgr.GetBuffVal(LDBuffType.Add301TriggerRate_164)));
        }
        private void TryAddBullet(LDSceneObj monsterObj,LDAtkBullet atkBullet)
        {
            LDHeroPlayer heroPlayer = CiTiaoMgr.HeroPlayer;
            double atk = heroPlayer.MechaMgr.GetAtk();
            int bulletId = m_BulletId;
            if (bulletId <= 0)
            {
                if (atkBullet != null)
                {
                    bulletId = atkBullet.AtkData.OriBulletId;
                    if (bulletId < 0)
                    {
                        bulletId = atkBullet.BulletItem.id;
                    }
                }
             }
            if(bulletId <= 0)
            {
                Debug.LogError("no BulletId Cfg");
                return;
            }
            LDAIMonster aiMonster = monsterObj as LDAIMonster;
            float angleMonster = 0;
            if (aiMonster != null)
            {
                angleMonster = aiMonster.MoveCtroller.RotateNode.localEulerAngles.y;
            }
            Transform bodyNode = LDFightNodeTools.GetBodyNode(monsterObj);
            float startAngle = RandomUtil.NextFloat(m_StartAngleMin , m_StartAngleMax) + angleMonster;
            startAngle = startAngle - m_DtAngle * (m_BulletCount - 1) / 2;
            for (int i = 0; i < m_BulletCount; i++)
            {
                float offAngle = startAngle + m_DtAngle * i;
                LDTrackBullet bullet = heroPlayer.BulletEmitter.GetBullet(bulletId, atk, CitiaoBuffMgr);
                bullet.Init(bodyNode, offAngle, 0);
                bullet.AtkData.SetOrignalScale(m_Scale);
                bullet.FreshBulletScale(bulletId);
                bullet.AtkData.MulCiTiaoParam(GetDamageParam(m_DamageParam));
                bullet.AtkData.Weapon = atkBullet.AtkData.Weapon;
                bullet.AtkData.SubWeapon = atkBullet.AtkData.SubWeapon;
            }
        }
        public override void TryCacheEffect()
        {
            CacheBulletEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[3]));
        }
    }
}
