
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDHeroMainWeaponCitiao_216 : LDEventCitiao
    {
        private List<LDHeroWeapon> m_ControlWpn = new List<LDHeroWeapon>(2);
        private Animator m_Animator = null;
        
        private bool m_IsFiring;
        private double m_CurTime;
        private LDEffectNodeContent m_TransEffectNode;
        
        public override void Init(LDCiTiaoBaseGroup ciTiaoBaseGroup, LDCiTiaoItemData ciTiaoItemData)
        {
            base.Init(ciTiaoBaseGroup, ciTiaoItemData);
            int partId = CiTiaoMgr.HeroPlayer.DIYData.BodyId;

            MechaItem mechaItem = Mecha.Data.Get(partId);
            LDAttrData attrData = new LDAttrData();
            attrData.InitByMainWeapon(partId);
            int weaponIndex = 1;
            for (int i = 0; i < mechaItem.ExtraArms.Length; i+=3)
            {
                LDWeaponData weaponData = new LDWeaponData();
                weaponData.CitiaoBuffMgr = CitiaoBuffMgr;
                weaponData.PartId = partId;
                weaponData.faceDir = LDParseTools.IntParse(mechaItem.ExtraArms[i + 2]);
                weaponData.InitByCiTiao216(CiTiaoMgr.HeroPlayer, GetCitiaoParam(), CiTiaoMgr.HeroPlayer.transform, attrData);
                LDHeroWeapon heroWeapon = CiTiaoMgr.HeroPlayer.MechaMgr.InitWeapon(weaponIndex, weaponData);
                heroWeapon.AutoFire = false;
                m_ControlWpn.Add(heroWeapon);
                
                if (m_Animator == null)
                {
                    string weaponNodeName = mechaItem.ExtraArms[i];
                    Transform weaponNode = CiTiaoMgr.HeroPlayer.HeroAnimCtrol.DiyHandle.GetMechaModeNode().Find(weaponNodeName);
                    if (weaponNode != null)
                    {
                        Animator[] animators = weaponNode.GetComponentsInChildren<Animator>(); 
                        foreach (Animator ani in animators)
                        {
                            if (ani.gameObject.layer == LDFightConstVal.MechaBody)
                            {
                                m_Animator = ani;
                            }
                            break;
                        }    
                    }
                }

                weaponIndex++;
            }
        }

        public override void TriggerSucess(int triggerType)
        {
            if(m_Restore){return;}
            base.TriggerSucess(triggerType);

            if (m_IsFiring) return;
            
            m_IsFiring = true;
            m_CurTime = 0;
            m_Animator?.Play(LDAnimName.MechaShow, -1, 0);
            
            m_TransEffectNode = Global.gApp.CurFightScene.gEffectMgr.GetFreeEffectNode(GetTransEffectId());
            m_TransEffectNode.transform.SetParent(CiTiaoMgr.HeroPlayer.AtkFight.RotateNode, false);
            m_TransEffectNode.transform.localPosition = Vector3.zero;
            m_TransEffectNode.PlayParticle();
            
            foreach (LDHeroWeapon heroWeapon in m_ControlWpn)
            {
                heroWeapon.AutoFire = true;
            }

            Global.gApp.gMsgDispatcher.Broadcast(MsgIds.MechaDeformation, true);
        }

        public override void OnDUpdate(float dt)
        {
            base.OnDUpdate(dt);
            
            if (m_IsFiring)
            {
                m_CurTime += dt;
                if (LDFightTools.GreaterOrEqualThenZero(m_CurTime, GetFireTime()))
                {
                    m_IsFiring = false;
                    m_Animator?.Play(LDAnimName.MechaClose, -1, 0);
                    TryRecycleEffectNode();
                    foreach (LDHeroWeapon heroWeapon in m_ControlWpn)
                    {
                        heroWeapon.AutoFire = false;
                    }
                    
                    Global.gApp.gMsgDispatcher.Broadcast(MsgIds.MechaDeformation, false);
                }
            }
        }

        public double GetFireTime()
        {
            double buffFireTime = CitiaoBuffMgr.GetBuffVal(LDBuffType.CitiaoTime216_169);
            if (buffFireTime > 0)
            {
                return buffFireTime;
            }

            return GetCitiaoParam()[13];
        }

        public int GetTransEffectId()
        {
            return EZMath.IDDoubleToInt(GetCitiaoParam()[14]);
        }
        

        private void TryRecycleEffectNode()
        {
            if (m_TransEffectNode != null)
            {
                m_TransEffectNode.StopAndRecycleEffect();
                m_TransEffectNode = null;
            }
        }

        public override void TryCacheEffect()
        {
            CacheBulletEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[2]));
            CacheEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[11]));
            CacheEffect(EZMath.IDDoubleToInt(GetCitiaoParam()[14]));
        }

        public override void Destroy()
        {
            TryRecycleEffectNode();
            base.Destroy();
        }
    }
}
