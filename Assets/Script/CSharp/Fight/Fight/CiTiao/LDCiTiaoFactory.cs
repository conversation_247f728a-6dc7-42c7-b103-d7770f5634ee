using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
	public class LDCiTiaoFactory
	{
		public static LDCiTiaoBaseGroup GetCiTiaoGroup(CitiaoGroupItem dYCiTiaoItemData)
		{
			return new LDNormalCitiaoGroup();
		}
		private static int NormalCiTiaoRandom_1 = 100;			// 最普通的就是普通buff
		private static int NormalCiTiao_1 = 101;			// 最普通的就是普通buff
		private static int ChangeBulletInfo = 102;			//
		public static int AddBuffDtReduce_103 = 103;			// 添加buff 按照时间 递减
		public static int BlockBuffDtReduce_104 = 104;			// 防止 buff 递减
		public static int ClearCiTiaoCD = 105;				// 清楚cd
		public static int ReTriggerCiTiao = 106;				// 防止 buff 递减
		public static int AddBuffByXICiTiao = 107;              // 每携带x个指定系部件加1次buff
		private static int TriggerAddHeroHp_17 = 108;           // 事件 + 回血操作 
		private static int TriggerAddBulletDamage_109 = 109; // 事件 + buff操作 
		private static int AddBuffByHeroHP = 110; // 事件 + buff操作 
		private static int AddBulletAndBuff = 111; // 事件 + buff操作 
		private static int AddBuffByOnce = 112; // 事件 + buff操作 
		private static int AddBuffResetByReload_113 = 113; // 事件 + buff操作 
		private static int AddBuffResetByDamage_114 = 114; // 事件 + buff操作 
		private static int AddNewCiTiao = 115; // 事件 +  
		private static int AddGrouCiTiao = 116; //  随机获取词条组里的一个词条 
		private static int CiTiaoChangeNextBullet_111 = 117; // 战斗换子弹
		private static int AddRandomCiTiao = 118; //  随机获取词几个词条
		private static int MulNormalCiTiao_1 = 119; //  Buff 翻倍
		public static int OpenGroupCiTiao = 120; //  打开选择本组词条
		public static int DamageIncByHitCount_121 = 121; // 根据子弹命中数量增伤 
		public static int RandomAddBuff_122 = 122; // 概率加buff 

		public static int HeroMainWeaponCitiao_201 = 201; //   加武器
		public static int SubWeaponCiTiao_202 = 202; // 事件 + 战斗换子弹 
		private static int TriggerHeroTsfBullet_203 = 203; //   事件 + 角色子弹操作 
		private static int AddFrisbeeBullet_102 = 204; // 事件 + 角色子弹操作 
		private static int AddBulletBullet = 205; //  子弹添加子弹
		public static int SubWeaponCiTiao_206 = 206; //  环绕子弹
		private static int ChangeNewBulle_207 = 207; //  更换子弹
		private static int TriggerTrailBullet_208 = 208; // 发射追踪子弹
		public static int SummonWeaponCiTiao_209 = 209; // 召唤炮塔子弹
		private static int TriggerHeroTsfBullet_210 = 210; //  竞技场等 专用

		private static int LDHeroTsfBulletCitiaoPve_211 = 211; //   事件 + 角色子弹操作 
		private static int LDLightningChainCitiao_212 = 212; //   闪电链词条
		private static int AddRowOfBullets_213 = 213; //   发射排弹
		private static int RemoveRoleBuffBullet_214 = 214; //   移除Buff创建子弹
		public static int SubWeaponCiTiao_215 = 215; //   从近到远创建子弹
		public static int HeroMainWeaponCitiao_216 = 216; //  变形开火
		public static int SequenceAddBulletCitiao_217 = 217; // 依次发射子弹 
		public static int AddBulletCitiao_218 = 218; // 由近到远创建子弹  不帮武器 
		public static int SubWeaponRangeAddBullet_219 = 219; // 服务器 随机创建子弹
		public static int TriggerTrailBullet2_220 = 220; // 服务器 随机创建子弹

		private static int TriggerAITsfBullet_301 = 301; // 事件 + 角色子弹操作 
		private static int TriggerAddAIBuff_302 = 302; // 事件 + buff操作 
		private static int TriggerRandomBulletTsfBullet_15 = 303; // 事件 + 角色子弹操作 
		private static int TriggerAITsfBulletBind_304 = 304; // 事件 + 角色子弹操作 子弹绑定到目标身上
		private static int TriggerRangeAddBullet_305 = 305; // 事件 + 角色子弹操作 范围内随机创建


		public static int RandomLvUp_401 = 401; //  随机升级
		public static int AddCitiaoMaxLv = 402; //  等级上限
		private static int GainAllExp = 501; // 吸取经验 


		private static int MonsterDeadthCiTiao_2 = 999992;   // 词条有一些操作 敌人死亡时向周围发射3枚子弹，每枚子弹造成10%伤害
		private static int TriggerAddHeroBuff_11 = 99999; // 事件 + buff操作 


		private static int TriggerHeroTsfBullet_13 = 999999; // 事件 + 角色子弹操作 

		private static int TriggerAITsfBullet_14 = 99999; // 事件 + 角色子弹操作 

		private static int TriggerAddHeroClip_16 = 99999; // 事件 + buff操作 


		private static int TriggerAddNormalAIBuff_19 = 99999; // 事件 + buff操作 
		private static int ChengJie_20 = 99999; //  惩戒
		private static int NuMou_21 = 99999; //  怒眸

		private static int TriggerGainExp_23 = 99999; //   事件 +  吸取经验
		private static int HeroTsfBulletCitiao_24 = 99999; //   事件 +  吸取经验
	
 		private static int HeroTsfBulletCitiao_26 = 99999; //   事件 +  吸取经验


		private static int TriggerAddChangeBullet_104 = 99999; // 事件 + buff操作 
		private static int Citiao602_105 = 9999; // 拥有护盾时，回能及移速+20% 
		private static int Citiao603_106 = 9999; // 拥有护盾时，每秒召唤雷电攻击附近一名敌人，造成0.3倍攻击伤害 
		private static int TimeShield_107 = 9999; // 每各一段时间加一次护盾 
		private static int ReduceShieldTime_108 = 9999; //  减护盾cd
		private static int CiTiao1104_109 = 9999; //  原地攻击时，每次攻击+5%攻速，最多75%，移动后重置
		private static int CiTiao1204_110 = 9999; //  角色受伤后效果重置

		private static int LoopBuffCitiao_112 = 9999; // 循环buff
		private static int RandomCiTiao_113 = 9999; // 结算随机词条
		private static int CiTiaoMaxNexLv_114 = 9999; // 最大等级计算
		private static int SelectCiTiao_115 = 9999; // 随机选词条


		private static int LDHeroTsfBulletCitiao_302 = 9999; // 事件 + 角色子弹操作 
		private static int LDChangeChangeTsf_305 = 9999; //  变形
		private static int CiTiaoChangeNextBullet_306 = 9999; // 事件 + 战斗换子弹 
		private static int SubPet_308 = 9999; //  召唤物
		public static LDCiTiaoItemBase GetCitiaoItem(CitiaoItem citiaoItem, LDCiTiaoMgr ciTiaoMgr)
        {
            LDCiTiaoItemBase ciTiao = null;
			float[] handlyType = citiaoItem.handleType;
			if(handlyType.Length == 0)
            {
				Debug.LogError("citiaoItem handleType Error" + citiaoItem.id);
            }
			int handleType = EZMath.IDDoubleToInt(handlyType[0]);
			if (handleType == NormalCiTiao_1)
            {
				ciTiao = new LDNormalCiTiao_1();
			} 			
			else if (handleType == NormalCiTiaoRandom_1)
            {
				ciTiao = new LDNormalCiTiaoRandom_1();
			} 	 			
			else if (handleType == MulNormalCiTiao_1)
            {
				ciTiao = new LDMulNormalCiTiao_1();
			} 	 		
			else if (handleType == ChangeBulletInfo)
            {
				ciTiao = new LDChangeBulletInfo_102();
			} 	
			else if(handleType == AddBuffDtReduce_103)
            {
				ciTiao = new LDTriggerAddBuffDtReduceCiTiao_103();
			}
			else if(handleType == BlockBuffDtReduce_104)
            {
				ciTiao = new LDTriggerBlockBuffDtReduceCiTiao_104();
			} 			
			else if(handleType == ClearCiTiaoCD)
            {
				ciTiao = new LDTriggerClearCiTiaoCD_105();
			}
			else if (handleType == ReTriggerCiTiao)
			{
				ciTiao = new LDTriggerReTriggerCiTiao_106();
			} 		
			else if (handleType == AddBuffByXICiTiao)
			{
				ciTiao = new LDTriggerAddBuffByXICiTiao_107();
			} 		
			else if (handleType == AddBuffByHeroHP)
			{
				ciTiao = new LDAddBuffByHeroHP_110();
			} 	
			else if (handleType == AddBulletAndBuff)
			{
				ciTiao = new LDAddBulletAndBullet_111();
			}
			else if (handleType == AddBuffByOnce)
			{
				ciTiao = new LDAddBuffByOnce_112();
			} 			
			else if (handleType == AddBuffResetByReload_113)
			{
				ciTiao = new LDAddBuffResetByReload_113();
			}
			else if (handleType == AddBuffResetByDamage_114)
			{
				ciTiao = new LDAddBuffResetByDamage_114();
			} 		
			else if (handleType == AddNewCiTiao)
			{
				ciTiao = new LDAddNewCiTiao_115();
			} 		
			else if (handleType == AddGrouCiTiao)
			{
				ciTiao = new LDAddNewCiTiao_116();
			} 		
			else if (handleType == OpenGroupCiTiao)
			{
				ciTiao = new LDOpenGrouCiTiao_120();
			}  		
			else if (handleType == DamageIncByHitCount_121)
			{
				ciTiao = new LDDamageIncByHitCount_121();
			}  		
			else if (handleType == RandomAddBuff_122)
			{
				ciTiao = new LDRandomAddBuff_122();
			} 			
			else if (handleType == AddRandomCiTiao)
			{
				ciTiao = new LDAddRandomCiTiao_118();
			}
			else if (handleType == LoopBuffCitiao_112)
            {
				ciTiao = new LDLoopBuffCitiao_112();
			} 			
			//else if(handleType == MonsterDeadthCiTiao_2)
   //         {
			//	ciTiao = new LDMonsterTsfBulletCitiao_2();
			//}
			else if(handleType == TriggerAddHeroBuff_11)
            {
				ciTiao = new LDTriggerAddHeroBuffCiTiao_11();
			}
			else if(handleType == TriggerHeroTsfBullet_13)
            {
				ciTiao = new LDHeroTsfBulletCitiao_13();
			} 				
			else if(handleType == HeroTsfBulletCitiao_24)
            {
				ciTiao = new LDHeroTsfBulletCitiao_24();
			} 	
			else if(handleType == HeroTsfBulletCitiao_26)
            {
				ciTiao = new LDHeroTsfBulletCitiao_26();
			} 	
			else if(handleType == HeroMainWeaponCitiao_201)
            {
				ciTiao = new LDHeroMainWeaponCitiao_25();
			}
			else if(handleType == TriggerHeroTsfBullet_203)
            {
				ciTiao = new LDHeroTsfBulletCitiao_13();
            } 	 	
			else if(handleType == LDHeroTsfBulletCitiaoPve_211)
            {
				ciTiao = new LDHeroTsfBulletCitiaoPve_13();
            } 	 	
			else if(handleType == LDLightningChainCitiao_212)
            {
				ciTiao = new LDLightningChainCitiao_212();
            } 	 	
			else if(handleType == AddRowOfBullets_213)
            {
				ciTiao = new LDCitiaoAddRowOfBullets_213();
            } 	 	
			else if(handleType == RemoveRoleBuffBullet_214)
            {
				ciTiao = new LDCitiaoRemoveRoleBuffBullet_214();
            }  	 	
			else if(handleType == SubWeaponCiTiao_215)
            {
				ciTiao = new LDCitiaoSubWeaponCiTiao_215();
            } 			
			else if (handleType == HeroMainWeaponCitiao_216)
			{
				ciTiao = new LDHeroMainWeaponCitiao_216();
			} 				
			else if (handleType == SequenceAddBulletCitiao_217)
			{
				ciTiao = new LDSequenceAddBulletCitiao_217();
			} 					
			else if (handleType == AddBulletCitiao_218)
			{
				ciTiao = new LDAddBulletCitiao_218();
			} 	 					
			else if (handleType == SubWeaponRangeAddBullet_219)
			{
				ciTiao = new LDSubWeaponRangeAddBullet_219();
			} 	
			else if(handleType == AddBulletBullet)
            {
				ciTiao = new LDAddBulletBullet_205();
            } 			
			else if(handleType == TriggerGainExp_23)
            {
				ciTiao = new LDTriggerGainExp_23();
            }
            else if(handleType == TriggerAITsfBullet_14)
            {
				ciTiao = new LDMonsterTsfBulletCitiao_14();
			}         
			
			else if(handleType == TriggerRandomBulletTsfBullet_15)
            {
				ciTiao = new LDAddRandomTsfBulletCitiao_15();
            } 		
			else if(handleType == TriggerTrailBullet_208)
            {
				ciTiao = new LDTriggerTrailBullet_208();
            }
			else if (handleType == TriggerTrailBullet2_220)
			{
				ciTiao = new LDTriggerTrailBullet2_220();
			}
			else if(handleType == TriggerAddAIBuff_302)
            {
				ciTiao = new LDTriggerAddAIBuffCiTiao_12();
			}
			else if(handleType == TriggerAITsfBulletBind_304)
            {
				ciTiao = new LDTriggerAITsfBulletBind_304();
			}
			else if(handleType == TriggerRangeAddBullet_305)
            {
				ciTiao = new LDTriggerRangeAddBullet_305();
			}
			else if(handleType == TriggerAddHeroClip_16)
            {
				ciTiao = new LDTriggerAddClipCiTiao_16();
            }
			else if(handleType == TriggerAddNormalAIBuff_19)
            {
				ciTiao = new LDTriggerAddNormalAIBuffCiTiao_19();
            } 	
			else if(handleType == RandomLvUp_401)
            {
				ciTiao = new LDTriggerRandAddLv_401();
            } 		
			else if(handleType == AddCitiaoMaxLv)
            {
				ciTiao = new LDAddCitiaoMaxLvCiTiao_402();
            } 		 		
			else if(handleType == GainAllExp)
            {
				ciTiao = new LDGainAllExp_501();
            } 		
			else if(handleType == TriggerAddBulletDamage_109)
            {
				ciTiao = new LDTriggerAddBulletDamageCiTiao_109();
            }
			else if(handleType == TriggerAddHeroHp_17)
            {
				ciTiao = new LDTriggerAddHeroHpCiTiao_17();
            } 			
			else if(handleType == TriggerAddChangeBullet_104)
            {
				ciTiao = new LDTriggerAddChangeBulletCiTiao_104();
            } 			
			else if(handleType == Citiao602_105)
            {
				ciTiao = new LDTriggerAddHeroBuff602CiTiao_105();
            } 			
			else if(handleType == Citiao603_106)
            {
				ciTiao = new LDAddRandomTsf603BulletCitiao_106();
            } 			
			else if(handleType == TimeShield_107)
            {
				ciTiao = new LDTimeShieldCiTiao_107();
            } 		
			else if(handleType == ReduceShieldTime_108)
            {
				ciTiao = new LDReduceShieldTimeCiTiao_108();
            } 			
			else if(handleType == ChengJie_20)
            {
				ciTiao = new LDChengJieCitiao_20();
            }
			else if(handleType == CiTiao1104_109)
            {
				ciTiao = new LDTriggerAddHeroBuff1104CiTiao_109();
            } 		
			else if(handleType == CiTiao1204_110)
            {
				ciTiao = new LDTriggerAddHeroBuff1204CiTiao_110();
            } 			
			else if(handleType == NuMou_21)
            {
				ciTiao = new LDNuMouCitiao_21();
            } 			
			else if(handleType == AddFrisbeeBullet_102)
            {
				ciTiao = new LDAddFrisbeeBullet_102();
            }
			else if(handleType == CiTiaoChangeNextBullet_111)
            {
				ciTiao = new LDCiTiaoChangeNextBullet_111(); 
			} 		
			else if (handleType == RandomCiTiao_113)
			{
				ciTiao = new LDRandomCiTiao_113();
			}
			else if (handleType == CiTiaoMaxNexLv_114)
			{
				ciTiao = new LDCiTiaoMaxNexLv_114();
			} 		
			else if (handleType == SelectCiTiao_115)
			{
				ciTiao = new LDSelectCiTiao_115();
			}
			else if (handleType == TriggerAITsfBullet_301)
			{
				ciTiao = new LDMonsterTsfBulletCitiao_301();
			}
			else if (handleType == LDHeroTsfBulletCitiao_302)
			{
				ciTiao = new LDHeroTsfBulletCitiao_302();
			}
			else if (handleType == CiTiaoChangeNextBullet_306)
			{
				ciTiao = new LDCiTiaoChangeNextBullet_306();
			} 			
			else if (handleType == LDChangeChangeTsf_305)
			{
				ciTiao = new LDChangeChangeTsf_305();
			} 	
			else if (handleType == SubWeaponCiTiao_202)
			{
				ciTiao = new LDSubWeaponCiTiao_202();
			} 
			else if (handleType == SubWeaponCiTiao_206)
			{
				ciTiao = new LDSubWeaponCiTiao_206();
			} 		
			else if(handleType == SummonWeaponCiTiao_209)
            {
				ciTiao = new LDSummonWeaponCiTiao_209();
			}
			else if (handleType == ChangeNewBulle_207)
			{
				ciTiao = new LDChangeNewBullet_207();
			}
			else if (handleType == SubPet_308)
			{
				ciTiao = new LDSubPet_308();
			}
			else if (handleType == TriggerHeroTsfBullet_210)
			{
				ciTiao = new LDHeroTsfBulletCitiao_210();
			}
			if (ciTiao != null)
			{
				ciTiao.CiTiaoItem = citiaoItem;
				ciTiao.SetBaseInfo(ciTiaoMgr, handleType);
			}
            else
            {
				Debug.LogError("citiaoItem handleType not exit " + citiaoItem.id);
			}
			return ciTiao;
		}
	}
}
