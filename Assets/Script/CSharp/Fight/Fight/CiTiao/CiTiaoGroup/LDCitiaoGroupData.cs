using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDCitiaoGroupData
    {
        public int CiTiaoGroupId = -1;

        public int MaxLv = 1;

        // only SelectCiTiao
        public List<int> LvUpCiTiao = new List<int>();
        public List<int> AllSelectCiTiao = new List<int>(); // 战斗里面才有 值。 否则用LvUpCiTiao
        private Dictionary<int, int> m_PartXiCount = new Dictionary<int, int>();
        private LDDIYData m_DIYData;
        private Func<int, bool> m_SelectFunc;

        public LDCitiaoGroupData(LDDIYData DIYData, Func<int, bool> act)
        {
            m_DIYData = DIYData;
            m_SelectFunc = act;
        }

        public void Init(int groupId)
        {
            CiTiaoGroupId = groupId;
            CitiaoGroupItem citiaoGroupItem = CitiaoGroup.Data.Get(CiTiaoGroupId);
            MaxLv = citiaoGroupItem.maxLv;
            InitXiData();
        }

        public void AddMaxLv(int addLv)
        {
            MaxLv = MaxLv + addLv;
        }

        public void TryCitiaoEffect(List<LDExpeditionCiTiaoDataItem> groups, int mechaId, List<int> citiaoIds)
        {
            foreach (int citiaoID in LvUpCiTiao)
            {
                TryCitiaoEffectImp(groups, mechaId, citiaoID);
            }

            foreach (int id in citiaoIds)
            {
                CitiaoItem citiaoItem = Citiao.Data.Get(id);
                if (citiaoItem.initialValid == 1)
                {
                    TryCitiaoEffectImp(groups, mechaId, id);
                }
            }
        }

        public void TryCitiaoEffectImp(List<LDExpeditionCiTiaoDataItem> groups, int mechaId, int citiaoID)
        {
            CitiaoItem citiaoItem = Citiao.Data.Get(citiaoID);
            float[] handlyType = citiaoItem.handleType;
            if (handlyType.Length == 0)
            {
                Debug.LogError("citiaoItem handleType Error" + citiaoItem.id);
                return;
            }

            if (EZMath.IDDoubleToInt(handlyType[0]) == LDCiTiaoFactory.AddCitiaoMaxLv)
            {
                int externLv = EZMath.IDFloatToInt(handlyType[1]);

                for (int i = 2; i < handlyType.Length; i += 1)
                {
                    int groupId = EZMath.IDDoubleToInt(handlyType[i]);
                    if (groupId > 0)
                    {
                        foreach (LDExpeditionCiTiaoDataItem info in groups)
                        {
                            if (info.CiTiaoGroupData.CiTiaoGroupId == groupId)
                            {
                                info.CiTiaoGroupData.AddMaxLv(externLv);
                            }
                        }
                    }
                    else
                    {
                        MechaItem mechaItem = Mecha.Data.Get(mechaId);

                        foreach (LDExpeditionCiTiaoDataItem info in groups)
                        {
                            if (info.CiTiaoGroupData.CiTiaoGroupId == mechaItem.EntryPhrases)
                            {
                                info.CiTiaoGroupData.AddMaxLv(externLv);
                            }
                        }
                    }
                }
            }
        }

        public LDDIYData GetDIYData()
        {
            return m_DIYData;
        }

        public void InitByExepdition(int groupId, List<int> allSelectCiTiao)
        {
            Init(groupId);

            // TODO: dqx 计算最大等级问题   远征会改最大等级
            CitiaoGroupItem citiaoGroupItem = CitiaoGroup.Data.Get(CiTiaoGroupId);
            MaxLv = citiaoGroupItem.maxLv;

            AllSelectCiTiao = allSelectCiTiao;
            foreach (int citiaoId in allSelectCiTiao)
            {
                CitiaoItem citiaoItem = Citiao.Data.Get(citiaoId);
                if (citiaoItem.initialValid == 0)
                {
                    if (citiaoItem.citiaoType != LDCiTiaoBaseGroup.EliteType)
                    {
                        if (!LvUpCiTiao.Contains(citiaoId))
                        {
                            LvUpCiTiao.Add(citiaoId);
                        }
                    }
                }
            }
        }

        private bool IsSelectedByAll(int citiaoId)
        {
            return m_SelectFunc(citiaoId);
        }

        public bool IsSelect(int citiaoId)
        {
            CitiaoItem citiaoItem = Citiao.Data.Get(citiaoId);
            if (citiaoItem.initialValid != 0 || AllSelectCiTiao.Contains(citiaoId) ||
                LvUpCiTiao.Contains(citiaoId))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        private void InitXiData()
        {
            foreach (LDDIYPartData partData in m_DIYData.Data)
            {
                DIYPartCfgItem diyPartItem = DIYPartCfg.Data.Get(partData.PId);
                int xiType = diyPartItem.element;
                if (xiType > 0)
                {
                    if (m_PartXiCount.ContainsKey(xiType))
                    {
                        m_PartXiCount[xiType] = m_PartXiCount[xiType] + 1;
                    }
                    else
                    {
                        m_PartXiCount[xiType] = 1;
                    }
                }
            }
        }

        public int GetXiCount(int xiType)
        {
            if (m_PartXiCount.ContainsKey(xiType))
            {
                return m_PartXiCount[xiType];
            }
            else
            {
                return 0;
            }
        }

        public void AddSelectCiTiao(int citiaoId)
        {
            if (!AllSelectCiTiao.Contains(citiaoId))
            {
                AllSelectCiTiao.Add(citiaoId);
            }
        }

        public void CiTiaoLvUp(int citiaoId)
        {
            LvUpCiTiao.Add(citiaoId);
        }

        public int GetLv()
        {
            return LvUpCiTiao.Count;
        }

        public bool CanSelect(int citiaoId, int dstCiTiaoType)
        {
            if (IsSelect(citiaoId))
            {
                return false;
            }

            CitiaoItem citiaoItem = Citiao.Data.Get(citiaoId);
            if (citiaoItem.weight <= 0)
            {
                return false;
            }

            CitiaoGroupItem citiaoGroupItem = CitiaoGroup.Data.Get(CiTiaoGroupId);
            //----------------------------------------------------------------- 在这个地方穷举 。不单独封装了
            if (citiaoItem.citiaoType != dstCiTiaoType)
            {
                return false;
            }

            int groupLv = GetLv();
            if (citiaoItem.citiaoType == LDCiTiaoBaseGroup.NormalType ||
                citiaoItem.citiaoType == LDCiTiaoBaseGroup.PowerType)
            {
                if (groupLv >= MaxLv)
                {
                    return false;
                }
            }

            if (ExitExclusiveCiTiao(citiaoItem))
            {
                return false;
            }

            if (!OptionaCitiaoCiTiao(citiaoItem))
            {
                return false;
            }


            // 等级条件
            if (groupLv < citiaoItem.optionalLv)
            {
                return false;
            }

            return MatchoptionalCondition(citiaoItem);
        }

        public bool MatchoptionalCondition(CitiaoItem citiaoItem)
        {
            int[] optionalCondition = citiaoItem.optionalCondition;
            if (optionalCondition.Length > 0)
            {
                int opType = optionalCondition[0];
                int mechaId = m_DIYData.BodyId;
                if (opType == LDCiTiaoOptionCondition.DIYPart) //上阵指定部件（部件ID）
                {
                    for (int i = 1; i < optionalCondition.Length; i++)
                    {
                        if (m_DIYData.ContainsPart(optionalCondition[i]))
                        {
                            return true;
                        }
                    }
                }
                else if (opType == LDCiTiaoOptionCondition.DIYPartXI) //上阵指定系部件（系类型枚举）
                {
                    for (int i = 1; i < optionalCondition.Length; i++)
                    {
                        if (GetXiCount(optionalCondition[i]) > 0)
                        {
                            return true;
                        }
                    }
                }
                else if (opType == LDCiTiaoOptionCondition.Mecha) //上阵指定机甲（机甲ID）
                {
                    for (int i = 1; i < optionalCondition.Length; i++)
                    {
                        if (mechaId == (optionalCondition[i]))
                        {
                            return true;
                        }
                    }
                }
                else if (opType == LDCiTiaoOptionCondition.MechaXI) //上阵指定系机甲（系类型枚举）
                {
                    MechaItem mechaItem = Mecha.Data.Get(mechaId);
                    int mechaXi = mechaItem.DamageSystem;
                    for (int i = 1; i < optionalCondition.Length; i++)
                    {
                        if (mechaXi == optionalCondition[i])
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
            else
            {
                // 部件 再说
                return true;
            }
        }

        public bool OptionaCitiaoCiTiao(CitiaoItem citiaoItem)
        {
            if (citiaoItem.optionaCitiao.Length > 0)
            {
                bool hasOptinoa = false;
                foreach (int optionaID in citiaoItem.optionaCitiao)
                {
                    if (IsSelectedByAll(optionaID))
                    {
                        hasOptinoa = true;
                        break;
                    }
                }

                if (!hasOptinoa)
                {
                    return false;
                }
            }

            return true;
        }

        public bool ExitExclusiveCiTiao(CitiaoItem citiaoItem)
        {
            int[] exclusiveCiTiao = citiaoItem.exclusiveCitiao;
            if (exclusiveCiTiao.Length > 0)
            {
                // 互斥词条存在
                foreach (int exclusiveId in exclusiveCiTiao)
                {
                    if (IsSelectedByAll(exclusiveId))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public string GetLinkDesc2(int citiaoId)
        {
            CitiaoItem citiaoItem = Citiao.Data.Get(citiaoId);
            CitiaoGroupItem citiaoGroupItem = CitiaoGroup.Data.Get(CiTiaoGroupId);
            int[] optionalCondition = citiaoItem.optionalCondition;
            if (optionalCondition.Length == 0)
            {
                return string.Empty;
            }

            int opType = optionalCondition[0];
            if (opType == LDCiTiaoOptionCondition.DIYPart) //上阵指定部件（部件ID）
            {
                return Global.gApp.gGameData.GetTipsInCurLanguage(9536);
            }
            else if (opType == LDCiTiaoOptionCondition.DIYPartXI) //上阵指定系部件（系类型枚举）
            {
                return Global.gApp.gGameData.GetTipsInCurLanguage(9536);
            }
            else if (opType == LDCiTiaoOptionCondition.Mecha) //上阵指定机甲（机甲ID）
            {
                return Global.gApp.gGameData.GetTipsInCurLanguage(9537);
            }
            else if (opType == LDCiTiaoOptionCondition.MechaXI) //上阵指定系机甲（系类型枚举）
            {
                return Global.gApp.gGameData.GetTipsInCurLanguage(9537);
            }

            return string.Empty;
        }

        public string GetLinkDesc(int citiaoId)
        {
            CitiaoItem citiaoItem = Citiao.Data.Get(citiaoId);
            CitiaoGroupItem citiaoGroupItem = CitiaoGroup.Data.Get(CiTiaoGroupId);
            int[] optionalCondition = citiaoItem.optionalCondition;
            if (optionalCondition.Length == 0)
            {
                return string.Empty;
            }

            int mechaId = m_DIYData.BodyId;
            int opType = optionalCondition[0];
            if (opType == LDCiTiaoOptionCondition.DIYPart) //上阵指定部件（部件ID）
            {
                for (int i = 1; i < optionalCondition.Length; i++)
                {
                    if (m_DIYData.ContainsPart(optionalCondition[i]))
                    {
                        DIYPartCfgItem partCfgItem = DIYPartCfg.Data.Get(optionalCondition[i]);
                        if (partCfgItem.EntryPhrases > 0)
                        {
                            string tips = Global.gApp.gGameData.GetTipsInCurLanguage(9532);
                            return string.Format(tips, Global.gApp.gGameData.GetTipsInCurLanguage(partCfgItem.name));
                        }
                    }
                }
            }
            else if (opType == LDCiTiaoOptionCondition.DIYPartXI) //上阵指定系部件（系类型枚举）
            {
                for (int i = 1; i < optionalCondition.Length; i++)
                {
                    if (GetXiCount(optionalCondition[i]) > 0)
                    {
                        MatchConfigItem matchConfigItem;
                        MatchConfig.Data.TryGet(optionalCondition[i], out matchConfigItem, false);

                        if (matchConfigItem != null)
                        {
                            string tips = Global.gApp.gGameData.GetTipsInCurLanguage(9533);
                            return string.Format(tips, Global.gApp.gGameData.GetTipsInCurLanguage(matchConfigItem.name));
                        }
                        else
                        {
                            return string.Empty;
                        }
                    }
                }
            }
            else if (opType == LDCiTiaoOptionCondition.Mecha) //上阵指定机甲（机甲ID）
            {
                for (int i = 1; i < optionalCondition.Length; i++)
                {
                    if (mechaId == (optionalCondition[i]))
                    {
                        MechaItem mechaItem = Mecha.Data.Get(mechaId);
                        if (mechaItem.EntryPhrases > 0)
                        {
                            string tips = Global.gApp.gGameData.GetTipsInCurLanguage(9534);
                            return string.Format(tips, Global.gApp.gGameData.GetTipsInCurLanguage(mechaItem.name));
                        }
                    }
                }
            }
            else if (opType == LDCiTiaoOptionCondition.MechaXI) //上阵指定系机甲（系类型枚举）
            {
                MechaItem mechaItem = Mecha.Data.Get(mechaId);
                int mechaXi = mechaItem.DamageSystem;
                for (int i = 1; i < optionalCondition.Length; i++)
                {
                    if (mechaXi == optionalCondition[i])
                    {
                        MatchConfigItem matchConfigItem;
                        MatchConfig.Data.TryGet(mechaXi, out matchConfigItem, false);
                        if (matchConfigItem != null)
                        {
                            string tips = Global.gApp.gGameData.GetTipsInCurLanguage(9535);
                            return string.Format(tips, Global.gApp.gGameData.GetTipsInCurLanguage(matchConfigItem.name));
                        }
                        else
                        {
                            return string.Empty;
                        }
                    }
                }
            }

            return string.Empty;
        }

        public string GetLinkIcon(int citiaoId)
        {
            CitiaoItem citiaoItem = Citiao.Data.Get(citiaoId);
            CitiaoGroupItem citiaoGroupItem = CitiaoGroup.Data.Get(CiTiaoGroupId);
            int[] optionalCondition = citiaoItem.optionalCondition;
            if (optionalCondition.Length == 0)
            {
                return string.Empty;
            }

            int mechaId = m_DIYData.BodyId;
            int opType = optionalCondition[0];
            if (opType == LDCiTiaoOptionCondition.DIYPart) //上阵指定部件（部件ID）
            {
                for (int i = 1; i < optionalCondition.Length; i++)
                {
                    if (m_DIYData.ContainsPart(optionalCondition[i]))
                    {
                        DIYPartCfgItem partCfgItem = DIYPartCfg.Data.Get(optionalCondition[i]);
                        if (partCfgItem.EntryPhrases > 0)
                        {
                            CitiaoGroupItem linkGroupItem = CitiaoGroup.Data.Get(partCfgItem.EntryPhrases);
                            return LDCiTiaoBaseGroup.GetCitiaoGroupIcon(linkGroupItem);
                        }
                    }
                }
            }
            else if (opType == LDCiTiaoOptionCondition.DIYPartXI) //上阵指定系部件（系类型枚举）
            {
                for (int i = 1; i < optionalCondition.Length; i++)
                {
                    if (GetXiCount(optionalCondition[i]) > 0)
                    {
                        MatchConfigItem matchConfigItem;
                        MatchConfig.Data.TryGet(optionalCondition[i], out matchConfigItem, false);

                        if (matchConfigItem != null)
                        {
                            return matchConfigItem.icon;
                        }
                    }
                }
            }
            else if (opType == LDCiTiaoOptionCondition.Mecha) //上阵指定机甲（机甲ID）
            {
                for (int i = 1; i < optionalCondition.Length; i++)
                {
                    if (mechaId == (optionalCondition[i]))
                    {
                        MechaItem mechaItem = Mecha.Data.Get(mechaId);
                        if (mechaItem.EntryPhrases > 0)
                        {
                            CitiaoGroupItem linkGroupItem = CitiaoGroup.Data.Get(mechaItem.EntryPhrases);
                            return LDCiTiaoBaseGroup.GetCitiaoGroupIcon(linkGroupItem);
                        }
                    }
                }
            }
            else if (opType == LDCiTiaoOptionCondition.MechaXI) //上阵指定系机甲（系类型枚举）
            {
                MechaItem mechaItem = Mecha.Data.Get(mechaId);
                int mechaXi = mechaItem.DamageSystem;
                for (int i = 1; i < optionalCondition.Length; i++)
                {
                    if (mechaXi == optionalCondition[i])
                    {
                        MatchConfigItem matchConfigItem;
                        MatchConfig.Data.TryGet(mechaXi, out matchConfigItem, false);
                        if (matchConfigItem != null)
                        {
                            return matchConfigItem.icon;
                        }
                        else
                        {
                            return string.Empty;
                        }
                    }
                }
            }

            return string.Empty;
        }
    }
}