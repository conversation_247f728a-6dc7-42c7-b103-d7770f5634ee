using UnityEngine;
using UnityEngine.AI;

namespace LD
{
    /// <summary>
    /// NavMeshAgent 的封装 可以做一些简单特殊处理 比如 SetDestination
    /// </summary>
    public class LDAINavMesh
    {
        NavMeshAgent m_Agent;
        public bool Enable { get { return m_Agent.enabled; } set { m_Agent.enabled = value; } }
        public float Speed { get { return m_Agent.speed; } }
        public bool UpdateRotation { get { return m_Agent.updateRotation; } set { m_Agent.updateRotation = value; } }
        public bool UpdatePosition { get { return m_Agent.updatePosition; } set { m_Agent.updatePosition = value; } }
        public float StoppingDistance { get { return m_Agent.stoppingDistance; } set { m_Agent.stoppingDistance = value; StopDisSqr = value * value; } }
        public bool PathPending { get { return m_Agent.pathPending; } }
        public float StopDisSqr { get; set; }
        private Vector3 m_DtPos;
        private bool OnNavMesh;
        LDAIMonster m_AIMonster;
        private float m_RecordSpeed = 1;
        public void Init(LDAIMonster player)
        {
            m_AIMonster = player;
            m_Agent = player.gameObject.GetComponent<NavMeshAgent>();
            m_Agent.stoppingDistance = m_AIMonster.AIMgr.GetStopDis(0);
            m_Agent.updateRotation = false;
            m_Agent.speed = 1;
        }
        /// <summary>
        /// 简化流程 帮忙处理了TimeScale 上层不需要关系了。
        ///  m_SoldierPlayer.SetSpeed(Vector3.zero, speed); 速度为零 需要 底层帮忙计算动画速度
        /// </summary>
        /// <param name="speed"></param>
        public void SetSpeed(float speed)
        {
            SetAISpeedSimple(speed);
            //m_AIMonster.SetSpeedByAgent(Vector3.zero, speed);
        }

        private void SetAISpeedSimple(float speed)
        {
            SetSpeedSimple(speed);
            m_RecordSpeed = speed;
        }
        private void SetSpeedSimple(float speed)
        {
            m_RecordSpeed = speed;
            m_Agent.speed = speed;
        }
        private void ResertSpeed()
        {
            SetAISpeedSimple(m_RecordSpeed);
        }
        private void CheckRayEnemy(ref bool result)
        {
            if(result)
            {
                result = LDFightTools.RayCastHit.collider.gameObject.GetComponent<LDNavMeshMask>() == null;
            }
        }
        public void SetDestination(Vector3 destination, float dtTime)
        {
            if (Enable)
            {
                if (m_Agent.isOnNavMesh)
                {
                    OnNavMesh = true;
                    //CalcSpeed();
                    m_Agent.SetDestination(destination);
                }
                else
                {
                    OnNavMesh = false;
                    m_DtPos = destination - m_Agent.transform.GetPoisition();
                    m_Agent.transform.SetPoisition(Vector3.Lerp(m_Agent.transform.GetPoisition(), destination, dtTime * m_Agent.speed * 0.8f));
                }
            }
            else
            {
                OnNavMesh = false;
            }
        }
        private void CalcSpeed()
        {
            if (m_AIMonster.NormalMonster())
            {
                Vector3 startPos = m_Agent.transform.GetPoisition();
                startPos.y = startPos.y + 0.1f;
                bool hitEnemy = LDFightTools.HasRaycastEnemy(startPos, m_AIMonster.MoveCtroller.GetRotateNode().GetForward(), m_AIMonster.AIMgr.GetStopDis(1));
                //CheckRayEnemy(ref hitEnemy);
                if (!hitEnemy)
                {
                    if (DesiredVelocity().sqrMagnitude > 0.000001)
                    {
                        hitEnemy = LDFightTools.HasRaycastEnemy(startPos, DesiredVelocity(), m_AIMonster.AIMgr.GetStopDis(1));
                        //CheckRayEnemy(ref hitEnemy);
                    }
                }

                if (hitEnemy)
                {
                    m_Agent.speed = 0.01f;
                }
                else
                {
                    ResertSpeed();
                }
            }
        }
        public Vector3 DesiredVelocity()
        {
            if (OnNavMesh)
            {
                return m_Agent.desiredVelocity;
            }
            else
            {
                return m_DtPos;
            }
        }
    }
}

