using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDGuidPassHandle_1002 : LDMainHandler
    {
        private LDGuidStepTools m_GuidStepTools = new LDGuidStepTools();
        private LDFightUI m_FightUI_Nodes;
        private List<int> MaskCiTiao1 = new List<int>()
        {
            10104, 504, 10107
        };
        private List<int> MaskCiTiao2 = new List<int>()
        {
            10101, 501, 10112
        };
        private int MaxFresCiTitiaoTimes = 0;
        public override void AfterInit()
        {
            base.AfterInit();
            RegListener(true);
            LDMainRole mainRole = Global.gApp.CurFightScene.GetLocalRole();
            MaxFresCiTitiaoTimes = mainRole.RoleData.MaxCiTiaoFreshTimes;
            mainRole.RoleData.MaxCiTiaoFreshTimes = 0;
            m_FightUI_Nodes = Global.gApp.CurFightScene.gFightUI;
            Global.gApp.CurFightScene.AddUnScaleUpAct(m_GuidStepTools.OnDUpdate);
            GenerateForceActUpAct();
        }
        public override void DestroyHandler()
        {
            base.DestroyHandler();
            RegListener(false);
            Global.gApp.CurFightScene.RemoveUnScaleUpAct(m_GuidStepTools.OnDUpdate);
        }
        public override void EndEnterAnim()
        {
            m_GuidStepTools.StartNextAct();
        }
        public override bool TryHeroDeadth(LDHeroPlayer heroPlayer)
        {
            return false;
        }
        private void GenerateForceActUpAct()
        {
            LDGuidStepItem guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuiStep36;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuiStep36_1;
            guidStepItem.DelyTime = 1.2f;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuiStep37;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuiStep37_1;
            guidStepItem.DelyTime = 0.8f;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuiStep38;
            m_GuidStepTools.AddAct(guidStepItem);

            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuiStep39;
            m_GuidStepTools.AddAct(guidStepItem);    
            
            guidStepItem = new LDGuidStepItem();
            guidStepItem.ActAct = GuiStep40;
            m_GuidStepTools.AddAct(guidStepItem); 
            
        }

        private bool GuiStep36()
        {
            Debug.Log("GuiStep36 ");
            ListererDropExp(true);
            GetLocalHero().MechaMgr.SetSearchRadiuScale(0.7f);
            return false;
        }
        private bool GuiStep36_1()
        {
            LDSDKEvent.SendBattleGuidEvent("30102020");
            GetLocalHero().MechaMgr.SetSearchRadiuScale(1);
            Debug.Log("GuiStep36_1 ");
            Global.gApp.gUiMgr.OpenUIAsync<UIDialog>(LDUICfg.UIDialog).SetLoadedCall(uIDialog =>
            {
                uIDialog.InitData(new List<int>() { 97501, 97502 });
                uIDialog.ChangeIcon();
            });
            return false;
        }
        private bool GuiStep37()
        {
            Debug.Log("GuiStep37 ");
            ListererGainExp(true);
            return false;
        }
        private bool GuiStep37_1()
        {
            Debug.Log("GuiStep37_1 ");
            LDSDKEvent.SendBattleGuidEvent("30102030");
            Global.gApp.gUiMgr.OpenUIAsync<UIDialog>(LDUICfg.UIDialog).SetLoadedCall(uIDialog =>
            {
                uIDialog.InitData(new List<int>() { 97503, 97504 });
                uIDialog.ChangeIcon();
            });
            return false;
        }
        private bool GuiStep38()
        {
            Debug.Log("GuiStep38 ");
            ListererDropExp(false);
            ListererGainExp(false);
            ResetAllCiTaio();
            LDMainRole MainRole = Global.gApp.CurFightScene.GetLocalRole();

            Dictionary<int, LDCiTiaoBaseGroup> allGroup = MainRole.GetCaptainPlayer().CiTiaoMgr.GetAllCiTiaoGroup();
            foreach (KeyValuePair<int, LDCiTiaoBaseGroup> item in allGroup)
            {
                foreach (LDCiTiaoItemData allItem in item.Value.GetAllCiTiaoData())
                {
                    if (MaskCiTiao1.Contains(allItem.CitiaoItem.id))
                    {
                        allItem.CanBeSelect = true;
                        allItem.ForceCanSelect = true;
                    }
                    else
                    {
                        allItem.ForceCanSelect = false;
                        allItem.CanBeSelect = false;
                    }
                }
            }
            List<LDCiTiaoItemData> CiTiaoItemDatas = MainRole.GetCaptainPlayer().CiTiaoMgr.GetRandomValidCiTiao(LDCiTiaoType.Normal);
            if (CiTiaoItemDatas.Count == 0)
            {
                ResetAllCiTaio();
            }
            return false;
        }
        private void ResetAllCiTaio()
        {
            LDMainRole MainRole = Global.gApp.CurFightScene.GetLocalRole();
            Dictionary<int, LDCiTiaoBaseGroup> allGroup = MainRole.GetCaptainPlayer().CiTiaoMgr.GetAllCiTiaoGroup();

            foreach (KeyValuePair<int, LDCiTiaoBaseGroup> item in allGroup)
            {
                foreach (LDCiTiaoItemData allItem in item.Value.GetAllCiTiaoData())
                {
                    allItem.CanBeSelect = true;
                    allItem.ForceCanSelect = false;
                }
            }
        }
        private bool GuiStep39()
        {
            Debug.Log("GuiStep39 ");
            ResetAllCiTaio();
            LDMainRole MainRole = Global.gApp.CurFightScene.GetLocalRole();
            Dictionary<int, LDCiTiaoBaseGroup> allGroup = MainRole.GetCaptainPlayer().CiTiaoMgr.GetAllCiTiaoGroup();
            foreach (KeyValuePair<int, LDCiTiaoBaseGroup> item in allGroup)
            {
                foreach (LDCiTiaoItemData allItem in item.Value.GetAllCiTiaoData())
                {
                    if (MaskCiTiao2.Contains(allItem.CitiaoItem.id))
                    {
                        allItem.CanBeSelect = true;
                        allItem.ForceCanSelect = true;
                    }
                    else
                    {
                        allItem.ForceCanSelect = false;
                        allItem.CanBeSelect = false;
                    }
                }
            }
            List<LDCiTiaoItemData> CiTiaoItemDatas = MainRole.GetCaptainPlayer().CiTiaoMgr.GetRandomValidCiTiao(LDCiTiaoType.Normal);
            if (CiTiaoItemDatas.Count == 0)
            {
                ResetAllCiTaio();
            }
            return false;

        }
        private bool GuiStep40()
        {
            Debug.Log("GuiStep40 ");
            ResetAllCiTaio();
            LDMainRole mainRole = Global.gApp.CurFightScene.GetLocalRole();
            mainRole.RoleData.MaxCiTiaoFreshTimes = MaxFresCiTitiaoTimes;
            return true;
        }

        public Vector3 GetDstPropPos()
        {
            Vector3 startPos = GetLocalHero().transform.GetPoisition();
            float radius = 4;
            float angle = 2 * LDMath.PI / 8;
            for (int i = 0; i < 8; i++)
            {
                Vector3 newPos = startPos + radius * new Vector3(LDMath.Sin(angle * i), 0, LDMath.Cos(angle * i));
                if (!LDFightTools.RaycastPropBlockSphere(newPos, 0.5f))
                {
                    return newPos;
                }
            }
            return startPos;
        }
        private void OnHeroLvUp(LDHeroPlayer heroPlayer, LDCiTiaoItemData itemData)
        {
            m_GuidStepTools.StartNextAct();
        }
        private void OnDropExp(LDProp baseProp,int propId)
        {
            int propType = (LDParseTools.IntParse(baseProp.PropItem.param[0]));
            if(propType == LDPropType.Exp)
            {
                ListererDropExp(false);
                Global.gApp.gUiMgr.OpenUIAsync<BattleExpGuid>(LDUICfg.UIBattleExpGuid).SetLoadedCall(battleExpGuid =>
                {
                    battleExpGuid.StartGuideByWorldPos(1, true, Vector3.one, baseProp.transform.GetPoisition(), 0.4f);
                });
                m_GuidStepTools.StartNextAct();
            }
        }
        private void GuidGainExp(long guid, float exp)
        {
            ListererGainExp(false);

            UIFightView uIFightView = Global.gApp.CurFightScene.gFightUI.FightUI_Nodes;
             Global.gApp.gUiMgr.OpenUIAsync<BattleExpGuid>(LDUICfg.UIBattleExpGuid).SetLoadedCall(battleExpGuid =>
             {
                    battleExpGuid.StartGuideByAnchoredPos(1, false, new Vector3(10.2f, 0.3f, 1), uIFightView.img_exp_BG.rectTransform.GetPoisition(),
                    uIFightView.img_exp_BG.rectTransform.anchorMin, uIFightView.img_exp_BG.rectTransform.anchorMax, 0.4f); ;
             });
            uIFightView.texiao_tishi.gameObject.SetActive(true);
            m_GuidStepTools.StartNextAct();
        }

        private void ListererGainExp(bool re)
        {
            Global.gApp.gMsgDispatcher.RegEvent<long, float>(MsgIds.GainExp, GuidGainExp, re);
        }
        private void ListererDropExp(bool re)
        {
            Global.gApp.gMsgDispatcher.RegEvent<LDProp, int>(MsgIds.CreateProp, OnDropExp,re);
        }

        private void OnCloseOtherUI(string uiName)
        {
            if (uiName == LDUICfg.UIDialog)
            {
                Global.gApp.CurFightScene.ClearPause();
                m_GuidStepTools.StartNextAct();
                Global.gApp.gUiMgr.CloseUI(LDUICfg.UIBattleExpGuid);
            }
        }
        protected void RegListener(bool addListener)
        {
            Global.gApp.gMsgDispatcher.RegEvent<LDHeroPlayer, LDCiTiaoItemData>(MsgIds.CiTiaoLvUp, OnHeroLvUp, addListener);
            Global.gApp.gMsgDispatcher.RegEvent<string>(MsgIds.OnCloseUI, OnCloseOtherUI, addListener);
            if (!addListener)
            {
                ListererDropExp(false);
                ListererGainExp(false);
            }
        }
    }
}
