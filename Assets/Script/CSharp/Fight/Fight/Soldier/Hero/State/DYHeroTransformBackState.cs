
using UnityEngine;

namespace LD
{
    public class DYHeroTransformBackState : LDHeroBaseState
    {
        private float m_CurTime;
        private float m_TransformerBackTime = 1.3f;

        protected override void OnEnterImp()
        {
            m_HeroPlayer.MechaMgr.SetWpnEnalble(false);
            m_HeroPlayer.HeroAnimCtrol.DiyHandle.SetAnimData(LDAnimName.DiyTransformingAnim);
            m_HeroPlayer.HeroAnimCtrol.PlayUAnim(LDAnimName.TransformerBack);
            m_HeroPlayer.HeroAnimCtrol.PlayDAnim(LDAnimName.TransformerBack);
            m_CurTime = 0;
        }

        protected override void OnRUpdate(float dt)
        {
            m_CurTime = m_CurTime + dt;
            if (m_CurTime >= m_TransformerBackTime)
            {
                m_AIStateMachine.ResetNormalSate();
            }
        }
        protected override void OnExitImp()
        {
            m_HeroPlayer.HeroAnimCtrol.DiyHandle.SetAnimData(LDAnimName.DiyNormalAnim);
            m_HeroPlayer.MechaMgr.SetWpnEnalble(false);
        }
    }
}
