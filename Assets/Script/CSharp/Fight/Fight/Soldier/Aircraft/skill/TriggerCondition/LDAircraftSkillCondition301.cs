using System.Collections.Generic;

namespace LD
{
    public class LDAircraftSkillCondition301 : LDAircraftSkillConditionBase
    {
        private HashSet<int> m_BulletIds = new HashSet<int>();
        protected override void InitImp()
        {
            for (int i = 1; i < GetParams().Length; i++)
            {
                m_BulletIds.Add(EZMath.IDDoubleToInt(GetParam(i)));
            } 
        }

        protected override void UpdateImp(float dt)
        {
        }
        
        public override void RegListener(bool reg)
        {
            Global.gApp.gMsgDispatcher.RegEvent<LDAtkBullet, LDHeroPlayer>(MsgIds.HeroOnHitByHeroBullet, OnHeroOnHitByHeroBullet, reg);
        }

        private void OnHeroOnHitByHeroBullet(LDAtkBullet bullet, LDHeroPlayer heroPlayer)
        {
            if (heroPlayer.Live)
            {
                if (m_BulletIds.Contains(bullet.BulletItem.id))
                {
                    TriggerSuccess(heroPlayer);
                }    
            }
        }

        protected override void TryTriggerImp()
        {
        }
    }
}