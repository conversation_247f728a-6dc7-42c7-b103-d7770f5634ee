using System.Collections.Generic;

namespace LD
{
    public class LDAircraftSkillCondition102 : LDAircraftSkillConditionBase
    {
        protected override void InitImp()
        {
        }

        protected override void UpdateImp(float dt)
        {
        }

        protected override void TryTriggerImp()
        {
            if (GetTargetType() == LDAircraftSkillConditionTargetType.AnyMecha)
            {
                List<LDHeroPlayer> heroPlayers = m_Aircraft.GetAllHeroPlayers();
                foreach (LDHeroPlayer heroPlayer in heroPlayers)
                {
                    if (MatchContion(heroPlayer))
                    {
                        TriggerSuccess(heroPlayer);
                    }
                    else
                    {
                        m_Skill.RemoveSkillLogicEffect();
                    }
                }
            }
        }
        
        private bool MatchContion(LDHeroPlayer heroPlayer)
        {
            if (heroPlayer.HeroData.GetShieldVal() > 0)
            {
                return true;
            }

            return false;
        }
    }
}