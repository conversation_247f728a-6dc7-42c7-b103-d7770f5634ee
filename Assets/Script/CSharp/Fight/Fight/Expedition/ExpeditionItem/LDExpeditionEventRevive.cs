using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace LD
{
    public class LDExpeditionEventRevive : LDExpeditionTriggerEvent
    {
        public override void InitImp()
        {
        }
        public override void TriggerSucess(int triggerType)
        {
            if (!m_Restore)
            {
                if (TryggerToEffectNode())
                {
                    int[] result = ExpeditionEventData.ExpeditionEventEffectItem.result;
                    m_MainRole.GetCaptainPlayer().HeroData.AddHpByPercent(result[1] / 10000.0d);
                    for (int i = 2; i < result.Length; i++)
                    {
                        m_MainRole.GetCaptainPlayer().BuffMgr.AddBuff(result[i], m_MainRole);
                    }
                }
            }
        }
        public override void DestroyImp()
        {
        }
    }
}
