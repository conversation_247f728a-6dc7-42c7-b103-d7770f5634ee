using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public class LDGoldCarElement : LDLockElement
    {
        private NormalAIHP m_NormalAIHP;
        private Transform m_ModeNode;
        private Animator m_GoldAnimator;
        private string m_AnimName = string.Empty;
        public GameObject Caikuangche_jinkuang01;
        public GameObject Caikuangche_jinkuang02;
        public GameObject WaKuangEffect;
        public override void Init(LDElementMgr elementMgr, TrapItem citiaoTrapItem)
        {
            base.Init(elementMgr, citiaoTrapItem);
            DYObject = LDGameObj.GoldCar;
            elementMgr.GoldCarElement = this;

            m_ModeNode = transform.Find(LDFightConstVal.ModelNodeName);
            Transform hpNode = transform.Find(LDFightConstVal.HpNode);
            if(hpNode != null)
            {
                GameObject hpUINode = Global.gApp.gResMgr.InstantiateLoadObj(LDUICfg.NormalCarHP,
                                  ResSceneType.NormalRes, Global.gApp.CurFightScene.gFightUI.FightUI_Nodes.HpNode.rectTransform);
                m_NormalAIHP = hpUINode.GetComponent<NormalAIHP>();
                m_NormalAIHP.Init(-1, hpNode, Global.gApp.CurFightScene.gFightUI.ParentCanvas);
                m_NormalAIHP.gameObject.SetActive(true);
                FreshHpProgress();
            }
            m_GoldAnimator = transform.Find(LDFightConstVal.AIAnimNode).GetComponent<Animator>();
            Caikuangche_jinkuang01 = m_GoldAnimator.transform.Find("caikuangche_jinkuang01").gameObject;
            Caikuangche_jinkuang02 = m_GoldAnimator.transform.Find("caikuangche_jinkuang02").gameObject;
            WaKuangEffect = m_GoldAnimator.transform.Find("fx_caikuang").gameObject;
            Debug.Log("CarHP " + m_HP);
        }
        public override bool OnHittedN(LDAtkBullet bullet)
        {
            base.OnHittedN(bullet);
            OnDamage((bullet.AtkData.BulletAtk * bullet.AtkData.Param_SkillDamage + bullet.AtkData.Param_SkillDamageBase) * bullet.AtkData.DamageParam);
            FreshHpProgress();
            if (m_HP <= 0)
            {
                OnDeadth();
            }
            return true;
        }
        public void StartWaKuang()
        {
            PlayAnim(LDAnimName.shoot_loop);
            WaKuangEffect.gameObject.SetActive(true);
        }
        public void PlayAnim(string animName)
        {
            if (animName != LDAnimName.shoot_loop)
            {
                WaKuangEffect.gameObject.SetActive(false);
            }
            if (m_AnimName.Equals(animName))
            {
                return;
            }
            m_AnimName = animName;
            m_GoldAnimator.Play(animName,-1,0);
        }
        protected override void OnDeadth()
        {
            base.OnDeadth();
            if(m_NormalAIHP != null)
            {
                Global.gApp.gResMgr.DestroyGameObj(m_NormalAIHP.gameObject);
            }
            //elementMgr.GoldCartElement = null;
            gameObject.SetActive(true);
            m_GoldAnimator.Play(LDAnimName.Death,0,0);
            Global.gApp.CurFightScene.gPassHandler.TryGameLose(LDGameEndReason.GoldCar);
        }
        public void SePosition(Vector3 poisition)
        {
            transform.SetPoisition(poisition);
        }
        public void SetForward(Vector3 forward)
        {
            m_ModeNode.SetForward(forward);
        }
        private void FreshHpProgress()
        {
            if (m_NormalAIHP != null)
            {
                m_NormalAIHP.SetHpPercent((float)(1.0d * m_HP / m_MaxHP));
            }
        }
        public override void RemoveSelf()
        {
            base.RemoveSelf();

        }
    }
}
