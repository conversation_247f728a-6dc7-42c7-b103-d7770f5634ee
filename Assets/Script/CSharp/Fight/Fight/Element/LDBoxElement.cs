using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD
{
    public class LDBoxElement : LDLockElement
    {
        public override void Init(LDElementMgr elementMgr, TrapItem citiaoTrapItem)
        {
            base.Init(elementMgr, citiaoTrapItem);
            UpdateKinematicState(false);
            DYObject = LDGameObj.ElementBox;
            elementMgr.AddBoxTrap(this);
            m_HP = 0;
        }
        public override bool OnHittedN(LDAtkBullet bullet)
        {
            base.OnHittedN(bullet);
            if (m_HP <= 0)
            {
                TryDropItems(bullet);
                OnDeadth();
            }
            return true;
        }
        public override void RemoveSelf()
        {
            m_ElementMgr.RemoveBoxTrap(this);
            base.RemoveSelf();
        }
        private float GetRate(LDAtkBullet bullet,int dropId)
        {
            GlobalCfgItem boxAIItem = GlobalCfg.Data.Get(LDGlobalConfigId.BoxAIRate);
            if (dropId == boxAIItem.valueIntarray[1])
            {
                if (Global.gApp.CurFightScene.gWaveMgr.LiveAICount >= boxAIItem.valueIntarray[0])
                {
                    return boxAIItem.valueIntarray[2];
                }
            }

            MapPropItem mapPropItem = MapProp.Data.Get(dropId);
            int  propType = LDParseTools.IntParse(mapPropItem.param[0]);
            if (propType == LDPropType.GainAllExp)
            {
                List<LDMainRole> allRole = Global.gApp.CurFightScene.gPlayerMgr.GetMainRole();
                foreach (LDMainRole role in allRole)
                {
                    if(role.Live)
                    {
                        int citiaoId = Global.gApp.gSystemMgr.gMonthlyCardMgr.GetMonthlyCardCiTiao();
                        if (role.GetCaptainPlayer().CiTiaoMgr.GetSelectedCiTiaoById(citiaoId) == null)
                        {
                            return 1;
                        }
                    }
                }
                return 0;
            }
            if (bullet.AtkData.GetHeroPlayer() != null)
            {
                LDHeroPlayer heroPlayer = bullet.AtkData.GetHeroPlayer();
                GlobalCfgItem hpRateItem = GlobalCfg.Data.Get(LDGlobalConfigId.BoxHpRate);
                if(EZMath.IDDoubleToInt(heroPlayer.HeroData.GetHpPercent() * 100) <= hpRateItem.valueIntarray[0])
                {
                    return hpRateItem.valueIntarray[2];
                }
                else
                {
                    return 1;
                }
            }
            else
            {
                return 1;
            }
        }
        private void TryDropItems(LDAtkBullet bullet)
        {
            if(m_CitiaoTrapItem == null)
            {
                return;
            }
            Vector3 dropPos = transform.GetPoisition();

            int maxRate = 0;


            for (int i = 0; i < m_CitiaoTrapItem.param.Length; i += 2)
            {
                maxRate += EZMath.IDFloatToInt(m_CitiaoTrapItem.param[i + 1] * GetRate(bullet, m_CitiaoTrapItem.param[i]));
            }
            int rateVal = m_ElementMgr.RandomUtil.NextInt(0,maxRate);
            int curRate = 0;
            for (int i = 0; i < m_CitiaoTrapItem.param.Length; i += 2)
            {
                curRate += EZMath.IDFloatToInt(m_CitiaoTrapItem.param[i + 1] * GetRate(bullet, m_CitiaoTrapItem.param[i]));
                if (curRate >= rateVal)
                {
                    Global.gApp.CurFightScene.gPropMgr.AddProp(dropPos, m_CitiaoTrapItem.param[i]);
                    return;
                }
            }
        }
    }
}
