namespace LD
{
    public class LDUnlockSystemUtils
    {
        public static int None = 0;         //空
        public const int MainUi = 1;        //主界面
        public const int Weapon = 100;      //武器
        public const int Equip = 200;       //装备
        public const int Role = 300;       //角色
        public const int Shop = 400;        //商店
        public const int Campsite = 500;    //家园

        //是否开启
        public static bool IsUnlockSystem(int system, bool isTips = false)
        {
            if (system <= 0)
            {
                return true;
            }
            //UnlockSystemItem systemItem = Global.gApp.gGameData.UnlockSystemData.Get(system);
            //if (systemItem == null)
            //{
            //    return true;
            //}
            //bool isUnLock = Global.gApp.gFilterFactory.Filter(systemItem.Unlock);
            //if (!isUnLock)
            //{
            //    if (isTips)
            //    {
            //        Global.gApp.gMsgDispatcher.Broadcast<string>(MsgIds.ShowGameTipsByStr, Global.gApp.gFilterFactory.GetUnFinishTips(systemItem.Unlock, systemItem.unlockTips));
            //    }
            //    return false;
            //}
            return true;
        }

        //是否开启
        public static string UnlockSystemTips(int system, bool withTips = false, bool shortTips = false)
        {
            //UnlockSystemItem systemItem = Global.gApp.gGameData.UnlockSystemData.Get(system);
            //if (systemItem == null)
            //{
            //    return string.Empty;
            //}
            //bool isUnLock = Global.gApp.gFilterFactory.Filter(systemItem.Unlock);
            //if (!isUnLock)
            //{
            //    if (withTips)
            //    {
            //        var tipsId = shortTips ? systemItem.SourceUnlockTips : systemItem.unlockTips;
            //        return Global.gApp.gFilterFactory.GetUnFinishTips(systemItem.Unlock, tipsId);
            //    }
            //    else
            //    {
            //        return Global.gApp.gFilterFactory.GetUnFinishTips(systemItem.Unlock);
            //    }

            //}
            return string.Empty;
        }
    }
}