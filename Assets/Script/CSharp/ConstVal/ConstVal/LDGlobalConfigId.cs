using LD;
using System.Collections.Generic;

public class LDGlobalConfigId
{    //语言列表
    public static int MaxCiTiaoFreshTimes = 10041;
    public static int MissionReviveLimit = 10042;
    public static int MissionReviveDiamondCost = 10043;
    public static int BoxHpRate = 10044;
    public static int BoxAIRate = 10045;

    public static int AttackReminder = 10047;
    public static int CameraPosY = 10050;
    public static int FirstBattle = 10051;
    public static int BattleModelScale = 10052;
    public static int CameraPosYGuid = 10053;
    public static int ActiveSkillEffect = 10057;
    public static int BattleTimeLimit = 10061;

    public static int ExpeditionPassParam = 20003;

    public static int SeasonPassParam = 30001;

    public static int UAVRapidSynthesisQuality = 80020;
    public static int ChangeNameCD = 80032;
    public static int ChangeNamePrice = 80031;

    public static int ExpeditionPassCitiao = 20001;


    public static int Language_Release = 80004;
    public static int Language_Test = 80121;

    public static int SNSDiscordURL = 80045;
    public static int SNSDiscordDiamondNum = 80046;
    public static int VipCombatSpeed = 80049;
    public static int BindAccountDiamondNum = 80061;
    public static int MagnetTrialHide = 80063;
    public static int AutosSelectCiTiao = 80067;
    public static int VipCombatSpeedArena = 80094;

    public static int AvatarBoxID = 80085;
    public static int ContinueFighting = 80077;

    public static int MechaSamples = 80104;
    public static int AFKsweepOpenUI = 80155;
    
    public static int PveTeamAtkInc = 10054;
    public static int PveTeamMissionDefenseHp = 10064;
    //跨服竞技场开闭区间
    public static int KFZeroBeforeMills = 80143;
    public static int KFZeroAfterMills = 80144;
    // 重新挑战上一关cond
    public static int ChallengePreviousLevel = 80163;
}