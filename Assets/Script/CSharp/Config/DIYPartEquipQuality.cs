//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class DIYPartEquipQuality : ScriptableObject {

		public static string CfgName = "DIYPartEquipQuality";

		public static DIYPartEquipQuality Data{ get { return Global.gApp.gGameData.GetData<DIYPartEquipQuality>(CfgName); } }
		[SerializeField, HideInInspector]
		private DIYPartEquipQualityItem[] _Items;
		public DIYPartEquipQualityItem[] items { get { return _Items; } }

		public DIYPartEquipQualityItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				DIYPartEquipQualityItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("DIYPartEquipQuality表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out DIYPartEquipQualityItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("DIYPartEquipQuality表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class DIYPartEquipQualityItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Equipid;
		/// <summary>
		/// 装备ID
		/// </summary>
		public int equipid { get { return _Equipid; } }

		[SerializeField, HideInInspector]
		private int _Quality;
		/// <summary>
		/// 品质
		/// </summary>
		public int quality { get { return _Quality; } }

		[SerializeField, HideInInspector]
		private string[] _Attr;
		/// <summary>
		/// 备注
		/// </summary>
		public string[] Attr { get { return _Attr; } }

		public override string ToString() {
			return string.Format("[DIYPartEquipQualityItem]{{id:{0}, equipid:{1}, quality:{2}, Attr:{3}}}",
				id, equipid, quality, array2string(Attr));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(DIYPartEquipQualityItem item) {
			return item != null;
		}

	}

}
