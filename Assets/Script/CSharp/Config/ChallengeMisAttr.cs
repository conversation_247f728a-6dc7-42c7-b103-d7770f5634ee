//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class ChallengeMisAttr : ScriptableObject {

		public static string CfgName = "ChallengeMisAttr";

		public static ChallengeMisAttr Data{ get { return Global.gApp.gGameData.GetData<ChallengeMisAttr>(CfgName); } }
		[SerializeField, HideInInspector]
		private ChallengeMisAttrItem[] _Items;
		public ChallengeMisAttrItem[] items { get { return _Items; } }

		public ChallengeMisAttrItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				ChallengeMisAttrItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("ChallengeMisAttr表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out ChallengeMisAttrItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("ChallengeMisAttr表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class ChallengeMisAttrItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Day;
		/// <summary>
		/// 备注
		/// </summary>
		public int day { get { return _Day; } }

		[SerializeField, HideInInspector]
		private string _Type;
		/// <summary>
		/// 天数
		/// </summary>
		public string type { get { return _Type; } }

		[SerializeField, HideInInspector]
		private int _Score;
		/// <summary>
		/// 类型D
		/// </summary>
		public int score { get { return _Score; } }

		[SerializeField, HideInInspector]
		private string[] _TargetReward;
		/// <summary>
		/// 单条任务所获得分值
		/// </summary>
		public string[] targetReward { get { return _TargetReward; } }

		[SerializeField, HideInInspector]
		private string _ModuleOpen;
		/// <summary>
		/// 任务奖励
		/// </summary>
		public string moduleOpen { get { return _ModuleOpen; } }

		[SerializeField, HideInInspector]
		private int _Jump;
		/// <summary>
		/// 开启条件
		/// </summary>
		public int jump { get { return _Jump; } }

		public override string ToString() {
			return string.Format("[ChallengeMisAttrItem]{{id:{0}, day:{1}, type:{2}, score:{3}, targetReward:{4}, moduleOpen:{5}, jump:{6}}}",
				id, day, type, score, array2string(targetReward), moduleOpen, jump);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(ChallengeMisAttrItem item) {
			return item != null;
		}

	}

}
