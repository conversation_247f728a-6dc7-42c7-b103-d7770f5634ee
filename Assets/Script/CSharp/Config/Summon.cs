//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class Summon : ScriptableObject {

		public static string CfgName = "Summon";

		public static Summon Data{ get { return Global.gApp.gGameData.GetData<Summon>(CfgName); } }
		[SerializeField, HideInInspector]
		private SummonItem[] _Items;
		public SummonItem[] items { get { return _Items; } }

		public SummonItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				SummonItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("Summon表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out SummonItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("Summon表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class SummonItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 召唤物ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private string _Prefab;
		/// <summary>
		/// 备注
		/// </summary>
		public string prefab { get { return _Prefab; } }

		[SerializeField, HideInInspector]
		private int _SummonType;
		/// <summary>
		/// 预置
		/// </summary>
		public int SummonType { get { return _SummonType; } }

		[SerializeField, HideInInspector]
		private int _MechaType;
		/// <summary>
		/// 召唤物类型
		/// </summary>
		public int MechaType { get { return _MechaType; } }

		[SerializeField, HideInInspector]
		private string _CollisionBody;
		/// <summary>
		/// 攻击类型
		/// </summary>
		public string CollisionBody { get { return _CollisionBody; } }

		[SerializeField, HideInInspector]
		private float[] _Sumparam;
		/// <summary>
		/// 碰撞体
		/// </summary>
		public float[] sumparam { get { return _Sumparam; } }

		[SerializeField, HideInInspector]
		private float _BaseSpeed;
		/// <summary>
		/// 召唤物参数
		/// </summary>
		public float baseSpeed { get { return _BaseSpeed; } }

		[SerializeField, HideInInspector]
		private float _LiveTime;
		/// <summary>
		/// 基础移速
		/// </summary>
		public float liveTime { get { return _LiveTime; } }

		[SerializeField, HideInInspector]
		private int _Bullet;
		/// <summary>
		/// 持续时间
		/// </summary>
		public int bullet { get { return _Bullet; } }

		[SerializeField, HideInInspector]
		private int _MainHitBullet;
		/// <summary>
		/// 子弹id
		/// </summary>
		public int mainHitBullet { get { return _MainHitBullet; } }

		[SerializeField, HideInInspector]
		private int _SummonBullet;
		/// <summary>
		/// 子弹产生子弹id
		/// </summary>
		public int summonBullet { get { return _SummonBullet; } }

		[SerializeField, HideInInspector]
		private float _CdTime;
		/// <summary>
		/// 召唤物产生子弹
		/// </summary>
		public float cdTime { get { return _CdTime; } }

		[SerializeField, HideInInspector]
		private float _TargetRange;
		/// <summary>
		/// 攻击间隔
		/// </summary>
		public float targetRange { get { return _TargetRange; } }

		[SerializeField, HideInInspector]
		private int _BornEffect;
		/// <summary>
		/// 索敌范围
		/// </summary>
		public int bornEffect { get { return _BornEffect; } }

		[SerializeField, HideInInspector]
		private int _DeathEffect;
		/// <summary>
		/// 出生特效
		/// </summary>
		public int deathEffect { get { return _DeathEffect; } }

		[SerializeField, HideInInspector]
		private string _BornAudio;
		/// <summary>
		/// 死亡特效
		/// </summary>
		public string bornAudio { get { return _BornAudio; } }

		[SerializeField, HideInInspector]
		private string _DeathAudio;
		/// <summary>
		/// 出生音效
		/// </summary>
		public string deathAudio { get { return _DeathAudio; } }

		[SerializeField, HideInInspector]
		private string _AtkAudio;
		/// <summary>
		/// 死亡音效
		/// </summary>
		public string atkAudio { get { return _AtkAudio; } }

		public override string ToString() {
			return string.Format("[SummonItem]{{id:{0}, prefab:{1}, SummonType:{2}, MechaType:{3}, CollisionBody:{4}, sumparam:{5}, baseSpeed:{6}, liveTime:{7}, bullet:{8}, mainHitBullet:{9}, summonBullet:{10}, cdTime:{11}, targetRange:{12}, bornEffect:{13}, deathEffect:{14}, bornAudio:{15}, deathAudio:{16}, atkAudio:{17}}}",
				id, prefab, SummonType, MechaType, CollisionBody, array2string(sumparam), baseSpeed, liveTime, bullet, mainHitBullet, summonBullet, cdTime, targetRange, bornEffect, deathEffect, bornAudio, deathAudio, atkAudio);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(SummonItem item) {
			return item != null;
		}

	}

}
