//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class AircraftReforgeBase : ScriptableObject {

		public static string CfgName = "AircraftReforgeBase";

		public static AircraftReforgeBase Data{ get { return Global.gApp.gGameData.GetData<AircraftReforgeBase>(CfgName); } }
		[SerializeField, HideInInspector]
		private AircraftReforgeBaseItem[] _Items;
		public AircraftReforgeBaseItem[] items { get { return _Items; } }

		public AircraftReforgeBaseItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				AircraftReforgeBaseItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("AircraftReforgeBase表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out AircraftReforgeBaseItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("AircraftReforgeBase表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class AircraftReforgeBaseItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ManufactureLevel;
		/// <summary>
		/// #备注
		/// </summary>
		public int manufactureLevel { get { return _ManufactureLevel; } }

		[SerializeField, HideInInspector]
		private int _ManufactureExp;
		/// <summary>
		/// 洗练等级
		/// </summary>
		public int manufactureExp { get { return _ManufactureExp; } }

		[SerializeField, HideInInspector]
		private string[] _ManufactureProbability;
		/// <summary>
		/// 经验
		/// </summary>
		public string[] manufactureProbability { get { return _ManufactureProbability; } }

		[SerializeField, HideInInspector]
		private int _ReforgeNum;
		/// <summary>
		/// 概率
		/// </summary>
		public int reforgeNum { get { return _ReforgeNum; } }

		[SerializeField, HideInInspector]
		private int _LockNum;
		/// <summary>
		/// 解锁洗练条数
		/// </summary>
		public int lockNum { get { return _LockNum; } }

		[SerializeField, HideInInspector]
		private string _ReforgeItem;
		/// <summary>
		/// 可锁定洗练条数
		/// </summary>
		public string reforgeItem { get { return _ReforgeItem; } }

		public override string ToString() {
			return string.Format("[AircraftReforgeBaseItem]{{id:{0}, manufactureLevel:{1}, manufactureExp:{2}, manufactureProbability:{3}, reforgeNum:{4}, lockNum:{5}, reforgeItem:{6}}}",
				id, manufactureLevel, manufactureExp, array2string(manufactureProbability), reforgeNum, lockNum, reforgeItem);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(AircraftReforgeBaseItem item) {
			return item != null;
		}

	}

}
