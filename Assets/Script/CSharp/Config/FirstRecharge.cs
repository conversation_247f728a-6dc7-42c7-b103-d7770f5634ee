//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class FirstRecharge : ScriptableObject {

		public static string CfgName = "FirstRecharge";

		public static FirstRecharge Data{ get { return Global.gApp.gGameData.GetData<FirstRecharge>(CfgName); } }
		[SerializeField, HideInInspector]
		private FirstRechargeItem[] _Items;
		public FirstRechargeItem[] items { get { return _Items; } }

		public FirstRechargeItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				FirstRechargeItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("FirstRecharge表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out FirstRechargeItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("FirstRecharge表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class FirstRechargeItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _MallGoodsId;
		/// <summary>
		/// 策划备注
		/// </summary>
		public int MallGoodsId { get { return _MallGoodsId; } }

		[SerializeField, HideInInspector]
		private int _SaleTxt;
		/// <summary>
		/// 商品ID
		/// </summary>
		public int saleTxt { get { return _SaleTxt; } }

		[SerializeField, HideInInspector]
		private string[] _Reward;
		/// <summary>
		/// 充值折扣显示
		/// </summary>
		public string[] reward { get { return _Reward; } }

		[SerializeField, HideInInspector]
		private string _ItemBackGround;
		/// <summary>
		/// 奖励内容
		/// </summary>
		public string itemBackGround { get { return _ItemBackGround; } }

		[SerializeField, HideInInspector]
		private string _EffectBackGround;
		/// <summary>
		/// 首充界面图标背景资源路径
		/// </summary>
		public string EffectBackGround { get { return _EffectBackGround; } }

		[SerializeField, HideInInspector]
		private int _MainTxt;
		/// <summary>
		/// 头图品质光照图资源路径
		/// </summary>
		public int mainTxt { get { return _MainTxt; } }

		[SerializeField, HideInInspector]
		private int _FirstRechargeTxt;
		/// <summary>
		/// 主界面入口文案
		/// </summary>
		public int firstRechargeTxt { get { return _FirstRechargeTxt; } }

		[SerializeField, HideInInspector]
		private int _AdvertisementTxt;
		/// <summary>
		/// 标题文案
		/// </summary>
		public int advertisementTxt { get { return _AdvertisementTxt; } }

		[SerializeField, HideInInspector]
		private string[] _MechaId;
		/// <summary>
		/// 广告语文案
		/// </summary>
		public string[] mechaId { get { return _MechaId; } }

		[SerializeField, HideInInspector]
		private string[] _MechaQualityName;
		/// <summary>
		/// 升级机甲id
		/// </summary>
		public string[] mechaQualityName { get { return _MechaQualityName; } }

		[SerializeField, HideInInspector]
		private string[] _AdvertisementImage;
		/// <summary>
		/// 机甲品质名字
		/// </summary>
		public string[] advertisementImage { get { return _AdvertisementImage; } }

		public override string ToString() {
			return string.Format("[FirstRechargeItem]{{id:{0}, MallGoodsId:{1}, saleTxt:{2}, reward:{3}, itemBackGround:{4}, EffectBackGround:{5}, mainTxt:{6}, firstRechargeTxt:{7}, advertisementTxt:{8}, mechaId:{9}, mechaQualityName:{10}, advertisementImage:{11}}}",
				id, MallGoodsId, saleTxt, array2string(reward), itemBackGround, EffectBackGround, mainTxt, firstRechargeTxt, advertisementTxt, array2string(mechaId), array2string(mechaQualityName), array2string(advertisementImage));
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(FirstRechargeItem item) {
			return item != null;
		}

	}

}
