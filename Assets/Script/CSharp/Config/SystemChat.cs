//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class SystemChat : ScriptableObject {

		public static string CfgName = "SystemChat";

		public static SystemChat Data{ get { return Global.gApp.gGameData.GetData<SystemChat>(CfgName); } }
		[SerializeField, HideInInspector]
		private SystemChatItem[] _Items;
		public SystemChatItem[] items { get { return _Items; } }

		public SystemChatItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				SystemChatItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("SystemChat表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out SystemChatItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("SystemChat表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class SystemChatItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _ChatTxt;
		/// <summary>
		/// 备注
		/// </summary>
		public int chatTxt { get { return _ChatTxt; } }

		[SerializeField, HideInInspector]
		private int _ChatSystemTxt;
		/// <summary>
		/// 频道公告内容
		/// </summary>
		public int chatSystemTxt { get { return _ChatSystemTxt; } }

		public override string ToString() {
			return string.Format("[SystemChatItem]{{id:{0}, chatTxt:{1}, chatSystemTxt:{2}}}",
				id, chatTxt, chatSystemTxt);
		}

		public static implicit operator bool(SystemChatItem item) {
			return item != null;
		}

	}

}
