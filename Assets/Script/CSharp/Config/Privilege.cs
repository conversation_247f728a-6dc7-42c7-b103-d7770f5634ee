//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class Privilege : ScriptableObject {

		public static string CfgName = "Privilege";

		public static Privilege Data{ get { return Global.gApp.gGameData.GetData<Privilege>(CfgName); } }
		[SerializeField, HideInInspector]
		private PrivilegeItem[] _Items;
		public PrivilegeItem[] items { get { return _Items; } }

		public PrivilegeItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				PrivilegeItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("Privilege表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out PrivilegeItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("Privilege表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class PrivilegeItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// 唯一ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private string _EntryName;
		/// <summary>
		/// 条件名
		/// </summary>
		public string entryName { get { return _EntryName; } }

		[SerializeField, HideInInspector]
		private int _DailyFrequency;
		/// <summary>
		/// 每日次数
		/// </summary>
		public int dailyFrequency { get { return _DailyFrequency; } }

		[SerializeField, HideInInspector]
		private int _Desc;
		/// <summary>
		/// 备注1
		/// </summary>
		public int desc { get { return _Desc; } }

		public override string ToString() {
			return string.Format("[PrivilegeItem]{{id:{0}, entryName:{1}, dailyFrequency:{2}, desc:{3}}}",
				id, entryName, dailyFrequency, desc);
		}

		public static implicit operator bool(PrivilegeItem item) {
			return item != null;
		}

	}

}
