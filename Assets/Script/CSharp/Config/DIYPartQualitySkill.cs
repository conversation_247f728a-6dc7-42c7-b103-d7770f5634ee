//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

using UnityEngine;

namespace LD {

	public partial class DIYPartQualitySkill : ScriptableObject {

		public static string CfgName = "DIYPartQualitySkill";

		public static DIYPartQualitySkill Data{ get { return Global.gApp.gGameData.GetData<DIYPartQualitySkill>(CfgName); } }
		[SerializeField, HideInInspector]
		private DIYPartQualitySkillItem[] _Items;
		public DIYPartQualitySkillItem[] items { get { return _Items; } }

		public DIYPartQualitySkillItem Get(int id) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				DIYPartQualitySkillItem item = _Items[index];
				if (item.id == id) { return item; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			UnityEngine.Debug.LogError("DIYPartQualitySkill表找不到 => " + id);
			return null;
		}

		public bool TryGet(int id, out DIYPartQualitySkillItem item, bool logError = true) {
			int min = 0;
			int max = items.Length;
			while (min < max) {
				int index = (min + max) >> 1;
				item = _Items[index];
				if (item.id == id) { return true; }
				if (id < item.id) {
					max = index;
				} else {
					min = index + 1;
				}
			}
			item = null;
			if (logError) { UnityEngine.Debug.LogError("DIYPartQualitySkill表找不到 => " + id); }
			return false;
		}

	}

	[System.Serializable]
	public class DIYPartQualitySkillItem {

		[SerializeField, HideInInspector]
		private int _Id;
		/// <summary>
		/// ID
		/// </summary>
		public int id { get { return _Id; } }

		[SerializeField, HideInInspector]
		private int _Requirement;
		/// <summary>
		/// 品质要求
		/// </summary>
		public int Requirement { get { return _Requirement; } }

		[SerializeField, HideInInspector]
		private int[] _DeleteCitiao;
		/// <summary>
		/// 升阶效果（删除词条）
		/// </summary>
		public int[] deleteCitiao { get { return _DeleteCitiao; } }

		[SerializeField, HideInInspector]
		private int[] _AddCitiao;
		/// <summary>
		/// 升阶效果（新增词条）
		/// </summary>
		public int[] addCitiao { get { return _AddCitiao; } }

		[SerializeField, HideInInspector]
		private int _Tips;
		/// <summary>
		/// 升阶描述
		/// </summary>
		public int tips { get { return _Tips; } }

		[SerializeField, HideInInspector]
		private float _Score;
		/// <summary>
		/// 品阶战力
		/// </summary>
		public float score { get { return _Score; } }

		public override string ToString() {
			return string.Format("[DIYPartQualitySkillItem]{{id:{0}, Requirement:{1}, deleteCitiao:{2}, addCitiao:{3}, tips:{4}, score:{5}}}",
				id, Requirement, array2string(deleteCitiao), array2string(addCitiao), tips, score);
		}

		private string array2string(System.Array array) {
			int len = array.Length;
			string[] strs = new string[len];
			for (int i = 0; i < len; i++) {
				strs[i] = string.Format("{0}", array.GetValue(i));
			}
			return string.Concat("[", string.Join(", ", strs), "]");
		}

		public static implicit operator bool(DIYPartQualitySkillItem item) {
			return item != null;
		}

	}

}
