using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ActivityQuestUI_Item : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_img_questBG;
		public RectTransform_Image_Container img_questBG { get { return m_img_questBG; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_questTitleBG1;
		public RectTransform_Image_Container img_questTitleBG1 { get { return m_img_questTitleBG1; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_questInfo;
		public RectTransform_Text_Container txt_questInfo { get { return m_txt_questInfo; } }

		[SerializeField]
		private RectTransform_ActivityQuestUI_Item_Item_Container m_Item;
		public RectTransform_ActivityQuestUI_Item_Item_Container Item { get { return m_Item; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_go;
		public RectTransform_Button_Image_Container btn_go { get { return m_btn_go; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_claimed;
		public RectTransform_Button_Image_Container btn_claimed { get { return m_btn_claimed; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_get;
		public RectTransform_Button_Image_Container btn_get { get { return m_btn_get; } }

		[SerializeField]
		private RectTransform_Container m_RedTips;
		public RectTransform_Container RedTips { get { return m_RedTips; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_questPro;
		public RectTransform_Text_Container txt_questPro { get { return m_txt_questPro; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_exp_preview;
		public RectTransform_Image_Container img_exp_preview { get { return m_img_exp_preview; } }

		[System.Serializable]
		public class RectTransform_ActivityQuestUI_Item_Item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ActivityQuestUI_Item_Item m_Item;
			public ActivityQuestUI_Item_Item Item { get { return m_Item; } }

			[System.NonSerialized] public List<ActivityQuestUI_Item_Item> mCachedList = new List<ActivityQuestUI_Item_Item>();
			private Queue<ActivityQuestUI_Item_Item> mCachedInstances;
			public ActivityQuestUI_Item_Item GetInstance(bool ignoreSibling = false) {
				ActivityQuestUI_Item_Item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ActivityQuestUI_Item_Item>(m_Item);
					instance.ItemInit();
				}
				Transform t0 = m_Item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ActivityQuestUI_Item_Item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ActivityQuestUI_Item_Item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ActivityQuestUI_Item_Item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
