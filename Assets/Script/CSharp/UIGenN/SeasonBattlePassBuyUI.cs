using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class SeasonBattlePassBuyUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Image_Container m_item01_icon;
		public RectTransform_Image_Container item01_icon { get { return m_item01_icon; } }

		[SerializeField]
		private RectTransform_Container m_NameNode01;
		public RectTransform_Container NameNode01 { get { return m_NameNode01; } }

		[SerializeField]
		private RectTransform_SeasonBattlePassBuyUI_leftItem_Container m_leftItem;
		public RectTransform_SeasonBattlePassBuyUI_leftItem_Container leftItem { get { return m_leftItem; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_lock1;
		public RectTransform_Button_Image_Container lock1 { get { return m_lock1; } }

		[SerializeField]
		private RectTransform_Text_Container m_blue_lable1;
		public RectTransform_Text_Container blue_lable1 { get { return m_blue_lable1; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_unlock1;
		public RectTransform_Button_Image_Container unlock1 { get { return m_unlock1; } }

		[SerializeField]
		private RectTransform_Image_Container m_item02_icon;
		public RectTransform_Image_Container item02_icon { get { return m_item02_icon; } }

		[SerializeField]
		private RectTransform_Container m_NameNode02;
		public RectTransform_Container NameNode02 { get { return m_NameNode02; } }

		[SerializeField]
		private RectTransform_SeasonBattlePassBuyUI_RightItem_Container m_RightItem;
		public RectTransform_SeasonBattlePassBuyUI_RightItem_Container RightItem { get { return m_RightItem; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_lock2;
		public RectTransform_Button_Image_Container lock2 { get { return m_lock2; } }

		[SerializeField]
		private RectTransform_Text_Container m_blue_lable2;
		public RectTransform_Text_Container blue_lable2 { get { return m_blue_lable2; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_unlock2;
		public RectTransform_Button_Image_Container unlock2 { get { return m_unlock2; } }

		[System.Serializable]
		public class RectTransform_SeasonBattlePassBuyUI_leftItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SeasonBattlePassBuyUI_leftItem m_leftItem;
			public SeasonBattlePassBuyUI_leftItem leftItem { get { return m_leftItem; } }

			[System.NonSerialized] public List<SeasonBattlePassBuyUI_leftItem> mCachedList = new List<SeasonBattlePassBuyUI_leftItem>();
			private Queue<SeasonBattlePassBuyUI_leftItem> mCachedInstances;
			public SeasonBattlePassBuyUI_leftItem GetInstance(bool ignoreSibling = false) {
				SeasonBattlePassBuyUI_leftItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SeasonBattlePassBuyUI_leftItem>(m_leftItem);
					instance.ItemInit();
				}
				Transform t0 = m_leftItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SeasonBattlePassBuyUI_leftItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SeasonBattlePassBuyUI_leftItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SeasonBattlePassBuyUI_leftItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_SeasonBattlePassBuyUI_RightItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SeasonBattlePassBuyUI_RightItem m_RightItem;
			public SeasonBattlePassBuyUI_RightItem RightItem { get { return m_RightItem; } }

			[System.NonSerialized] public List<SeasonBattlePassBuyUI_RightItem> mCachedList = new List<SeasonBattlePassBuyUI_RightItem>();
			private Queue<SeasonBattlePassBuyUI_RightItem> mCachedInstances;
			public SeasonBattlePassBuyUI_RightItem GetInstance(bool ignoreSibling = false) {
				SeasonBattlePassBuyUI_RightItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SeasonBattlePassBuyUI_RightItem>(m_RightItem);
					instance.ItemInit();
				}
				Transform t0 = m_RightItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SeasonBattlePassBuyUI_RightItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SeasonBattlePassBuyUI_RightItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SeasonBattlePassBuyUI_RightItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
