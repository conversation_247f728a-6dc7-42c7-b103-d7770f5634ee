using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class UIFightGoldStone : LDBaseUI {

		[SerializeField]
		private RectTransform_Animator_Container m_stoneNode;
		public RectTransform_Animator_Container stoneNode { get { return m_stoneNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_stone_light1;
		public RectTransform_Image_Container img_stone_light1 { get { return m_img_stone_light1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_stone_light2;
		public RectTransform_Image_Container img_stone_light2 { get { return m_img_stone_light2; } }

	}

}
