using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GuildLeagueMainUI_Tank_01 : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_Destroyed;
		public RectTransform_Image_Container Destroyed { get { return m_Destroyed; } }

		[SerializeField]
		private RectTransform_Text_Container m_TankName;
		public RectTransform_Text_Container TankName { get { return m_TankName; } }

		[SerializeField]
		private RectTransform_Text_Container m_TeamNum;
		public RectTransform_Text_Container TeamNum { get { return m_TeamNum; } }

		[SerializeField]
		private RectTransform_Button_Animator_Image_Container m_Btn;
		public RectTransform_Button_Animator_Image_Container Btn { get { return m_Btn; } }

	}

}
