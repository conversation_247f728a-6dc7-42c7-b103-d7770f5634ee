using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GuildLeagueFormatUI_Guild : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_Info;
		public RectTransform_Container Info { get { return m_Info; } }

		[SerializeField]
		private RectTransform_Container m_GuildLogo;
		public RectTransform_Container GuildLogo { get { return m_GuildLogo; } }

		[SerializeField]
		private RectTransform_Container m_SizeNode;
		public RectTransform_Container SizeNode { get { return m_SizeNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_GuildName;
		public RectTransform_Text_Container GuildName { get { return m_GuildName; } }

		[SerializeField]
		private RectTransform_Image_Container m_Kill;
		public RectTransform_Image_Container Kill { get { return m_Kill; } }

		[SerializeField]
		private RectTransform_Text_Container m_MyGuild;
		public RectTransform_Text_Container MyGuild { get { return m_MyGuild; } }

		[SerializeField]
		private RectTransform_Image_Container m_Waiting;
		public RectTransform_Image_Container Waiting { get { return m_Waiting; } }

		[SerializeField]
		private RectTransform_Container m_WaitingNode;
		public RectTransform_Container WaitingNode { get { return m_WaitingNode; } }

	}

}
