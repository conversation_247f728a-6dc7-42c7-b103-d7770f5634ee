using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class PartRankUpResultUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_Model;
		public RectTransform_Container Model { get { return m_Model; } }

		[SerializeField]
		private RectTransform_Container m_fx_lao;
		public RectTransform_Container fx_lao { get { return m_fx_lao; } }

		[SerializeField]
		private RectTransform_Container m_fx_xin;
		public RectTransform_Container fx_xin { get { return m_fx_xin; } }

		[SerializeField]
		private RectTransform_Animator_Image_Container m_image;
		public RectTransform_Animator_Image_Container image { get { return m_image; } }

		[SerializeField]
		private RectTransform_Container m_NameNode;
		public RectTransform_Container NameNode { get { return m_NameNode; } }

		[SerializeField]
		private RectTransform_Container m_img_quaTitle1;
		public RectTransform_Container img_quaTitle1 { get { return m_img_quaTitle1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rank1;
		public RectTransform_Image_Container img_rank1 { get { return m_img_rank1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_nameBG;
		public RectTransform_Image_Container img_nameBG { get { return m_img_nameBG; } }

		[SerializeField]
		private Transform_Container m_fx;
		public Transform_Container fx { get { return m_fx; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_name_upgrade;
		public RectTransform_Text_Container txt_name_upgrade { get { return m_txt_name_upgrade; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_sortIcon1;
		public RectTransform_Image_Container img_sortIcon1 { get { return m_img_sortIcon1; } }

		[SerializeField]
		private RectTransform_Container m_array_switch;
		public RectTransform_Container array_switch { get { return m_array_switch; } }

		[SerializeField]
		private RectTransform_PartRankUpResultUI_AttrItem_Container m_AttrItem;
		public RectTransform_PartRankUpResultUI_AttrItem_Container AttrItem { get { return m_AttrItem; } }

		[SerializeField]
		private RectTransform_Container m_ShowNode;
		public RectTransform_Container ShowNode { get { return m_ShowNode; } }

		[SerializeField]
		private RectTransform_Container m_showEffectNode;
		public RectTransform_Container showEffectNode { get { return m_showEffectNode; } }

		[SerializeField]
		private RectTransform_Container m_NewSkillShow;
		public RectTransform_Container NewSkillShow { get { return m_NewSkillShow; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_skillDesc;
		public RectTransform_Text_Container txt_skillDesc { get { return m_txt_skillDesc; } }

		[SerializeField]
		private RectTransform_Container m_ItemRank;
		public RectTransform_Container ItemRank { get { return m_ItemRank; } }

		[SerializeField]
		private RectTransform_Container m_effectNode;
		public RectTransform_Container effectNode { get { return m_effectNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rankLight;
		public RectTransform_Image_Container img_rankLight { get { return m_img_rankLight; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rankLogo;
		public RectTransform_Image_Container img_rankLogo { get { return m_img_rankLogo; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rankLock;
		public RectTransform_Image_Container img_rankLock { get { return m_img_rankLock; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_rankName;
		public RectTransform_Text_Container txt_rankName { get { return m_txt_rankName; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_PlayVideoBtn;
		public RectTransform_Button_Image_Container PlayVideoBtn { get { return m_PlayVideoBtn; } }

		[SerializeField]
		private RectTransform_Text_Container m_close;
		public RectTransform_Text_Container close { get { return m_close; } }

		[System.Serializable]
		public class RectTransform_PartRankUpResultUI_AttrItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private PartRankUpResultUI_AttrItem m_AttrItem;
			public PartRankUpResultUI_AttrItem AttrItem { get { return m_AttrItem; } }

			[System.NonSerialized] public List<PartRankUpResultUI_AttrItem> mCachedList = new List<PartRankUpResultUI_AttrItem>();
			private Queue<PartRankUpResultUI_AttrItem> mCachedInstances;
			public PartRankUpResultUI_AttrItem GetInstance(bool ignoreSibling = false) {
				PartRankUpResultUI_AttrItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<PartRankUpResultUI_AttrItem>(m_AttrItem);
					instance.ItemInit();
				}
				Transform t0 = m_AttrItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(PartRankUpResultUI_AttrItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<PartRankUpResultUI_AttrItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<PartRankUpResultUI_AttrItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
