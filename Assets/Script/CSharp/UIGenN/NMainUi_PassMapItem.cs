using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class NMainUi_PassMapItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_RewardNode;
		public RectTransform_Image_Container RewardNode { get { return m_RewardNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_RewardBg;
		public RectTransform_Image_Container RewardBg { get { return m_RewardBg; } }

		[SerializeField]
		private RectTransform_Container m_RewardItem;
		public RectTransform_Container RewardItem { get { return m_RewardItem; } }

		[SerializeField]
		private RectTransform_Container m_PassMapBattleIcon;
		public RectTransform_Container PassMapBattleIcon { get { return m_PassMapBattleIcon; } }

		[SerializeField]
		private RectTransform_Container m_IconNode;
		public RectTransform_Container IconNode { get { return m_IconNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_EliteLive;
		public RectTransform_Image_Container EliteLive { get { return m_EliteLive; } }

		[SerializeField]
		private RectTransform_Image_Container m_EliteDeath;
		public RectTransform_Image_Container EliteDeath { get { return m_EliteDeath; } }

		[SerializeField]
		private RectTransform_Image_Container m_BossLive;
		public RectTransform_Image_Container BossLive { get { return m_BossLive; } }

		[SerializeField]
		private RectTransform_Image_Container m_BossDeath;
		public RectTransform_Image_Container BossDeath { get { return m_BossDeath; } }

		[SerializeField]
		private RectTransform_Text_Container m_PassOrder;
		public RectTransform_Text_Container PassOrder { get { return m_PassOrder; } }

	}

}
