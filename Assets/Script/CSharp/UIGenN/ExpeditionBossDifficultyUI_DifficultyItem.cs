using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ExpeditionBossDifficultyUI_DifficultyItem : LDBaseResUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_Item_Click;
		public RectTransform_Button_Image_Container Item_Click { get { return m_Item_Click; } }

		[SerializeField]
		private RectTransform_Image_Container m_DifficultyBG_Orange;
		public RectTransform_Image_Container DifficultyBG_Orange { get { return m_DifficultyBG_Orange; } }

		[SerializeField]
		private RectTransform_Image_Container m_DifficultyBG_Purpl;
		public RectTransform_Image_Container DifficultyBG_Purpl { get { return m_DifficultyBG_Purpl; } }

		[SerializeField]
		private RectTransform_Image_Container m_DifficultyBG_Blue;
		public RectTransform_Image_Container DifficultyBG_Blue { get { return m_DifficultyBG_Blue; } }

		[SerializeField]
		private RectTransform_Text_Container m_Difficulty_Title;
		public RectTransform_Text_Container Difficulty_Title { get { return m_Difficulty_Title; } }

		[SerializeField]
		private RectTransform_Text_Container m_Difficulty_Txt;
		public RectTransform_Text_Container Difficulty_Txt { get { return m_Difficulty_Txt; } }

		[SerializeField]
		private RectTransform_ExpeditionBossDifficultyUI_DifficultyItem_Item_Container m_Item;
		public RectTransform_ExpeditionBossDifficultyUI_DifficultyItem_Item_Container Item { get { return m_Item; } }

		[SerializeField]
		private RectTransform_Container m_Exchange;
		public RectTransform_Container Exchange { get { return m_Exchange; } }

		[SerializeField]
		private RectTransform_Image_Container m_Exchange_Title_Dec;
		public RectTransform_Image_Container Exchange_Title_Dec { get { return m_Exchange_Title_Dec; } }

		[SerializeField]
		private RectTransform_Text_Container m_Exchange_Title_Txt02;
		public RectTransform_Text_Container Exchange_Title_Txt02 { get { return m_Exchange_Title_Txt02; } }

		[SerializeField]
		private RectTransform_Container m_Integral;
		public RectTransform_Container Integral { get { return m_Integral; } }

		[SerializeField]
		private RectTransform_Image_Container m_Integral_Title_Dec;
		public RectTransform_Image_Container Integral_Title_Dec { get { return m_Integral_Title_Dec; } }

		[SerializeField]
		private RectTransform_Text_Container m_Integral_Title_Txt02;
		public RectTransform_Text_Container Integral_Title_Txt02 { get { return m_Integral_Title_Txt02; } }

		[SerializeField]
		private RectTransform_Container m_Lucky;
		public RectTransform_Container Lucky { get { return m_Lucky; } }

		[SerializeField]
		private RectTransform_Image_Container m_Lucky_Title_Dec;
		public RectTransform_Image_Container Lucky_Title_Dec { get { return m_Lucky_Title_Dec; } }

		[SerializeField]
		private RectTransform_Text_Container m_Lucky_Title_Txt02;
		public RectTransform_Text_Container Lucky_Title_Txt02 { get { return m_Lucky_Title_Txt02; } }

		[SerializeField]
		private RectTransform_Image_Container m_Selelct;
		public RectTransform_Image_Container Selelct { get { return m_Selelct; } }

		[System.Serializable]
		public class RectTransform_ExpeditionBossDifficultyUI_DifficultyItem_Item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ExpeditionBossDifficultyUI_DifficultyItem_Item m_Item;
			public ExpeditionBossDifficultyUI_DifficultyItem_Item Item { get { return m_Item; } }

			[System.NonSerialized] public List<ExpeditionBossDifficultyUI_DifficultyItem_Item> mCachedList = new List<ExpeditionBossDifficultyUI_DifficultyItem_Item>();
			private Queue<ExpeditionBossDifficultyUI_DifficultyItem_Item> mCachedInstances;
			public ExpeditionBossDifficultyUI_DifficultyItem_Item GetInstance(bool ignoreSibling = false) {
				ExpeditionBossDifficultyUI_DifficultyItem_Item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ExpeditionBossDifficultyUI_DifficultyItem_Item>(m_Item);
					instance.ItemInit();
				}
				Transform t0 = m_Item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ExpeditionBossDifficultyUI_DifficultyItem_Item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ExpeditionBossDifficultyUI_DifficultyItem_Item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ExpeditionBossDifficultyUI_DifficultyItem_Item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
