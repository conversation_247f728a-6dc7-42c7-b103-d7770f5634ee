using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class WelfareUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_node_dailyAward;
		public RectTransform_Container node_dailyAward { get { return m_node_dailyAward; } }

		[SerializeField]
		private RectTransform_Container m_Award;
		public RectTransform_Container Award { get { return m_Award; } }

		[SerializeField]
		private RectTransform_Image_Container m_Scroll_View;
		public RectTransform_Image_Container Scroll_View { get { return m_Scroll_View; } }

		[SerializeField]
		private RectTransform_Container m_Award_Content;
		public RectTransform_Container Award_Content { get { return m_Award_Content; } }

		[SerializeField]
		private RectTransform_WelfareUI_Award_Item_Container m_Award_Item;
		public RectTransform_WelfareUI_Award_Item_Container Award_Item { get { return m_Award_Item; } }

		[SerializeField]
		private RectTransform_Image_Container m_PassRewardBarBg;
		public RectTransform_Image_Container PassRewardBarBg { get { return m_PassRewardBarBg; } }

		[SerializeField]
		private RectTransform_Image_Container m_PassRewardBar;
		public RectTransform_Image_Container PassRewardBar { get { return m_PassRewardBar; } }

		[SerializeField]
		private RectTransform_Text_Container m_ProgressDate_Num;
		public RectTransform_Text_Container ProgressDate_Num { get { return m_ProgressDate_Num; } }

		[SerializeField]
		private RectTransform_WelfareUI_item_Container m_item;
		public RectTransform_WelfareUI_item_Container item { get { return m_item; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_WelfareUI_WelfareFuncBtn_Container m_WelfareFuncBtn;
		public RectTransform_WelfareUI_WelfareFuncBtn_Container WelfareFuncBtn { get { return m_WelfareFuncBtn; } }

		[System.Serializable]
		public class RectTransform_WelfareUI_Award_Item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private WelfareUI_Award_Item m_Award_Item;
			public WelfareUI_Award_Item Award_Item { get { return m_Award_Item; } }

			[System.NonSerialized] public List<WelfareUI_Award_Item> mCachedList = new List<WelfareUI_Award_Item>();
			private Queue<WelfareUI_Award_Item> mCachedInstances;
			public WelfareUI_Award_Item GetInstance(bool ignoreSibling = false) {
				WelfareUI_Award_Item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<WelfareUI_Award_Item>(m_Award_Item);
					instance.ItemInit();
				}
				Transform t0 = m_Award_Item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(WelfareUI_Award_Item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<WelfareUI_Award_Item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<WelfareUI_Award_Item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_WelfareUI_item_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private WelfareUI_item m_item;
			public WelfareUI_item item { get { return m_item; } }

			[System.NonSerialized] public List<WelfareUI_item> mCachedList = new List<WelfareUI_item>();
			private Queue<WelfareUI_item> mCachedInstances;
			public WelfareUI_item GetInstance(bool ignoreSibling = false) {
				WelfareUI_item instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<WelfareUI_item>(m_item);
					instance.ItemInit();
				}
				Transform t0 = m_item.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(WelfareUI_item instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<WelfareUI_item>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<WelfareUI_item> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_WelfareUI_WelfareFuncBtn_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private WelfareUI_WelfareFuncBtn m_WelfareFuncBtn;
			public WelfareUI_WelfareFuncBtn WelfareFuncBtn { get { return m_WelfareFuncBtn; } }

			[System.NonSerialized] public List<WelfareUI_WelfareFuncBtn> mCachedList = new List<WelfareUI_WelfareFuncBtn>();
			private Queue<WelfareUI_WelfareFuncBtn> mCachedInstances;
			public WelfareUI_WelfareFuncBtn GetInstance(bool ignoreSibling = false) {
				WelfareUI_WelfareFuncBtn instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<WelfareUI_WelfareFuncBtn>(m_WelfareFuncBtn);
					instance.ItemInit();
				}
				Transform t0 = m_WelfareFuncBtn.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(WelfareUI_WelfareFuncBtn instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<WelfareUI_WelfareFuncBtn>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<WelfareUI_WelfareFuncBtn> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
