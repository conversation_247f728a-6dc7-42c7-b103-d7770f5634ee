using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class MarqueeTips : LDBaseUI {

		[SerializeField]
		private RectTransform_Image_Container m_MarqueeMask;
		public RectTransform_Image_Container MarqueeMask { get { return m_MarqueeMask; } }

		[SerializeField]
		private RectTransform_Text_Container m_TipsText;
		public RectTransform_Text_Container TipsText { get { return m_TipsText; } }

	}

}
