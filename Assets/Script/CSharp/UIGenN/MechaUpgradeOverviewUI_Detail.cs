using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class MechaUpgradeOverviewUI_Detail : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_entry_bg;
		public RectTransform_Container entry_bg { get { return m_entry_bg; } }

		[SerializeField]
		private RectTransform_Image_Container m_entry_1;
		public RectTransform_Image_Container entry_1 { get { return m_entry_1; } }

		[SerializeField]
		private RectTransform_Image_Container m_entry_2;
		public RectTransform_Image_Container entry_2 { get { return m_entry_2; } }

		[SerializeField]
		private RectTransform_Container m_unlock;
		public RectTransform_Container unlock { get { return m_unlock; } }

		[SerializeField]
		private RectTransform_Image_Container m_QuaIcon_icon;
		public RectTransform_Image_Container QuaIcon_icon { get { return m_QuaIcon_icon; } }

		[SerializeField]
		private RectTransform_Image_Container m_mask;
		public RectTransform_Image_Container mask { get { return m_mask; } }

		[SerializeField]
		private RectTransform_Image_Container m_QuaIcon_Lock;
		public RectTransform_Image_Container QuaIcon_Lock { get { return m_QuaIcon_Lock; } }

		[SerializeField]
		private RectTransform_Image_Container m_nextUnlock;
		public RectTransform_Image_Container nextUnlock { get { return m_nextUnlock; } }

		[SerializeField]
		private RectTransform_Text_Container m_CitiaoDesc;
		public RectTransform_Text_Container CitiaoDesc { get { return m_CitiaoDesc; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_PlayVideoBtn;
		public RectTransform_Button_Image_Container PlayVideoBtn { get { return m_PlayVideoBtn; } }

	}

}
