using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ExpeditionShiJianUI_Option : LDBaseResUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_Option_BG01;
		public RectTransform_Button_Image_Container Option_BG01 { get { return m_Option_BG01; } }

		[SerializeField]
		private RectTransform_Text_Container m_Option_Txt01;
		public RectTransform_Text_Container Option_Txt01 { get { return m_Option_Txt01; } }

		[SerializeField]
		private RectTransform_Text_Container m_Option_Txt02;
		public RectTransform_Text_Container Option_Txt02 { get { return m_Option_Txt02; } }

		[SerializeField]
		private RectTransform_Image_Container m_Select;
		public RectTransform_Image_Container Select { get { return m_Select; } }

		[SerializeField]
		private RectTransform_Container m_View;
		public RectTransform_Container View { get { return m_View; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_View_Click;
		public RectTransform_Button_Image_Container View_Click { get { return m_View_Click; } }

		[SerializeField]
		private RectTransform_Image_Container m_View_BG;
		public RectTransform_Image_Container View_BG { get { return m_View_BG; } }

	}

}
