using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class PreviewBuyGiftUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Text_Container m_txt_time;
		public RectTransform_Text_Container txt_time { get { return m_txt_time; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_Help;
		public RectTransform_Text_Container txt_Help { get { return m_txt_Help; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Image_Container m_ActivityImage;
		public RectTransform_Image_Container ActivityImage { get { return m_ActivityImage; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_Title;
		public RectTransform_Text_Container txt_Title { get { return m_txt_Title; } }

		[SerializeField]
		private RectTransform_Text_Container m_Value_Num;
		public RectTransform_Text_Container Value_Num { get { return m_Value_Num; } }

		[SerializeField]
		private RectTransform_Container m_Content;
		public RectTransform_Container Content { get { return m_Content; } }

		[SerializeField]
		private RectTransform_PreviewBuyGiftUI_itemNode_Container m_itemNode;
		public RectTransform_PreviewBuyGiftUI_itemNode_Container itemNode { get { return m_itemNode; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_Buy;
		public RectTransform_Button_Image_Container btn_Buy { get { return m_btn_Buy; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_Buy;
		public RectTransform_Text_Container txt_Buy { get { return m_txt_Buy; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_UnBuy;
		public RectTransform_Button_Image_Container btn_UnBuy { get { return m_btn_UnBuy; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_UnBuy;
		public RectTransform_Text_Container txt_UnBuy { get { return m_txt_UnBuy; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_Receive;
		public RectTransform_Button_Image_Container btn_Receive { get { return m_btn_Receive; } }

		[SerializeField]
		private RectTransform_Animator_Container m_PrivilegesTips;
		public RectTransform_Animator_Container PrivilegesTips { get { return m_PrivilegesTips; } }

		[SerializeField]
		private RectTransform_Text_Container m_PrivilegesTips_Txt;
		public RectTransform_Text_Container PrivilegesTips_Txt { get { return m_PrivilegesTips_Txt; } }

		[System.Serializable]
		public class RectTransform_PreviewBuyGiftUI_itemNode_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private PreviewBuyGiftUI_itemNode m_itemNode;
			public PreviewBuyGiftUI_itemNode itemNode { get { return m_itemNode; } }

			[System.NonSerialized] public List<PreviewBuyGiftUI_itemNode> mCachedList = new List<PreviewBuyGiftUI_itemNode>();
			private Queue<PreviewBuyGiftUI_itemNode> mCachedInstances;
			public PreviewBuyGiftUI_itemNode GetInstance(bool ignoreSibling = false) {
				PreviewBuyGiftUI_itemNode instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<PreviewBuyGiftUI_itemNode>(m_itemNode);
					instance.ItemInit();
				}
				Transform t0 = m_itemNode.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(PreviewBuyGiftUI_itemNode instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<PreviewBuyGiftUI_itemNode>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<PreviewBuyGiftUI_itemNode> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
