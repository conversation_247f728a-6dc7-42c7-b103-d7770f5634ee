using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class AircraftRestoreUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Container m_item_icon_Node;
		public RectTransform_Container item_icon_Node { get { return m_item_icon_Node; } }

		[SerializeField]
		private RectTransform_TextMeshProUGUI_Container m_txt_name_upgrade;
		public RectTransform_TextMeshProUGUI_Container txt_name_upgrade { get { return m_txt_name_upgrade; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_nowLv;
		public RectTransform_Text_Container txt_nowLv { get { return m_txt_nowLv; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_arrow;
		public RectTransform_Image_Container img_arrow { get { return m_img_arrow; } }

		[SerializeField]
		private RectTransform_AircraftRestoreUI_costItem_Container m_costItem;
		public RectTransform_AircraftRestoreUI_costItem_Container costItem { get { return m_costItem; } }

		[SerializeField]
		private RectTransform_Container m_btn_resetNode;
		public RectTransform_Container btn_resetNode { get { return m_btn_resetNode; } }

		[SerializeField]
		private Transform_Container m_button_m;
		public Transform_Container button_m { get { return m_button_m; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_reset;
		public RectTransform_Button_Image_Container btn_reset { get { return m_btn_reset; } }

		[SerializeField]
		private RectTransform_Text_Container m_Green_lable_m;
		public RectTransform_Text_Container Green_lable_m { get { return m_Green_lable_m; } }

		[System.Serializable]
		public class RectTransform_AircraftRestoreUI_costItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private AircraftRestoreUI_costItem m_costItem;
			public AircraftRestoreUI_costItem costItem { get { return m_costItem; } }

			[System.NonSerialized] public List<AircraftRestoreUI_costItem> mCachedList = new List<AircraftRestoreUI_costItem>();
			private Queue<AircraftRestoreUI_costItem> mCachedInstances;
			public AircraftRestoreUI_costItem GetInstance(bool ignoreSibling = false) {
				AircraftRestoreUI_costItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<AircraftRestoreUI_costItem>(m_costItem);
					instance.ItemInit();
				}
				Transform t0 = m_costItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(AircraftRestoreUI_costItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<AircraftRestoreUI_costItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<AircraftRestoreUI_costItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
