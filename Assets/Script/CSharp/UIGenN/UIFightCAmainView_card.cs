using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class UIFightCAmainView_card : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_img_card_BG;
		public RectTransform_Image_Container img_card_BG { get { return m_img_card_BG; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_diypart_icon;
		public RectTransform_Image_Container img_diypart_icon { get { return m_img_diypart_icon; } }

		[SerializeField]
		private RectTransform_Image_Container m_cdMask;
		public RectTransform_Image_Container cdMask { get { return m_cdMask; } }

	}

}
