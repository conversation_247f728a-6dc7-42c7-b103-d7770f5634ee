using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class DriverRollTips_list : LDBaseResUI {

		[SerializeField]
		private RectTransform_Image_Container m_queBG;
		public RectTransform_Image_Container queBG { get { return m_queBG; } }

		[SerializeField]
		private RectTransform_Image_Container m_s;
		public RectTransform_Image_Container s { get { return m_s; } }

		[SerializeField]
		private RectTransform_Text_Container m_treasure_name;
		public RectTransform_Text_Container treasure_name { get { return m_treasure_name; } }

		[SerializeField]
		private RectTransform_Text_Container m_probability;
		public RectTransform_Text_Container probability { get { return m_probability; } }

		[SerializeField]
		private RectTransform_Image_Container m_item;
		public RectTransform_Image_Container item { get { return m_item; } }

		[SerializeField]
		private RectTransform_DriverRollTips_list_item01_Container m_item01;
		public RectTransform_DriverRollTips_list_item01_Container item01 { get { return m_item01; } }

		[System.Serializable]
		public class RectTransform_DriverRollTips_list_item01_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private DriverRollTips_list_item01 m_item01;
			public DriverRollTips_list_item01 item01 { get { return m_item01; } }

			[System.NonSerialized] public List<DriverRollTips_list_item01> mCachedList = new List<DriverRollTips_list_item01>();
			private Queue<DriverRollTips_list_item01> mCachedInstances;
			public DriverRollTips_list_item01 GetInstance(bool ignoreSibling = false) {
				DriverRollTips_list_item01 instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<DriverRollTips_list_item01>(m_item01);
					instance.ItemInit();
				}
				Transform t0 = m_item01.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(DriverRollTips_list_item01 instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<DriverRollTips_list_item01>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<DriverRollTips_list_item01> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
