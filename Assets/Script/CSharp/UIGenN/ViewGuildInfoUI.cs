using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ViewGuildInfoUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Text_Container m_title;
		public RectTransform_Text_Container title { get { return m_title; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_btn_close;
		public RectTransform_Button_Image_Container btn_close { get { return m_btn_close; } }

		[SerializeField]
		private RectTransform_Container m_GuildLogoUI;
		public RectTransform_Container GuildLogoUI { get { return m_GuildLogoUI; } }

		[SerializeField]
		private RectTransform_Text_Container m_Level_Txt;
		public RectTransform_Text_Container Level_Txt { get { return m_Level_Txt; } }

		[SerializeField]
		private RectTransform_Text_Container m_GuildName_Txt;
		public RectTransform_Text_Container GuildName_Txt { get { return m_GuildName_Txt; } }

		[SerializeField]
		private RectTransform_Text_Container m_GuildId_Txt;
		public RectTransform_Text_Container GuildId_Txt { get { return m_GuildId_Txt; } }

		[SerializeField]
		private RectTransform_Text_Container m_LeaderName_Txt;
		public RectTransform_Text_Container LeaderName_Txt { get { return m_LeaderName_Txt; } }

		[SerializeField]
		private RectTransform_Text_Container m_GuildDeclaration_Txt;
		public RectTransform_Text_Container GuildDeclaration_Txt { get { return m_GuildDeclaration_Txt; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Orange_btn_CallLeader;
		public RectTransform_Button_Image_Container Orange_btn_CallLeader { get { return m_Orange_btn_CallLeader; } }

	}

}
