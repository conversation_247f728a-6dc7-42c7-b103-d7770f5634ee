using System.Collections.Generic;
using UnityEngine;

namespace LD {

	public partial class ShopUI_BuyDiamond : LDBaseResUI {

		[SerializeField]
		private RectTransform_ShopUI_BuyDiamond_DiamondItem_Container m_DiamondItem;
		public RectTransform_ShopUI_BuyDiamond_DiamondItem_Container DiamondItem { get { return m_DiamondItem; } }

		[System.Serializable]
		public class RectTransform_ShopUI_BuyDiamond_DiamondItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private ShopUI_BuyDiamond_DiamondItem m_DiamondItem;
			public ShopUI_BuyDiamond_DiamondItem DiamondItem { get { return m_DiamondItem; } }

			[System.NonSerialized] public List<ShopUI_BuyDiamond_DiamondItem> mCachedList = new List<ShopUI_BuyDiamond_DiamondItem>();
			private Queue<ShopUI_BuyDiamond_DiamondItem> mCachedInstances;
			public ShopUI_BuyDiamond_DiamondItem GetInstance(bool ignoreSibling = false) {
				ShopUI_BuyDiamond_DiamondItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<ShopUI_BuyDiamond_DiamondItem>(m_DiamondItem);
					instance.ItemInit();
				}
				Transform t0 = m_DiamondItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(ShopUI_BuyDiamond_DiamondItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<ShopUI_BuyDiamond_DiamondItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<ShopUI_BuyDiamond_DiamondItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
