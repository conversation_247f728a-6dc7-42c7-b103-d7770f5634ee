using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class GuildLeagueFightWinUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_MainFightResultUI;
		public RectTransform_Container MainFightResultUI { get { return m_MainFightResultUI; } }

		[SerializeField]
		private RectTransform_Container m_Durability;
		public RectTransform_Container Durability { get { return m_Durability; } }

		[SerializeField]
		private RectTransform_Container m_PvpNode;
		public RectTransform_Container PvpNode { get { return m_PvpNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_exp_preview;
		public RectTransform_Image_Container img_exp_preview { get { return m_img_exp_preview; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_exp_pro;
		public RectTransform_Image_Container img_exp_pro { get { return m_img_exp_pro; } }

		[SerializeField]
		private RectTransform_Text_Container m_GuildName;
		public RectTransform_Text_Container GuildName { get { return m_GuildName; } }

		[SerializeField]
		private RectTransform_Text_Container m_Num;
		public RectTransform_Text_Container Num { get { return m_Num; } }

		[SerializeField]
		private RectTransform_Text_Container m_Durability_Num;
		public RectTransform_Text_Container Durability_Num { get { return m_Durability_Num; } }

		[SerializeField]
		private RectTransform_Container m_PveNode;
		public RectTransform_Container PveNode { get { return m_PveNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_PveText;
		public RectTransform_Text_Container PveText { get { return m_PveText; } }

		[SerializeField]
		private RectTransform_GuildLeagueFightWinUI_dropItem_Container m_dropItem;
		public RectTransform_GuildLeagueFightWinUI_dropItem_Container dropItem { get { return m_dropItem; } }

		[SerializeField]
		private RectTransform_Container m_Rescue;
		public RectTransform_Container Rescue { get { return m_Rescue; } }

		[System.Serializable]
		public class RectTransform_GuildLeagueFightWinUI_dropItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private GuildLeagueFightWinUI_dropItem m_dropItem;
			public GuildLeagueFightWinUI_dropItem dropItem { get { return m_dropItem; } }

			[System.NonSerialized] public List<GuildLeagueFightWinUI_dropItem> mCachedList = new List<GuildLeagueFightWinUI_dropItem>();
			private Queue<GuildLeagueFightWinUI_dropItem> mCachedInstances;
			public GuildLeagueFightWinUI_dropItem GetInstance(bool ignoreSibling = false) {
				GuildLeagueFightWinUI_dropItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<GuildLeagueFightWinUI_dropItem>(m_dropItem);
					instance.ItemInit();
				}
				Transform t0 = m_dropItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(GuildLeagueFightWinUI_dropItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<GuildLeagueFightWinUI_dropItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<GuildLeagueFightWinUI_dropItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
