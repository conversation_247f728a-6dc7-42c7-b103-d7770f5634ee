using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class TestUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Button_Image_Container m_MainPass;
		public RectTransform_Button_Image_Container MainPass { get { return m_MainPass; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_TestCharge;
		public RectTransform_Button_Image_Container TestCharge { get { return m_TestCharge; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_TestAD;
		public RectTransform_Button_Image_Container TestAD { get { return m_TestAD; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_MainPassRecord;
		public RectTransform_Button_Image_Container MainPassRecord { get { return m_MainPassRecord; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_PveBtn;
		public RectTransform_Button_Image_Container PveBtn { get { return m_PveBtn; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_TestMecha;
		public RectTransform_Button_Image_Container TestMecha { get { return m_TestMecha; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_ServerTime;
		public RectTransform_Button_Image_Container ServerTime { get { return m_ServerTime; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_SetMarquee;
		public RectTransform_Button_Image_Container SetMarquee { get { return m_SetMarquee; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Chat;
		public RectTransform_Button_Image_Container Chat { get { return m_Chat; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_FlyItem;
		public RectTransform_Button_Image_Container FlyItem { get { return m_FlyItem; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_AttackRank;
		public RectTransform_Button_Image_Container AttackRank { get { return m_AttackRank; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_RemoveGuide;
		public RectTransform_Button_Image_Container RemoveGuide { get { return m_RemoveGuide; } }

		[SerializeField]
		private RectTransform_InputField_Image_Container m_RemoveGuideInputField;
		public RectTransform_InputField_Image_Container RemoveGuideInputField { get { return m_RemoveGuideInputField; } }

	}

}
