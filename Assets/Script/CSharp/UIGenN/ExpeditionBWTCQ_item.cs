using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class ExpeditionBWTCQ_item : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_Offset;
		public RectTransform_Container Offset { get { return m_Offset; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality;
		public RectTransform_Image_Container Quality { get { return m_Quality; } }

		[SerializeField]
		private RectTransform_Container m_CanGet;
		public RectTransform_Container CanGet { get { return m_CanGet; } }

		[SerializeField]
		private RectTransform_Image_Container m_Icon01;
		public RectTransform_Image_Container Icon01 { get { return m_Icon01; } }

		[SerializeField]
		private RectTransform_Image_Container m_Icon02_JiaoBiao;
		public RectTransform_Image_Container Icon02_JiaoBiao { get { return m_Icon02_JiaoBiao; } }

		[SerializeField]
		private RectTransform_Image_Container m_Num_bg;
		public RectTransform_Image_Container Num_bg { get { return m_Num_bg; } }

		[SerializeField]
		private RectTransform_Text_Container m_Num;
		public RectTransform_Text_Container Num { get { return m_Num; } }

		[SerializeField]
		private RectTransform_Container m_Num_Quality;
		public RectTransform_Container Num_Quality { get { return m_Num_Quality; } }

		[SerializeField]
		private RectTransform_Container m_Content;
		public RectTransform_Container Content { get { return m_Content; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality1;
		public RectTransform_Image_Container Quality1 { get { return m_Quality1; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality2;
		public RectTransform_Image_Container Quality2 { get { return m_Quality2; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality3;
		public RectTransform_Image_Container Quality3 { get { return m_Quality3; } }

		[SerializeField]
		private RectTransform_Image_Container m_Quality4;
		public RectTransform_Image_Container Quality4 { get { return m_Quality4; } }

		[SerializeField]
		private RectTransform_Container m_Time;
		public RectTransform_Container Time { get { return m_Time; } }

		[SerializeField]
		private RectTransform_Text_Container m_Time_lbl;
		public RectTransform_Text_Container Time_lbl { get { return m_Time_lbl; } }

		[SerializeField]
		private RectTransform_Container m_Mask;
		public RectTransform_Container Mask { get { return m_Mask; } }

		[SerializeField]
		private RectTransform_Image_Container m_ClickBtn;
		public RectTransform_Image_Container ClickBtn { get { return m_ClickBtn; } }

		[SerializeField]
		private RectTransform_Container m_equip;
		public RectTransform_Container equip { get { return m_equip; } }

		[SerializeField]
		private RectTransform_Image_Container m_Red;
		public RectTransform_Image_Container Red { get { return m_Red; } }

		[SerializeField]
		private RectTransform_Image_Container m_Select;
		public RectTransform_Image_Container Select { get { return m_Select; } }

		[SerializeField]
		private RectTransform_Image_Container m_Rarity;
		public RectTransform_Image_Container Rarity { get { return m_Rarity; } }

	}

}
