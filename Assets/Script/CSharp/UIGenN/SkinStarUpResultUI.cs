using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class SkinStarUpResultUI : LDBaseResUI {

		[SerializeField]
		private RectTransform_Container m_mechaModel;
		public RectTransform_Container mechaModel { get { return m_mechaModel; } }

		[SerializeField]
		private RectTransform_Container m_fx_lao;
		public RectTransform_Container fx_lao { get { return m_fx_lao; } }

		[SerializeField]
		private RectTransform_Container m_fx_xin;
		public RectTransform_Container fx_xin { get { return m_fx_xin; } }

		[SerializeField]
		private RectTransform_RawImage_Container m_ModelImage;
		public RectTransform_RawImage_Container ModelImage { get { return m_ModelImage; } }

		[SerializeField]
		private RectTransform_Animator_Image_Container m_partImage;
		public RectTransform_Animator_Image_Container partImage { get { return m_partImage; } }

		[SerializeField]
		private RectTransform_Container m_starNode;
		public RectTransform_Container starNode { get { return m_starNode; } }

		[SerializeField]
		private RectTransform_SkinStarUpResultUI_star_Container m_star;
		public RectTransform_SkinStarUpResultUI_star_Container star { get { return m_star; } }

		[SerializeField]
		private RectTransform_Container m_NameNode;
		public RectTransform_Container NameNode { get { return m_NameNode; } }

		[SerializeField]
		private RectTransform_Container m_img_quaTitle1;
		public RectTransform_Container img_quaTitle1 { get { return m_img_quaTitle1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rank1;
		public RectTransform_Image_Container img_rank1 { get { return m_img_rank1; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_nameBG;
		public RectTransform_Image_Container img_nameBG { get { return m_img_nameBG; } }

		[SerializeField]
		private Transform_Container m_fx;
		public Transform_Container fx { get { return m_fx; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_name_upgrade;
		public RectTransform_Text_Container txt_name_upgrade { get { return m_txt_name_upgrade; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_sortIcon1;
		public RectTransform_Image_Container img_sortIcon1 { get { return m_img_sortIcon1; } }

		[SerializeField]
		private RectTransform_Container m_array_switch;
		public RectTransform_Container array_switch { get { return m_array_switch; } }

		[SerializeField]
		private RectTransform_SkinStarUpResultUI_AttrItem_Container m_AttrItem;
		public RectTransform_SkinStarUpResultUI_AttrItem_Container AttrItem { get { return m_AttrItem; } }

		[SerializeField]
		private RectTransform_Container m_ShowNode;
		public RectTransform_Container ShowNode { get { return m_ShowNode; } }

		[SerializeField]
		private RectTransform_Container m_NewSkillShow;
		public RectTransform_Container NewSkillShow { get { return m_NewSkillShow; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_skillDesc;
		public RectTransform_Text_Container txt_skillDesc { get { return m_txt_skillDesc; } }

		[SerializeField]
		private RectTransform_Container m_ItemRank;
		public RectTransform_Container ItemRank { get { return m_ItemRank; } }

		[SerializeField]
		private RectTransform_Container m_effectNode;
		public RectTransform_Container effectNode { get { return m_effectNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rankLight;
		public RectTransform_Image_Container img_rankLight { get { return m_img_rankLight; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rankLogo;
		public RectTransform_Image_Container img_rankLogo { get { return m_img_rankLogo; } }

		[SerializeField]
		private RectTransform_Image_Container m_img_rankLock;
		public RectTransform_Image_Container img_rankLock { get { return m_img_rankLock; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_rankName;
		public RectTransform_Text_Container txt_rankName { get { return m_txt_rankName; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_PlayVideoBtn;
		public RectTransform_Button_Image_Container PlayVideoBtn { get { return m_PlayVideoBtn; } }

		[SerializeField]
		private RectTransform_Text_Container m_close;
		public RectTransform_Text_Container close { get { return m_close; } }

		[SerializeField]
		private RectTransform_Container m_CommonModelUI;
		public RectTransform_Container CommonModelUI { get { return m_CommonModelUI; } }

		[SerializeField]
		private Transform_Container m_ModelNodeRoot;
		public Transform_Container ModelNodeRoot { get { return m_ModelNodeRoot; } }

		[SerializeField]
		private Transform_Container m_WorldNode;
		public Transform_Container WorldNode { get { return m_WorldNode; } }

		[SerializeField]
		private Transform_Container m_ModelNode;
		public Transform_Container ModelNode { get { return m_ModelNode; } }

		[SerializeField]
		private Transform_Container m_CamerRootNode;
		public Transform_Container CamerRootNode { get { return m_CamerRootNode; } }

		[SerializeField]
		private Transform_Container m_RenderCameraNode;
		public Transform_Container RenderCameraNode { get { return m_RenderCameraNode; } }

		[SerializeField]
		private Transform_Animator_Container m_diybeijing;
		public Transform_Animator_Container diybeijing { get { return m_diybeijing; } }

		[System.Serializable]
		public class RectTransform_SkinStarUpResultUI_AttrItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SkinStarUpResultUI_AttrItem m_AttrItem;
			public SkinStarUpResultUI_AttrItem AttrItem { get { return m_AttrItem; } }

			[System.NonSerialized] public List<SkinStarUpResultUI_AttrItem> mCachedList = new List<SkinStarUpResultUI_AttrItem>();
			private Queue<SkinStarUpResultUI_AttrItem> mCachedInstances;
			public SkinStarUpResultUI_AttrItem GetInstance(bool ignoreSibling = false) {
				SkinStarUpResultUI_AttrItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SkinStarUpResultUI_AttrItem>(m_AttrItem);
					instance.ItemInit();
				}
				Transform t0 = m_AttrItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SkinStarUpResultUI_AttrItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SkinStarUpResultUI_AttrItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SkinStarUpResultUI_AttrItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_SkinStarUpResultUI_star_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private SkinStarUpResultUI_star m_star;
			public SkinStarUpResultUI_star star { get { return m_star; } }

			[System.NonSerialized] public List<SkinStarUpResultUI_star> mCachedList = new List<SkinStarUpResultUI_star>();
			private Queue<SkinStarUpResultUI_star> mCachedInstances;
			public SkinStarUpResultUI_star GetInstance(bool ignoreSibling = false) {
				SkinStarUpResultUI_star instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<SkinStarUpResultUI_star>(m_star);
					instance.ItemInit();
				}
				Transform t0 = m_star.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(SkinStarUpResultUI_star instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<SkinStarUpResultUI_star>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<SkinStarUpResultUI_star> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
