using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class PveFightWinUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Container m_MainFightResultUI;
		public RectTransform_Container MainFightResultUI { get { return m_MainFightResultUI; } }

		[SerializeField]
		private RectTransform_Container m_root;
		public RectTransform_Container root { get { return m_root; } }

		[SerializeField]
		private RectTransform_Container m_title_win;
		public RectTransform_Container title_win { get { return m_title_win; } }

		[SerializeField]
		private RectTransform_Container m_title_fail;
		public RectTransform_Container title_fail { get { return m_title_fail; } }

		[SerializeField]
		private RectTransform_Container m_infoNode;
		public RectTransform_Container infoNode { get { return m_infoNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_stageName;
		public RectTransform_Text_Container txt_stageName { get { return m_txt_stageName; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_passPro;
		public RectTransform_Text_Container txt_passPro { get { return m_txt_passPro; } }

		[SerializeField]
		private RectTransform_Text_Container m_time_txt;
		public RectTransform_Text_Container time_txt { get { return m_time_txt; } }

		[SerializeField]
		private RectTransform_Container m_timeoutNode;
		public RectTransform_Container timeoutNode { get { return m_timeoutNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_timeOut;
		public RectTransform_Text_Container txt_timeOut { get { return m_txt_timeOut; } }

		[SerializeField]
		private RectTransform_Image_Container m_newRecord;
		public RectTransform_Image_Container newRecord { get { return m_newRecord; } }

		[SerializeField]
		private RectTransform_Image_Container m_bg_win;
		public RectTransform_Image_Container bg_win { get { return m_bg_win; } }

		[SerializeField]
		private RectTransform_Image_Container m_bg_lose;
		public RectTransform_Image_Container bg_lose { get { return m_bg_lose; } }

		[SerializeField]
		private RectTransform_Image_Container m_Nothing;
		public RectTransform_Image_Container Nothing { get { return m_Nothing; } }

		[SerializeField]
		private RectTransform_PveFightWinUI_dropItem_Container m_dropItem;
		public RectTransform_PveFightWinUI_dropItem_Container dropItem { get { return m_dropItem; } }

		[SerializeField]
		private RectTransform_PveFightWinUI_damageItem_Container m_damageItem;
		public RectTransform_PveFightWinUI_damageItem_Container damageItem { get { return m_damageItem; } }

		[SerializeField]
		private RectTransform_Container m_Button_ad_play;
		public RectTransform_Container Button_ad_play { get { return m_Button_ad_play; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_gray_btn;
		public RectTransform_Button_Image_Container gray_btn { get { return m_gray_btn; } }

		[SerializeField]
		private RectTransform_Text_Container m_cyan_lable_ad_b;
		public RectTransform_Text_Container cyan_lable_ad_b { get { return m_cyan_lable_ad_b; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_fightresult_btn_ad;
		public RectTransform_Button_Image_Container fightresult_btn_ad { get { return m_fightresult_btn_ad; } }

		[SerializeField]
		private RectTransform_Text_Container m_fightresult_lable_ad_a;
		public RectTransform_Text_Container fightresult_lable_ad_a { get { return m_fightresult_lable_ad_a; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_remain;
		public RectTransform_Text_Container txt_remain { get { return m_txt_remain; } }

		[SerializeField]
		private RectTransform_Container m_Button_Return_M;
		public RectTransform_Container Button_Return_M { get { return m_Button_Return_M; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_Back_btn_m;
		public RectTransform_Button_Image_Container Back_btn_m { get { return m_Back_btn_m; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_SendBtn;
		public RectTransform_Button_Image_Container SendBtn { get { return m_SendBtn; } }

		[SerializeField]
		private RectTransform_Container m_titleNode;
		public RectTransform_Container titleNode { get { return m_titleNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_player_1_damage;
		public RectTransform_Image_Container player_1_damage { get { return m_player_1_damage; } }

		[SerializeField]
		private RectTransform_Text_Container m_player_1_damage_num;
		public RectTransform_Text_Container player_1_damage_num { get { return m_player_1_damage_num; } }

		[SerializeField]
		private RectTransform_Container m_player_2_damage;
		public RectTransform_Container player_2_damage { get { return m_player_2_damage; } }

		[SerializeField]
		private RectTransform_Text_Container m_player_2_damage_num;
		public RectTransform_Text_Container player_2_damage_num { get { return m_player_2_damage_num; } }

		[SerializeField]
		private RectTransform_Container m_player_1;
		public RectTransform_Container player_1 { get { return m_player_1; } }

		[SerializeField]
		private RectTransform_Text_Container m_player_1_name;
		public RectTransform_Text_Container player_1_name { get { return m_player_1_name; } }

		[SerializeField]
		private RectTransform_Container m_RoleHead1;
		public RectTransform_Container RoleHead1 { get { return m_RoleHead1; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_addFriend1;
		public RectTransform_Button_Image_Container addFriend1 { get { return m_addFriend1; } }

		[SerializeField]
		private RectTransform_Container m_player_2;
		public RectTransform_Container player_2 { get { return m_player_2; } }

		[SerializeField]
		private RectTransform_Text_Container m_player_2_name;
		public RectTransform_Text_Container player_2_name { get { return m_player_2_name; } }

		[SerializeField]
		private RectTransform_Container m_RoleHead2;
		public RectTransform_Container RoleHead2 { get { return m_RoleHead2; } }

		[SerializeField]
		private RectTransform_Button_Image_Container m_addFriend2;
		public RectTransform_Button_Image_Container addFriend2 { get { return m_addFriend2; } }

		[System.Serializable]
		public class RectTransform_PveFightWinUI_damageItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private PveFightWinUI_damageItem m_damageItem;
			public PveFightWinUI_damageItem damageItem { get { return m_damageItem; } }

			[System.NonSerialized] public List<PveFightWinUI_damageItem> mCachedList = new List<PveFightWinUI_damageItem>();
			private Queue<PveFightWinUI_damageItem> mCachedInstances;
			public PveFightWinUI_damageItem GetInstance(bool ignoreSibling = false) {
				PveFightWinUI_damageItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<PveFightWinUI_damageItem>(m_damageItem);
					instance.ItemInit();
				}
				Transform t0 = m_damageItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(PveFightWinUI_damageItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<PveFightWinUI_damageItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<PveFightWinUI_damageItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

		[System.Serializable]
		public class RectTransform_PveFightWinUI_dropItem_Container {

			[SerializeField]
			private GameObject m_GameObject;
			public GameObject gameObject { get { return m_GameObject; } }

			[SerializeField]
			private RectTransform m_rectTransform;
			public RectTransform rectTransform { get { return m_rectTransform; } }

			[SerializeField]
			private PveFightWinUI_dropItem m_dropItem;
			public PveFightWinUI_dropItem dropItem { get { return m_dropItem; } }

			[System.NonSerialized] public List<PveFightWinUI_dropItem> mCachedList = new List<PveFightWinUI_dropItem>();
			private Queue<PveFightWinUI_dropItem> mCachedInstances;
			public PveFightWinUI_dropItem GetInstance(bool ignoreSibling = false) {
				PveFightWinUI_dropItem instance = null;
				if (mCachedInstances != null) {
					while ((instance == null || instance.Equals(null)) && mCachedInstances.Count > 0) {
						instance = mCachedInstances.Dequeue();
					}
				}
				if (instance == null || instance.Equals(null)) {
					instance = Instantiate<PveFightWinUI_dropItem>(m_dropItem);
					instance.ItemInit();
				}
				Transform t0 = m_dropItem.transform;
				Transform t1 = instance.transform;
				t1.SetParent(t0.parent);
				t1.localPosition = t0.localPosition;
				t1.localRotation = t0.localRotation;
				t1.localScale = t0.localScale;
				if (!ignoreSibling) {
					t1.SetSiblingIndex(t0.GetSiblingIndex() + 1);
				}else{
					t1.SetAsLastSibling();
				}
				instance.ItemInit();
				instance.gameObject.SetActive(true);
				mCachedList.Add(instance);
				return instance;
			}
			public bool CacheInstance(PveFightWinUI_dropItem instance) {
				if (instance == null || instance.Equals(null)) { return false; }
				if (mCachedInstances == null) { mCachedInstances = new Queue<PveFightWinUI_dropItem>(); }
				if (mCachedInstances.Contains(instance)) { return false; }
				instance.ItemRecycle();
				instance.gameObject.SetActive(false);
				mCachedInstances.Enqueue(instance);
				return true;
			}

			public void CacheInstanceList(List<PveFightWinUI_dropItem> instanceList) {
				gameObject.SetActive(false);
				foreach (var instance in instanceList){
					CacheInstance(instance);
				}
				instanceList.Clear();
			}

			public void CacheInstanceList() {
				gameObject.SetActive(false);
				foreach (var instance in mCachedList){
					CacheInstance(instance);
				}
				mCachedList.Clear();
			}

		}

	}

}
