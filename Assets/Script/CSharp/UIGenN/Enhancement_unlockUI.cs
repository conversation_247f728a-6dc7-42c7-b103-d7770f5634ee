using UnityEngine;
using UnityEngine.UI;

namespace LD {

	public partial class Enhancement_unlockUI : LDBaseUI {

		[SerializeField]
		private RectTransform_Image_Container m_badgeIcon;
		public RectTransform_Image_Container badgeIcon { get { return m_badgeIcon; } }

		[SerializeField]
		private RectTransform_Container m_star;
		public RectTransform_Container star { get { return m_star; } }

		[SerializeField]
		private RectTransform_Image_Container m_fx;
		public RectTransform_Image_Container fx { get { return m_fx; } }

		[SerializeField]
		private RectTransform_Container m_BadgeNode;
		public RectTransform_Container BadgeNode { get { return m_BadgeNode; } }

		[SerializeField]
		private RectTransform_Image_Container m_badge;
		public RectTransform_Image_Container badge { get { return m_badge; } }

		[SerializeField]
		private RectTransform_Text_Container m_badge_name;
		public RectTransform_Text_Container badge_name { get { return m_badge_name; } }

		[SerializeField]
		private RectTransform_Container m_badge_ungrade;
		public RectTransform_Container badge_ungrade { get { return m_badge_ungrade; } }

		[SerializeField]
		private RectTransform_Image_Container m_badge_ungrade_BG;
		public RectTransform_Image_Container badge_ungrade_BG { get { return m_badge_ungrade_BG; } }

		[SerializeField]
		private RectTransform_Text_Container m_badge_ungrade_name;
		public RectTransform_Text_Container badge_ungrade_name { get { return m_badge_ungrade_name; } }

		[SerializeField]
		private RectTransform_Container m_ShowNode;
		public RectTransform_Container ShowNode { get { return m_ShowNode; } }

		[SerializeField]
		private RectTransform_Text_Container m_txt_skillDesc;
		public RectTransform_Text_Container txt_skillDesc { get { return m_txt_skillDesc; } }

		[SerializeField]
		private RectTransform_Text_Container m_close;
		public RectTransform_Text_Container close { get { return m_close; } }

	}

}
