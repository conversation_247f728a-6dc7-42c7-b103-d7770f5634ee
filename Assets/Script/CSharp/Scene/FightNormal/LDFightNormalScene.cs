using UnityEngine;

namespace LD
{
    public class LDFightNormalScene : LDFightScene
    {
        private bool m_TestPveMode = false;
        public LDFightNormalScene(LDPassInfo passInfo) : base(passInfo)
        {
            PassType = LDPassType.MainPass;
            UseAgent = true;
        }
        public override void CreateRole()
        {
            gPlayerMgr.CreateRole(gPassInfo.FightData.RoleDatas);

        }
        public override void OnDestroy()
        {
            base.OnDestroy();
            Global.gApp.gResMgr.UnLoadCrossSceneAssets();
        }

        public override void CreatePassHandle()
        {


            //if (gPassData.PassItem.id == 4000)
            //{
            //    gPassHandler = new LDGuidPassHandle_4000();
            //}
            //else
            //{
            //    gPassHandler = new LDGuidPassHandle_1050();
            //}
            //return;
            GlobalCfgItem globalCfgItem = GlobalCfg.Data.Get(LDGlobalConfigId.MechaSamples);
            if (globalCfgItem.valueInt <= 0 || globalCfgItem.valueInt == gPassData.PassItem.id)
            {
                gPassHandler = new LDGuidPassHandle_1033();
            }
            else if (gPassData.PassItem.id == 101)
            {
                gPassHandler = new LDGuidPassHandle_1001();
            }
            else if (gPassData.PassItem.id == 102)
            {
                gPassHandler = new LDGuidPassHandle_1002();
            }
            else if (gPassData.PassItem.id == 103)
            {
                gPassHandler = new LDGuidPassHandle_1003();
            }
            else if (gPassData.PassItem.id == 104)
            {
                gPassHandler = new LDGuidPassHandle_1004();
            }
            else if (gPassData.PassItem.id == 106)
            {
                gPassHandler = new LDGuidPassHandle_1006();
            }
            else if (gPassData.PassItem.id == 111)
            {
                gPassHandler = new LDGuidPassHandle_1011();
            }
            else if (gPassData.PassItem.id == 4000)
            {
                gPassHandler = new LDGuidPassHandle_4000();
            }
            else
            {
                int lastId = Global.gApp.gSystemMgr.gMainPassMgr.GetLastPassId();
                if (gPassData.PassItem.id != lastId)
                {
                    gPassHandler = new LDNormalPassHandle();
                }
                else
                {
                    gPassHandler = new LDGuidPassHandle_1050();
                }

            }
            gPassHandler.PassData = gPassData;
            gPassHandler.PreInit();
        }
        public override void PauseOnly()
        {
            m_PauseRef++;
            if (m_PauseRef == 1)
            {
                gPassHandler.SetRenderScale(LDRenderSetting.MaxRenderScale);
                gWaveMgr.SetTimeScale(0);
                Global.gApp.gAudioSource.Pause(false);
            }
        }
        public override void TryPause()
        {
            if (m_TestPveMode || RecordMode)
            {
                return;
            }
            Pause();
        }
        public override void TryResume()
        {
            if (m_TestPveMode || RecordMode)
            {
                TryDelyOpen();
                return;
            }
            Resume();
        }
        public override void Pause()
        {
            PauseOnly();
            Global.Log("Pause Game " + m_PauseRef);
            if (m_PauseRef == 1)
            {
                SetTimeScale(0);
            }
        }
        public override void Resume()
        {
            m_PauseRef--;
            Global.Log("Resume Resume " + m_PauseRef);
            m_PauseRef = Mathf.Max(m_PauseRef, 0);
            if (m_PauseRef == 0)
            {
                SetTimeScale(1);
                gPassHandler.SetRenderScale(RenderSetting.FightRenderScale);
                Global.gApp.gAudioSource.Resume();
            }
            TryDelyOpen();
        }
        public override void TryUpdateLogic(float dt)
        {
            if (RecordMode)
            {
                for (int i = 0; i < 20; i++)
                {
                    UpdateImp(DtUpdate);
                }
            }
            else
            {
                UpdateImp(dt);
                if (Orders.ServerMaxFrame == Frame)
                {
                    InputController.OnDUpdate(dt);
                }
            }
        }
    }
}
