using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

namespace LD.Data
{
    public partial class GameDatas
    {
        public string DefaultLanguage = "en";
        public Dictionary<string, Font> FontMap { get; private set; }
        public Dictionary<string, TMP_FontAsset> TMPFontMap { get; private set; }
        private HashSet<string> Languages = new HashSet<string>();
        public Font GetFont(string lan)
        {
            Font f;
            if (!FontMap.TryGetValue(lan, out f))
            {
                if (!LoadFont(lan))
                {
                    f = FontMap[DefaultLanguage];
                }
                else
                {
                    f = FontMap[lan];
                }

            }
            return f;
        }
        public TMP_FontAsset GetTMPFont(string lan)
        {
            TMP_FontAsset f;
            if (!TMPFontMap.TryGetValue(lan, out f))
            {
                if (!LoadFont(lan))
                {
                    f = TMPFontMap[DefaultLanguage];
                }
                else
                {
                    f = TMPFontMap[lan];
                }
            }
            return f;
        }
        private void LoadFont()
        {
            FontMap = new Dictionary<string, Font>();
            TMPFontMap = new Dictionary<string, TMP_FontAsset>();
            GlobalCfgItem cfg = GetLangItem();
            for (int i = 0; i < cfg.valueStringarray.Length; i += 3)
            {
                Languages.Add(cfg.valueStringarray[i + 1]);
            }
        }
        private bool LoadFont(string language)
        {
            GlobalCfgItem cfg = GetLangItem();
            for (int i = 0; i < cfg.valueStringarray.Length; i += 3)
            {
                if (language == cfg.valueStringarray[i + 1])
                {
                    string folderName = cfg.valueStringarray[i + 2];
                    Font font = Global.gApp.gResMgr.LoadFont($"Font/{folderName}/{folderName}");
                    FontMap[language] = font;
                    TMP_FontAsset tmpFont = Global.gApp.gResMgr.LoadTMPFont($"Font/{folderName}/{folderName}_tmp");
                    TMPFontMap[language] = tmpFont;
                    return true;
                }
            }
            return false;
        }
        public void CalcTMPFont(TextMeshProUGUI textMeshProUGUI,string fontName, Material material)
        {
            if (material != null)
            {
                string[] matName = material.name.Split("___");
                if (matName.Length == 2)
                {
                    string newMatName = $"Font/{fontName}/{fontName}_tmp___{matName[1]}";
                    textMeshProUGUI.fontSharedMaterial = Global.gApp.gResMgr.LoadTMPMaterial(newMatName);
                }
            }
        }
        public string GetClientCurLanguage()
        {
            string language = GetMachineLanguage();
            language = language.Replace("-", "_");
            if(language == "id")
            {
                language = "idn";
            }
            if (Languages.Contains(language))
            {
                return language;
            }
            return DefaultLanguage;
        }

        public string GetSystemCurLanguage()
        {
            string curLanguage = GetClientCurLanguage();

            foreach (LanguageItem item in Language.Data.items)
            {
                if (item.abbr.Equals(curLanguage))
                {
                    return item.sabbr;
                }
            }
            return DefaultLanguage;
        }

        public string GetMachineLanguage()
        {
            string language = Global.gApp.gSystemMgr.gLocalDataMgr.GetVal(false, LDLocalDataKeys.Language);
            if (string.IsNullOrEmpty(language))
            {
                language = Global.gApp.gGameData.GetCurSystemLanguage();
                Global.gApp.gSystemMgr.gLocalDataMgr.SetVal(false, LDLocalDataKeys.Language, language);
            }
            return language;
        }
        public GlobalCfgItem GetLangItem()
        {
            if (RuntimeSettings.Release)
            {
                return GlobalCfg.Data.Get(LDGlobalConfigId.Language_Release);
            }
            else
            {
                return GlobalCfg.Data.Get(LDGlobalConfigId.Language_Test);
            }
        }

    }
}