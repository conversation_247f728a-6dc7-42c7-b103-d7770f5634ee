using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace RTLTMPro
{
    using U_Int_32 = UInt32;

    public abstract class PtrClass<T> where T : class, new()
    {
        public T value;

        public PtrClass()
        {
            value = null;
        }

        public PtrClass(T inValue)
        {
            value = (inValue);
        }
    }

    public static class Bidi
    {
        public static int strcmp(char[] s1, string s2)
        {
            return String.CompareOrdinal(charArrToString(s1), s2);
        }

        public static int strcmp(string s1, string s2)
        {
            return String.CompareOrdinal(s1, s2);
        }

        public static string charArrToString(char[] chars)
        {
            StringBuilder sb = new StringBuilder();
            int end = 0;
            for (; end < chars.Length && chars[end] != 0; ++end)
            {
                sb.Append(chars[end]);
            }

            return sb.ToString();
        }

        public static int strlen(char[] bytes)
        {
            int end = 0;
            for (; end < bytes.Length && bytes[end] != 0; ++end)
            {
            }

            return end;
        }

        public static char[] strcat(char[] dest, string src)
        {
            int idxs = 0;
            int idxd = strlen(dest);
            for (; idxd < dest.Length - 1 && idxs < src.Length && src[idxs] != 0; ++idxd, ++idxs)
            {
                dest[idxd] = src[idxs];
            }

            dest[idxd] = '\0';
            return dest;
        }

        static void ClearLog()
        {
#if UNITY_EDITOR
            string TestLogPath = Path.Combine(UnityEngine.Application.dataPath, "../Logs/bidi_log.txt");
            File.WriteAllText(TestLogPath, "");
#endif
        }

        public static void printf(string format, params object[] arg)
        {
#if UNITY_EDITOR
            string TestLogPath = Path.Combine(UnityEngine.Application.dataPath, "../Logs/bidi_log.txt");
            File.AppendAllText(TestLogPath, String.Format(format, arg));
#endif
        }

        public static int strstr(string s1, string s2)
        {
            return s1.IndexOf(s2, StringComparison.OrdinalIgnoreCase);
        }

        public static int strstr(char[] chars, string s2)
        {
            return strstr(charArrToString(chars), s2);
        }


        /*
         * bidiref.h
         *
         * Public API for libbidir.
         */
        private const int BR_MAXINPUTLEN = 200;


        /*
         * Trace flags for control of debug output.
         *
         * These trace flags are bits set in the global traceFlags
         * unsigned 32-bit int.
         */
        private const uint Trace0 = (1); /* On by default: print final test results */
        private const uint Trace1 = (1 << 1); /* Trace main algorithm function entry. */
        private const uint Trace2 = (1 << 2); /* Trace initialization code function entry. */
        private const uint Trace3 = (1 << 3); /* Trace stack handling in X1-X8 */
        private const uint Trace4 = (1 << 4); /* Additional debug output for X1-X8 */
        private const uint Trace5 = (1 << 5); /* Trace run and sequence handling in X10 */
        private const uint Trace6 = (1 << 6); /* Trace stack handling in N0 */
        private const uint Trace7 = (1 << 7); /* Additional debug output for N0 */
        private const uint Trace8 = (1 << 8); /* Trace reordering in L2 */
        private const uint Trace9 = (1 << 9); /* Additional debug output for P2/P3 */
        private const uint Trace10 = (1 << 10); /* Trace property file parsing. */

        private const uint Trace11 = (1 << 11); /* Display all intermediate UBA results */
        private const uint Trace12 = (1 << 12); /* Additional debug output for test parsing */
        private const uint Trace13 = (1 << 13); /* Additional debug output for test parsing */
        private const uint Trace14 = (1 << 14); /* Additional debug output for test parsing */
        private const uint Trace15 = (1 << 15); /* Additional debug output for test parsing */

        private const uint TraceAll = (Trace0 | Trace1 | Trace2 | Trace3 | Trace4 | Trace5 | Trace6 | Trace7 | Trace8 |
                                       Trace9 | Trace10 | Trace11 | Trace12 | Trace13 | Trace14 | Trace15);

        enum UBA_Version_Type
        {
            UBAXX, /* Undefined rule set */
            UBA62, /* Base version. UBA 6.2.0 rule set */
            UBA63, /* UBA 6.3.0 rule set */
            UBA70, /* UBA 6.3.0 rule set; Unicode 7.0.0 repertoire */
            UBA80, /* UBA 8.0.0 rule set */
            UBA90, /* UBA 8.0.0 rule set; Unicode 9.0.0 repertoire */
            UBA100, /* UBA 8.0.0 rule set; Unicode 10.0.0 repertoire */
            UBA110, /* UBA 8.0.0 rule set; Unicode 11.0.0 repertoire */
            UBA120, /* UBA 8.0.0 rule set; Unicode 12.0.0 repertoire */
            UBA130, /* UBA 8.0.0 rule set; Unicode 13.0.0 repertoire */
            UBA140, /* UBA 8.0.0 rule set; Unicode 14.0.0 repertoire */
            UBA150, /* UBA 8.0.0 rule set; Unicode 15.0.0 repertoire */

            UBACUR /* Unspecified version: default to current UBA rules and
               use unversioned file names for data. */
        }

        /*
         * Return codes.
         *
         * These are used both by the public API and by the
         * compiled bidiref executable as return values.
         */
        private static readonly int BR_TESTOK = (1); /* Successful completion */
        private static readonly int BR_NOACTION = (0); /* Command line execution with no action taken */
        private static readonly int BR_OUTPUTERR = (-1); /* Problem in generation of output */
        private static readonly int BR_INITERR = (-2); /* Failure to read or parse input data */
        private static readonly int BR_ALLOCERR = (-3); /* Usually a memory allocation failure */
        private static readonly int BR_TESTERR = (-4); /* Test runs but does not return expected result */


        /*
         * bidirefp.h
         *
         * Private types, constants, and declarations used by bidiref.
         */

        private const int NUMVERSIONS = (13);

        /*
         * Constants defining Unihan ranges.
         */
        private const int CJK_URO_FIRST = (0x4E00);
        private const int CJK_URO_LAST = (0x9FFF);
        private const int CJK_EXTA_FIRST = (0x3400);
        private const int CJK_EXTA_LAST = (0x4DBF);
        private const int CJK_EXTB_FIRST = (0x20000);
        private const int CJK_EXTB_LAST = (0x2A6DF);
        private const int CJK_EXTC_FIRST = (0x2A700);
        private const int CJK_EXTC_LAST = (0x2B739);
        private const int CJK_EXTD_FIRST = (0x2B740);
        private const int CJK_EXTD_LAST = (0x2B81D);
        private const int CJK_EXTE_FIRST = (0x2B820);
        private const int CJK_EXTE_LAST = (0x2CEA1);
        private const int CJK_EXTF_FIRST = (0x2CEB0);
        private const int CJK_EXTF_LAST = (0x2EBE0);
        private const int CJK_EXTG_FIRST = (0x30000);
        private const int CJK_EXTG_LAST = (0x3134A);
        private const int CJK_EXTH_FIRST = (0x31350);

        private const int CJK_EXTH_LAST = (0x323AF);

        /*
         * Enumerated types for relevant Unicode properties,
         * to make reference to them easier to understand in the algorithm.
         */
        enum BIDIPROP
        {
            BIDI_None,
            BIDI_Unknown,
            BIDI_L, /* 0x00 strong: left-to-right (bc=L) */
            BIDI_ON, /* 0x01 neutral: Other Neutral (bc=ON) */
            BIDI_R, /* 0x02 strong: right-to-left (bc=R) */
            BIDI_EN, /* 0x03 weak: European Number (bc=EN) */
            BIDI_ES, /* 0x04 weak: European Number Separator (bc=ES) */
            BIDI_ET, /* 0x05 weak: European Number Terminator (bc=ET) */
            BIDI_AN, /* 0x06 weak: Arabic Number (bc=AN) */
            BIDI_CS, /* 0x07 weak: Common Number Separator (bc=CS) */
            BIDI_B, /* 0x08 neutral: Paragraph Separator (bc=B) */
            BIDI_S, /* 0x09 neutral: Segment Separator (bc=S) */
            BIDI_WS, /* 0x0A neutral: White Space (bc=WS) */
            BIDI_AL, /* 0x0B strong: right-to-left Arabic (bc=AL) */
            BIDI_NSM, /* 0x0C weak: non-spacing mark (bc=NSM) */
            BIDI_BN, /* 0x0D weak: boundary neutral (bc=BN) */
            BIDI_PDF, /* 0x0E format: pop directional formatting (bc=PDF) */
            BIDI_LRE, /* format: left-to-right embedding */
            BIDI_LRO, /* format: left-to-right override */
            BIDI_RLE, /* format: right-to-left embedding */
            BIDI_RLO, /* format: right-to-left override */
            BIDI_LRI, /* format: left-to-right isolate */
            BIDI_RLI, /* format: right-to-left isolate */
            BIDI_FSI, /* format: first strong isolate */
            BIDI_PDI, /* format: pop directional isolate */
            BIDI_MAX
        }

        enum BPTPROP
        {
            BPT_O,
            BPT_C,
            BPT_None
        }

        /*
         * File format defines.
         *
         * These defines can be used to distinguish between formats
         * of data to be read in. They are used to branch the
         * parsing functions in brinput.c.
         */
        private const int FORMAT_A = (0); /* Format used in BidiCharacterTest.txt */
        private const int FORMAT_B = (1); /* Format used in BidiTest.txt */

        /*
         * ALGORITHM_STATE is used to store how far along in
         * algorithm the data has been processed. This can be
         * used to help the debug display to show relevant
         * information.
         */
        enum ALGORITHM_STATE
        {
            State_Unitialized, /* context allocated, but no data */
            State_Initialized, /* test case data read in and parsed */
            State_P3Done, /* rules done through P3 */
            State_X8Done, /* rules done through X8 */
            State_X9Done, /* rules done through X9 */
            State_RunsDone, /* rules done through X10 part 1: runs are identified */
            State_X10Done, /* rules done through X10: runs & seqs are identified */
            State_W1Done, /* rules done through W1:  combining marks are resolved */
            State_W2Done, /* rules done through W2: */
            State_W3Done, /* rules done through W3: */
            State_W4Done, /* rules done through W4: */
            State_W5Done, /* rules done through W5: */
            State_W6Done, /* rules done through W6: */
            State_W7Done, /* rules done through W7:  weak types are resolved */
            State_N0Done, /* rules done through N0:  bracket pairs are resolved */
            State_N1Done, /* rules done through N1: */
            State_N2Done, /* rules done through N2:  neutral types are resolved */
            State_I2Done, /* rules done through I2:  implicit levels are resolved */
            State_L1Done, /* rules done through L1:  trailing whitespace resolved */
            State_L2Done, /* rules done through L2:  reordering data available */
            State_Complete /* finished application of rules: ready for checking */
        }

        /*
         * BIDIPROPDATA
         *
         * This struct is used to store property information
         * about characters and code points.
         *
         * Only limited property data is stored -- just the data
         * needed for running the UBA algorithm.
        */
        class BIDIDATA
        {
            public int han;
            public BIDIPROP bidivalue;
            public U_Int_32 bpb;
            public BPTPROP bpt;
        }

        // typedef BIDIDATA *BDPTR;
        class BDPTR : PtrClass<BIDIDATA>
        {
        }


        private const uint BPB_None = (0xFFFFFFFF);

        enum Paragraph_Direction
        {
            Dir_LTR,
            Dir_RTL,
            Dir_Auto,
            Dir_Unknown
        }

        enum D_Override_Status
        {
            Override_Neutral,
            Override_LTR,
            Override_RTL
        }

        class STATUSSTACKELEMENT
        {
            public int embedding_level;
            public D_Override_Status override_status; /* direction */
            public int isolate_status; /* boolean */
        }

        // typedef STATUSSTACKELEMENT *STACKPTR;
        class STACKPTR : PtrClass<STATUSSTACKELEMENT>
        {
        }

        /*
         * The maximum_depth for embedding for UBA62.
         */
        private const int MAX_DEPTH_62 = 61;

        /*
         * The maximum_depth for embedding for UBA63.
         */
        private const int MAX_DEPTH_63 = 125;

        /*
         * Typedefs used in the bracket pairing algorithm in rule N0.
         */
        class BRACKETSTACKELEMENT
        {
            public U_Int_32 bracket;
            public int pos;
        }

        // typedef BRACKETSTACKELEMENT *BRACKETSTACKPTR;
        class BRACKETSTACKPTR : PtrClass<BRACKETSTACKELEMENT>
        {
        }

        class PAIRINGELEMENT
        {
            public PAIRINGELEMENT next;
            public int openingpos;
            public int closingpos;
        }

        // typedef PAIRINGELEMENT *PAIRINGPTR;
        class PAIRINGPTR : PtrClass<PAIRINGELEMENT>
        {
        }


        private const int MAXPAIRINGDEPTH = (63);

        /* 
         * Provide a reasonable length for input-handling buffers, based
         * on MAXINPUTLEN, which limits how many code points the
         * implementation will handle for an input string.
         */
        private const int BUFFERLEN = (5 * BR_MAXINPUTLEN); /* for data chunks parsed from input */
        private const int RAWBUFFERLEN = (2 * BUFFERLEN); /* full unparsed input line */

        /*
         * BIDIUNIT
         *
         * This struct is the primitive manipulated by
         * the UBA reference implementation. It is used
         * to construct a vector in the UBACONTEXT,
         * containing the character data (stored as UTF-32),
         * the looked-up property data, so that the UBA,
         * which works primarily on the basis of the Bidi_Class
         * values, has the correct input, along with the
         * level and order information.
         *
         * Starting with Version 2.0 of bidiref, a number of
         * accelerator flags are added, to speed up checking of
         * common combinations of Bidi_Class values. Each flag
         * is conceptually a Boolean, and could be stored as a bit
         * in a bitfield, but in this reference implementation
         * is simply stored as another int field in the BIDIUNIT.
         * These values are all initialized when the BIDIUNIT
         * vector is initialized for a testcase string. Some need
         * to be reset, whenever a rule changes the current bc
         * value for a character.
         */
        class BIDIUNIT
        {
            public U_Int_32 c; /* character value, stored as UTF-32 */
            public BIDIPROP bc; /* current Bidi_Class value */
            public BIDIPROP orig_bc; /* store original value for Bidi_Class */
            public bool bc_numeric; /* accelerator for bc = (AN or EN) */
            public bool bc_isoinit; /* accelerator for bc = (LRI or RLI or FSI) */
            public U_Int_32 bpb; /* bidi paired bracket property */
            public BPTPROP bpt; /* bidi paired bracket type property */
            public int level; /* current embedding level */
            public int expLevel; /* store expected level for test case */
            public int order; /* store position for reordering */
            public int order2; /* spare order array used in reversal algorithm */
        }

        // typedef BIDIUNIT *BIDIUNITPTR;
        class BIDIUNITPTR : PtrClass<BIDIUNIT>
        {
        }

        private const int NOLEVEL = (-1);

        /*
         * A text chain is simply an array of pointers to BIDIUNITs.
         *
         * This data structure is introduced to help in the abstraction
         * of rule application between UBA62 (and earlier), where
         * rules apply to level runs, and UBA63 (and later), where
         * rules apply to isolating run sequences. Because
         * isolating run sequences may contain indefinite lists
         * of discontiguous level runs, it is difficult to
         * specify rule application directly to the isolating
         * run sequences -- the concept of previous character
         * and next character in the sequence gets rather complex
         * and makes the implementation difficult.
         *
         * One possible approach to the isolating run sequences
         * would be to simply clone the entire text into
         * allocated contiguous BIDIUNIT vectors for each 
         * isolating run sequence identified. However, that makes
         * bookkeeping in the original input structure a bit more
         * complex. Instead, whenever an isolating run sequence
         * is identified, a contiguous text chain is allocated
         * and initialized instead. This keeps all the data
         * in the original input structure, and simply embodies
         * the traversal order (and prior and next relation)
         * needed for rule application.
         *
         * So that the rule application can be uniform, a
         * text chain is also allocated for level runs, as well.
         *
         * The text chain is hung off the "textChain" field in
         * both the BIDIRUN and the ISOLATING_RUN_SEQUENCE
         * struct definitions. The "len" field defines the
         * length of that array.
         */


        /*
         * BIDIRUN
         *
         * Once embedding levels are determined, the UBA
         * treats each contiguous sequence at the same level
         * as a distinct run.
         *
         * To simplify the processing of runs, a list of runs
         * is constructed as a linked list, which hangs off
         * the UBACONTEXT.
         *
         * Each BIDIRUN consists of pointers to the first
         * and last BIDIUNIT of the run, and then additional
         * information calculated during the X10 rule, when
         * the runs are identified.
         *
         * This may not be the most efficient approach to
         * an implementation, but it makes it much easier
         * to express the X10 rule and subsequent rules which
         * process the runs individually.
         *
         * The seqID is only used in processing run lists
         * into isolating run sequence lists in UBA63.
         * It is initialized to zero. During processing
         * to identify isolating run sequences, it is set
         * to the sequence id of the isolating run sequence
         * that a run is assigned to.
         */

        class BIDIRUN
        {
            public BIDIRUN next; /* next run */
            public BIDIUNIT[] elements;
            public int len; /* explicit len, to simplify processing */
            public int runID; /* stamp run with id for debugging */
            public int seqID; /* isolating run sequence id */
            public int level; /* embedding level of this run */
            public BIDIPROP sor; /* direction of start of run: L or R */
            public BIDIPROP eor; /* direction of end of run: L or R */
            public BIDIUNIT[] textChain; /* constructed text chain */
            public int textStart; /* constructed text chain */
        }

        // typedef BIDIRUN *BIDIRUNPTR;
        class BIDIRUNPTR : PtrClass<BIDIRUN>
        {
        }

        /*
         * The BidiRunListStruct abstracts the creation of
         * a list of bidi runs for attachment to the isolating
         * run lists. Instead of duplicating all the run information
         * into that list, the list consists just of pointers to
         * the already allocated basic list of runs.
         */
        class BIDIRUNLISTELEMENT
        {
            public BIDIRUNLISTELEMENT next;
            public BIDIRUN run;
        }

        // typedef BIDIRUNLISTELEMENT *BIDIRUNLISTPTR;
        class BIDIRUNLISTPTR : PtrClass<BIDIRUNLISTELEMENT>
        {
        }

        /*
         * ISOLATING_RUN_SEQUENCE
         *
         * This is a concept introduced in UBA63.
         *
         * Essentially it consists of an ordered sequence of
         * bidi runs, as defined in BD13.
         *
         * It is implemented here by attaching the list of runs
         * associated with this particular isolating run sequence.
         * The attached list just contains pointers to each of
         * the relevant BIDIRUN structs in the already constructed
         * sequential list of runs attached to the UBACONTEXT.
         *
         * All runs associated with a single isolating run sequence
         * are at the same level, so that level can be stored
         * in the isolating run sequence struct for ease of access.
         *
         * Each isolating run sequence has a start of sequence (sos)
         * and end of sequence (eos) directional value assigned.
         * These are calculated based on the sor and eor values
         * for the associated list of runs, but again, are stored
         * in the isolating run sequence struct for ease of access.
         */

        class ISOLATING_RUN_SEQUENCE
        {
            public ISOLATING_RUN_SEQUENCE next; /* next sequence */
            public BIDIRUNLISTELEMENT theRuns; /* list of runs in this sequence */
            public BIDIRUNLISTELEMENT lastRun; /* for list appending */
            public int len; /* explicit len, to simplify processing */
            public int seqID; /* stamp seq with id for debugging */
            public int level; /* embedding level of this seq */
            public BIDIPROP sos; /* direction of start of seq: L or R */
            public BIDIPROP eos; /* directino of end of seq: L or R */
            public BIDIUNIT[] textChain; /* constructed text chain */
            public int textStart; /* constructed text chain */
        }

        // typedef ISOLATING_RUN_SEQUENCE *IRSEQPTR;
        class IRSEQPTR : PtrClass<ISOLATING_RUN_SEQUENCE>
        {
        }

        /*
         * UBACONTEXT
         *
         * This struct is used to store all context associated
         * with the bidi reference UBA processing, including
         * input, expected test output,
         * and the constructed runs and other intermediate data.
         *
         * theText, testLen, paragraphDirection are input.
         * theRuns and paragraphEmbeddingLevel are calculated.
         * expEmbeddingLevel, expOrder are parsed
         *    from the testcase data and checked against
         *    calculated values.
         * For simplicity, the expected levels data parsed
         * from the test case are stored with theText.
         *
         * Starting from Version 2.0, theText pointer is
         * allocated statically, simply pointing to a static
         * buffer of BR_MAXINPUTLEN length, to cut down on
         * repeated dynamic allocations during long testcase
         * runs.
         */
        class UBACONTEXT
        {
            public ALGORITHM_STATE state; /* track state */
            public int dirtyBit; /* used for debug output control */
            public Int64 testId; /* 64-bit id used for tagging trace output */
            public Paragraph_Direction paragraphDirection; /* input */
            public int paragraphEmbeddingLevel; /* calculated */

            public int textLen; /* input */

            // public BIDIUNIT theText; /* input */
            public BIDIUNIT[] textUnits;
            public BIDIRUN theRuns; /* calculated */
            public BIDIRUN lastRun; /* for list appending */
            public ISOLATING_RUN_SEQUENCE theSequences; /* calculated: UBA63 only */
            public ISOLATING_RUN_SEQUENCE lastSequence; /* for list appending */
            public int expEmbeddingLevel; /* expected test result */
            public string expOrder; /* expected test result */
        }

        // typedef UBACONTEXT *UBACTXTPTR;
        class UBACTXTPTR : PtrClass<UBACONTEXT>
        {
        }

        /*
         * BIDI_RULE_TYPE
         *
         * An enumerated type which facilitates lookup of
         * rule callbacks and other data by rule type.
         */
        enum BIDI_RULE_TYPE
        {
            UBA_RULE_W1,
            UBA_RULE_W2,
            UBA_RULE_W3,
            UBA_RULE_W4,
            UBA_RULE_W5,
            UBA_RULE_W6,
            UBA_RULE_W7,
            UBA_RULE_N0,
            UBA_RULE_N1,
            UBA_RULE_N2,
            UBA_RULE_Last
        };

        /*
         * RULE_CONTEXT
         *
         * A struct used to package up parameter information
         * for a class of rules, to make parameter passing
         * and function prototype neater.
         */
        class BIDI_RULE_CONTEXT
        {
            public BIDIUNIT[] textChain;
            public int textStart;
            public int len;
            public int level;
            public BIDIPROP sot;
            public BIDIPROP eot;
        };

        // typedef BIDI_RULE_CONTEXT *BIDIRULECTXTPTR;
        class BIDIRULECTXTPTR : PtrClass<BIDI_RULE_CONTEXT>
        {
        }

        /*
         * BIDIRUN_RULE_FPTR
         *
         * Function type of a UBA rule which operates on
         * a single text chain.
         *
         * This type is used to better abstract the bidi rule
         * dispatch mechanism, to distinguish version-specific
         * operations to extract level runs from the logic
         * which then applies to that run.
         */

        // typedef int (*BIDIRUN_RULE_FPTR)( BIDIRULECTXTPTR brp );
        delegate int BIDIRUN_RULE_FPTR(BIDI_RULE_CONTEXT brp);

        class RULE_TBL_ELEMENT
        {
            public BIDIRUN_RULE_FPTR ruleMethod; /* actual function callback for rule */
            public string ruleLabel; /* printable label for the function */
            public string ruleNumber; /* W1, W2, ... */
            public string ruleError;
        };

        // typedef RULE_TBL_ELEMENT *RTEPTR;
        class RTEPTR : PtrClass<RULE_TBL_ELEMENT>
        {
        }


        /*
         * brtable.c
         *
         * Module to create and access tables of Bidi_Class and any
         * other Unicode character properties required for correct
         * implementation of the UBA.
         *
         * Values needed to initialize the tables are read
         * from UnicodeData.txt and other UCD data files of explicit
         * versions, as needed.
         *
         * Exports:
         *  br_InitTable
         *  br_Init
         *  br_InitWithPath
         *  br_GetInitDone
         *  br_GetBC
         *  br_GetBPT
         *  br_GetBPB
         */
        static int linesProcessed;

        static bool initDone = false;

        private const int UNICODE_DATA = (0);
        private const int BRACKET_DATA = (1);

        /*
         * Arrays to store basic data for each character.
         */
        static BIDIDATA[] planes01 = new BIDIDATA[0x20000]; /* 2048 K  Inefficient, but gets the job done. */

        static BIDIDATA[] plane2 = new BIDIDATA[0x800]; /* Only needed for compatibility CJK range. */

        static BIDIDATA[] plane14 = new BIDIDATA[0x200]; /* Just for tag and variation selector characters. */

        static BIDIDATA handata = new BIDIDATA(); /* Static value shared for all unified CJK. */

        /***********************************************************/

        /*
         * SECTION: Utility routines.
         */
        static bool br_GetInitDone()
        {
            return (initDone);
        }

        private const char DELIM = ';';

        /*
         * skipField
         *
         * Skip over one semi-colon delimited field.
         */
        static int skipField(string src, int index)
        {
            var sp = index;
            while ((src[sp] != DELIM) && (src[sp] != '\0') && (src[sp] != '\n'))
            {
                sp++;
            }

            if ((src[sp] == DELIM) || (src[sp] == '\n'))
            {
                sp++;
            }

            return sp;
        }

        /*
         * skipSpace
         *
         * Skip over spaces.
         */
        static int skipSpace(string src, int index)
        {
            var sp = index;
            while ((src[sp] == ' ') && (src[sp] != '\0') && (src[sp] != '\n'))
            {
                sp++;
            }

            return sp;
        }

        /*
         * br_PrintErr
         *
         * Print out a diagnostic error for a parsing evaluationa failure.
         */
        static void br_PrintErr(int n, string s)
        {
            if (Trace(Trace15))
            {
                if (n == -1)
                {
                    printf("Error: Uneven hex digits in code point [{0}].\n", s);
                }
                else
                {
                    printf("Error: Invalid hex digit in code point [{0}].\n", s);
                }
            }
        }

        static void br_PrintErr(int n, char[] chars)
        {
            br_PrintErr(n, charArrToString(chars));
        }


        /***********************************************************/

        /*
         * SECTION: Array access utility routines.
         *
         * Abstract the access to the BidiData array by code point.
         */

        static BIDIDATA getBidiDataPtr(U_Int_32 i)
        {
            if (i <= 0x33FF)
            {
                return ((planes01[i]));
            }
            else if (i <= CJK_EXTA_LAST)
            {
                return (handata);
            }
            else if (i <= 0x4DFF)
            {
                return ((planes01[i]));
            }
            else if (i <= CJK_URO_LAST)
            {
                return (handata);
            }
            else if (i <= 0x1FFFF)
            {
                return ((planes01[i]));
            }
            else if (i <= CJK_EXTB_LAST)
            {
                return (handata);
            }
            else if (i <= CJK_EXTC_LAST)
            {
                return (handata);
            }
            else if (i <= CJK_EXTD_LAST)
            {
                return (handata);
            }
            else if (i <= CJK_EXTE_LAST)
            {
                return (handata);
            }
            else if (i <= CJK_EXTF_LAST)
            {
                return (handata);
            }
            else if (i <= CJK_EXTH_LAST)
            {
                return (handata);
            }
            else if ((i >= 0x2F800) && (i <= 0x2FFFF))
            {
                return ((plane2[i - 0x2F800]));
            }
            else if (i <= CJK_EXTG_LAST)
            {
                return (handata);
            }
            else if ((i >= 0xE0000) && (i <= 0xE01FF))
            {
                return ((plane14[i - 0xE0000]));
            }
            else
            {
                return null;
            }
        }

        /*
         * Exported functions to get property values from the
         * property tables for particular characters.
         */
        static BIDIPROP br_GetBC(U_Int_32 c)
        {
            var dp = getBidiDataPtr(c);
            return dp?.bidivalue ?? BIDIPROP.BIDI_Unknown;
        }

        static BPTPROP br_GetBPT(U_Int_32 c)
        {
            var dp = getBidiDataPtr(c);
            return dp?.bpt ?? BPTPROP.BPT_None;
        }

        static U_Int_32 br_GetBPB(U_Int_32 c)
        {
            var dp = getBidiDataPtr(c);
            return dp?.bpb ?? BPB_None;
        }

        /***********************************************************/

        /*
         * SECTION: Initialize the BidiData arrays.
         *
         * Special case the Han and Hangul ranges, which are not
         * explicitly listed in the UnicodeData files.
         *
         * Also special case unassigned characters in ranges that
         * have Bidi_Class defaults other than bc=L.
         */

        static void initBidiData()
        {
            U_Int_32 i;
            BIDIDATA tmp;

            for (int j = 0; j < planes01.Length; j++)
            {
                planes01[j] = new BIDIDATA();
            }

            for (int j = 0; j < plane2.Length; j++)
            {
                plane2[j] = new BIDIDATA();
            }

            for (int j = 0; j < plane14.Length; j++)
            {
                plane14[j] = new BIDIDATA();
            }

            for (int j = 0; j < statusStack.Length; j++)
            {
                statusStack[j] = new STATUSSTACKELEMENT();
            }

            for (int j = 0; j < bracketStack.Length; j++)
            {
                bracketStack[j] = new BRACKETSTACKELEMENT();
            }

            /*
             * First initialize the static record for handata.
             */
            handata.han = 1;
            handata.bidivalue = BIDIPROP.BIDI_L;
            handata.bpb = BPB_None;
            handata.bpt = BPTPROP.BPT_None;

            /*
             * Then scan through the arrays and initialize
             * all values. Skip setting values if tmp turns up
             * pointing to the handata record.
             *
             * For this initial setting, default to Bidi_Class L.
             */

            for (i = 0; i <= 0x1FFFF; i++)
            {
                tmp = getBidiDataPtr(i);
                if (tmp == handata)
                    continue;
                tmp.han = 0;
                tmp.bidivalue = BIDIPROP.BIDI_L;
                tmp.bpb = BPB_None;
                tmp.bpt = BPTPROP.BPT_None;
            }

            for (i = 0x2F800; i <= 0x2FFFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.han = 0;
                tmp.bidivalue = BIDIPROP.BIDI_L;
                tmp.bpb = BPB_None;
                tmp.bpt = BPTPROP.BPT_None;
            }

            for (i = 0xE0000; i <= 0xE01FF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.han = 0;
                tmp.bidivalue = BIDIPROP.BIDI_BN;
                tmp.bpb = BPB_None;
                tmp.bpt = BPTPROP.BPT_None;
            }

            /*
             * Special case the ranges for default values
             * for the Bidi_Class which are not L. This covers
             * the various right-to-left block ranges.
             * See latest DerivedBidiClass.txt for details.
             */
            /* Default unassigned to bc=AL */
            for (i = 0x0600; i <= 0x07BF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            for (i = 0x0860; i <= 0x08FF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            for (i = 0xFB50; i <= 0xFDCF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            for (i = 0xFDF0; i <= 0xFDFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            for (i = 0xFE70; i <= 0xFEFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            for (i = 0x10D00; i <= 0x10D3F; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            for (i = 0x10EC0; i <= 0x10EFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            for (i = 0x10F30; i <= 0x10F6F; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            for (i = 0x1EC70; i <= 0x1ECBF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            for (i = 0x1ED00; i <= 0x1ED4F; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            for (i = 0x1EE00; i <= 0x1EEFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_AL;
            }

            /* Default unassigned to bc=R */
            for (i = 0x0590; i <= 0x05FF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            for (i = 0x07C0; i <= 0x085F; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            for (i = 0xFB1D; i <= 0xFB4F; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            for (i = 0x10800; i <= 0x10CFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            for (i = 0x10D40; i <= 0x10EBF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            for (i = 0x10F00; i <= 0x10F2F; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            for (i = 0x10F70; i <= 0x10FFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            for (i = 0x1E800; i <= 0x1EC6F; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            for (i = 0x1ECC0; i <= 0x1ECFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            for (i = 0x1ED50; i <= 0x1EDFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            for (i = 0x1EF00; i <= 0x1EFFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_R;
            }

            /* Default unassigned to bc=ET */
            for (i = 0x20A0; i <= 0x20CF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_ET;
            }

            /* Default unassigned to bc=BN */
            for (i = 0x2060; i <= 0x206F; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_BN;
            }

            for (i = 0xFDD0; i <= 0xFDEF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_BN;
            }

            for (i = 0xFFF0; i <= 0xFFFF; i++)
            {
                tmp = getBidiDataPtr(i);
                tmp.bidivalue = BIDIPROP.BIDI_BN;
            }
        }

        /***********************************************************/

        /*
         * processUnicodeData
         *
         * Process a line from UnicodeData.txt
         */

        static int processUnicodeData(string buf)
        {
            char[] localbuf = new char[256];
            char[] errString = new char[80];
            U_Int_32 n = 0;

            var sp = 0;

            sp = copyField(localbuf, buf, sp);

            var nn = convertHexToInt(ref n, localbuf);
            if (nn != 0)
            {
                br_PrintErr(nn, localbuf);
                return (-1);
            }

            /* Field (field 1): Unicode name */

            sp = copyField(localbuf, buf, sp);

            /* 
             * Special case range values in UnicodeData.txt, which are
             * marked by first and last point labels in angle brackets.
             * Just skip these entries without processing.
             * The General_Category and Bidi_Class values are set
             * instead by default initialization of the table ranges.
             */

            if (localbuf[0] == '<')
            {
                if ((strstr(localbuf, "First") != -1) ||
                    (strstr(localbuf, "Last") != -1))
                {
                    return (0);
                }
            }

            /* Omit 1 field (field 2): Unicode category */

            sp = skipField(buf, sp);

            /* Omit 1 field (field 3): Combining class */

            sp = skipField(buf, sp);

            /* Span and copy 1 field (field 4): Bidi category */

            sp = copyField(localbuf, buf, sp);
            var nn2 = br_GetBCFromLabel(localbuf);
            if (nn2 == BIDIPROP.BIDI_Unknown)
            {
                br_ErrPrint($"U+{n:X4} Bidi_Class value {charArrToString(localbuf)} unknown!\n");
                return (-1);
            }

            /* At this point we have a valid BC value. Stuff it in the table. */
            var tmp = getBidiDataPtr(n);
            tmp.bidivalue = (BIDIPROP)nn2;

            /* Omit 7 fields (fields 5 - 11) */

            /* Diagnostic output */
#if NOTDEF
            if ((n >= 0x200B) && (n <= 0x202F))
            {
                printf("n={0:X4}, gc={1}, bc={2}\n", n, nn, nn2);
            }

#endif
            return (1);
        }

        /***********************************************************/

        /*
         * processBracketData
         *
         * Process a line from BidiBrackets.txt
         *
         * Lines are of the format:
         *
         * 0028; 0029; o # LEFT PARENTHESIS
         * 0029; 0028; c # RIGHT PARENTHESIS
         *
         * field 0 is a Unicode code point.
         * field 1 is the bpb property: a Unicode code point or "<none>"
         * field 2 is the bpt property: "o" or "c" or "n"
         *
         * For all of the lines actually defined in the file, bpb != none.
         *
         * Note that unlike UnicodeData.txt, BidiBrackets.txt includes
         * spaces after the semicolons. These need to be skipped over
         * before parsing the subsequent fields.
         */

        static int processBracketData(string buf)
        {
            char[] localbuf = new char[256];
            U_Int_32 n = 0;
            U_Int_32 n2 = 0;
            BPTPROP bpt;

            var sp = 0;

            sp = copyField(localbuf, buf, sp);

            var nn = convertHexToInt(ref n, localbuf);
            if (nn != 0)
            {
                br_PrintErr(nn, localbuf);
                return (-1);
            }

            sp = skipSpace(buf, sp);

            /* Field (field 1): bpb value, another code point */

            sp = copyField(localbuf, buf, sp);

            nn = convertHexToInt(ref n2, localbuf);
            if (nn != 0)
            {
                br_PrintErr(nn, localbuf);
                return (-1);
            }

            sp = skipSpace(buf, sp);

            /* Field (field 1): bpt value, a symbolic value, "o" or "c" */

            /* 
             * This parse uses copySubField atm, because this field in
             * BidiBrackets.txt is not terminated by a semicolon, but
             * rather is followed by " # " and the character name as
             * a comment.
             */

            sp = copySubField(localbuf, buf, sp);

            if (strcmp(localbuf, "o") == 0)
            {
                bpt = BPTPROP.BPT_O;
            }
            else if (strcmp(localbuf, "c") == 0)
            {
                bpt = BPTPROP.BPT_C;
            }
            else
            {
                bpt = BPTPROP.BPT_None;
            }

            /* At this point we have valid bpb and bpt values. Stuff them in the table. */
            var tmp = getBidiDataPtr(n);
            tmp.bpb = n2;
            tmp.bpt = bpt;

            return (1);
        }

        /***********************************************************/

        /*
         * SECTION: parsePropertyData
         *
         * Load and parse the UnicodeData.txt file (or any other file using
         * the same generic format), branching by status value to specific
         * parse routines for each line.
         *
         * If version == UBACUR, use the unversioned file name, otherwise,
         * attempt to load a specific, versioned file name that matches
         * the UBA version number.
         */

        static int br_parsePropertyData(int status, int version, string datapath)
        {
            int usepath;
            string fileName;
            string fqfn;

            if (Trace(Trace10))
            {
                printf("Trace: Entering br_parsePropertyData\n");
            }

            linesProcessed = 0;
            var rc = 0;

            /*
             * If datapath is null or is a zero-length string, ignore it.
             * Otherwise use it.
             */
            if (datapath == null)
            {
                usepath = 0;
            }
            else if (strcmp(datapath, "") == 0)
            {
                usepath = 0;
            }
            else
            {
                usepath = 1;
            }

            if (status == UNICODE_DATA)
            {
                if (version == (int)UBA_Version_Type.UBACUR)
                {
                    fileName = "UnicodeData.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA150)
                {
                    fileName = "UnicodeData-15.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA140)
                {
                    fileName = "UnicodeData-14.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA130)
                {
                    fileName = "UnicodeData-13.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA120)
                {
                    fileName = "UnicodeData-12.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA110)
                {
                    fileName = "UnicodeData-11.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA100)
                {
                    fileName = "UnicodeData-10.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA90)
                {
                    fileName = "UnicodeData-9.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA80)
                {
                    fileName = "UnicodeData-8.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA70)
                {
                    fileName = "UnicodeData-7.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA63)
                {
                    fileName = "UnicodeData-6.3.0.txt";
                }
                else
                {
                    fileName = "UnicodeData-6.2.0.txt";
                }
            }
            else if (status == BRACKET_DATA)
            {
                if (version == (int)UBA_Version_Type.UBACUR)
                {
                    fileName = "BidiBrackets.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA150)
                {
                    fileName = "BidiBrackets-15.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA140)
                {
                    fileName = "BidiBrackets-14.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA130)
                {
                    fileName = "BidiBrackets-13.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA120)
                {
                    fileName = "BidiBrackets-12.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA110)
                {
                    fileName = "BidiBrackets-11.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA100)
                {
                    fileName = "BidiBrackets-10.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA90)
                {
                    fileName = "BidiBrackets-9.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA80)
                {
                    fileName = "BidiBrackets-8.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA70)
                {
                    fileName = "BidiBrackets-7.0.0.txt";
                }
                else if (version == (int)UBA_Version_Type.UBA63)
                {
                    fileName = "BidiBrackets-6.3.0.txt";
                }
                else
                {
                    fileName = "Not Defined";
                }
            }
            else
            {
                fileName = "Not Defined";
            }

            if (usepath != 0)
            {
                if (datapath.EndsWith("/"))
                {
                    fqfn = datapath + fileName;
                }
                else
                {
                    fqfn = datapath + '/' + fileName;
                }
            }
            else
            {
                fqfn = fileName;
            }

            if (!TryGetFromStreamingAssets(fqfn, out byte[] fileData))
            {
                if (Trace(Trace15))
                {
                    printf("Cannot open property data file: \"{0}\"\n", fqfn);
                }

                return -1;
            }

            /* Do the work */
            using (StreamReader reader = new StreamReader(new MemoryStream(fileData)))
            {
                while (reader.Peek() >= 0)
                {
                    string buffer = reader.ReadLine();
                    /* Don't process empty lines or comments., Also check for non-zero length lines with just whitespace */
                    if (string.IsNullOrWhiteSpace(buffer) || (buffer[0] == '#') || (buffer[0] == ';'))
                        continue;
                    switch (status)
                    {
                        case UNICODE_DATA:
                            rc = processUnicodeData(buffer);
                            linesProcessed++;
                            break;
                        case BRACKET_DATA:
                            rc = processBracketData(buffer);
                            linesProcessed++;
                            break;
                        default:
                            rc = -1;
                            break;
                    }

                    if (rc == -1)
                        break;
                }
            }

            if (rc < 0)
            {
                return (rc);
            }
            else if (Trace(Trace10))
            {
                printf("Debug: Processed {0} lines from {1}\n", linesProcessed, fqfn);
            }

            return (0);
        }

        public static bool TryGetFromStreamingAssets(string filepath, out byte[] data)
        {
            var url = new Uri(Path.Combine(UnityEngine.Application.streamingAssetsPath, filepath));
            UnityEngine.Networking.UnityWebRequest request = UnityEngine.Networking.UnityWebRequest.Get(url);
            request.SendWebRequest();
            if (request.error != null)
            {
                data = null;
                return false;
            }

            while (true)
            {
                if (request.downloadHandler.isDone)
                {
                    data = request.downloadHandler.data;
                    return data != null;
                }
            }
        }

        /***********************************************************/

        /*
         * SECTION: Initialize the property tables.
         */

        /*
         * br_InitTable
         *
         * Initialize the arrays, then read in values from
         * UnicodeData.txt.
         *
         * If the input is set to FORMAT_B, then no parsed
         * property data is required. Exit without reading
         * in property data files.
         */

        static int br_InitTable(int version, string datapath)
        {
            /* Bombproof against repeat invocations */

            if (initDone)
            {
                return (1);
            }

            /* 
             * Forcing Trace10 on enables checking at runtime which
             * version of property data files are being read and
             * parsed for initializing the tables.
             */

#if NOTDEF
            TraceOn(Trace10);
            printf("Forced Trace10 on.\n");
#endif

            if (Trace(Trace10))
            {
                printf("Trace: Entering br_InitTable with version={0}\n", version);
            }

            if (GetFileFormat() == FORMAT_B)
            {
                initDone = true;
                return (1);
            }

            /*
             * Initialize the property arrays with
             * default values.
             */

            initBidiData();

            var rc = br_parsePropertyData(UNICODE_DATA, version, datapath);
            if (rc < 0)
            {
                if (Trace(Trace15))
                {
                    printf("Error: Failure in parsing UnicodeData.txt: {0}.\n", rc);
                }

                return (rc);
            }

            if (version > (int)UBA_Version_Type.UBA62)
            {
                rc = br_parsePropertyData(BRACKET_DATA, version, datapath);
                if (rc < 0)
                {
                    if (Trace(Trace15))
                    {
                        printf("Error: Failure in parsing BidiBrackets.txt: {0}.\n", rc);
                    }

                    return (rc);
                }
            }

            if (Trace(Trace0))
            {
                printf("Note:  Initialized bidiref 15.0.0 library for UBA version {0}\n", GetUBAVersionStr());
            }

            initDone = true;
            return (1);
        }

        /*
         * br_Init
         *
         * Public API. This forces the file format to FORMAT_A and
         * then initializes the tables.
         *
         * Set Trace0 and Trace15 on as the default for output display. These
         * may be turned off for the br_QueryTestResults API, or the
         * client may turn on other trace flags for br_ProcessTestResults.
         *
         * Used for an external application making use of the public APIs
         * to run and/or query individual test cases.
         */

        static int br_Init(int version)
        {
            /* Bombproof against repeat invocations */

            if (initDone)
            {
                return (1);
            }

            SetFileFormat(FORMAT_A);

            SetUBAVersion(version);

            TraceOn(Trace0);
            TraceOn(Trace15);

            var rc = br_InitTable(version, "");

            return (rc);
        }

        /*
         * br_InitWithPath
         *
         * As for br_Init, but with an explicit datapath passed in.
         */

        static int br_InitWithPath(int version, string datapath)
        {
            /* Bombproof against repeat invocations */

            if (initDone)
            {
                return (1);
            }

            SetFileFormat(FORMAT_A);

            SetUBAVersion(version);

            TraceOn(Trace0);
            TraceOn(Trace15);

            var rc = br_InitTable(version, datapath);

            //特殊处理8205
            BIDIDATA tmp = getBidiDataPtr(8205);
            tmp.bidivalue = BIDIPROP.BIDI_AL;

            BIDIDATA tmp2 = getBidiDataPtr(65039);
            tmp2.bidivalue = BIDIPROP.BIDI_AL;
            return (rc);
        }

        /*
         * brrule.c
         *
         * Module to implement the UBA rules.
         *
         * Exports:
         *	br_UBA
         *	br_Check
         */

        /******************************************************/

        /*
         * SECTION: Stack declarations.
         */

        /*
         * statusStack
         *
         * For simplicity the static stack is allocated here using the larger 
         * MAX_DEPTH value required for UBA63. The actual maximum_depth
         * used by the stack handling is set contingent on the
         * version of UBA being run.
         */

        static STATUSSTACKELEMENT[] statusStack = new STATUSSTACKELEMENT[MAX_DEPTH_63 + 2];

        static int stackTop;
        static int stackMax;

        static int maximum_depth;

        /*
         * bracketStack
         *
         * This is the stack used by the bracket pairing algorithm
         * in Rule N0.
         *
         * For the reference implementation, this stack is just declared
         * statically here. In an optimized implementation, this
         * stack might be handled differently.
         */

        static BRACKETSTACKELEMENT[] bracketStack = new BRACKETSTACKELEMENT[MAXPAIRINGDEPTH + 1];

        static int bracketStackTop;
        static int bracketStackMax;

        /*
         * pairList
         *
         * Also used in the bracket pairing algorithm in
         * Rule N0.
         */

        static PAIRINGELEMENT pairList;

        /******************************************************/

        /*
         * SECTION: Forward declaration of rule methods.
         */

        // static int br_UBA_ResolveCombiningMarks(BIDIRULECTXTPTR brcp);
        // static int br_UBA_ResolveEuropeanNumbers(BIDIRULECTXTPTR brcp);
        // static int br_UBA_ResolveAL(BIDIRULECTXTPTR brcp);
        // static int br_UBA_ResolveSeparators(BIDIRULECTXTPTR brcp);
        // static int br_UBA_ResolveTerminators(BIDIRULECTXTPTR brcp);
        // static int br_UBA_ResolveESCSET(BIDIRULECTXTPTR brcp);
        // static int br_UBA_ResolveEN(BIDIRULECTXTPTR brcp);
        // static int br_UBA_ResolvePairedBrackets(BIDIRULECTXTPTR brcp);
        // static int br_UBA_ResolveNeutralsByContext(BIDIRULECTXTPTR brcp);
        // static int br_UBA_ResolveNeutralsByLevel(BIDIRULECTXTPTR brcp);
        //
        // static int br_Dummy_Rule(BIDIRULECTXTPTR brcp);

        /*
         * bidi_rules
         *
         * This is the table of rule methods used by br_UBA_RuleDispatch
         *
         * Method-specific diagnostic strings and error messages are also
         * stored here, for convenience and generality.
         */

        static RULE_TBL_ELEMENT[] bidi_rules = new[]
        {
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_UBA_ResolveCombiningMarks, ruleLabel = "ResolveCombiningMarks", ruleNumber = "W1",
                ruleError = "resolving combining marks"
            }, /* UBA_RULE_W1 */
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_UBA_ResolveEuropeanNumbers, ruleLabel = "ResolveEuropeanNumbers", ruleNumber = "W2",
                ruleError = "resolving European numbers"
            }, /* UBA_RULE_W2 */
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_UBA_ResolveAL, ruleLabel = "ResolveAL", ruleNumber = "W3",
                ruleError = "resolving AL"
            }, /* UBA_RULE_W3 */
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_UBA_ResolveSeparators, ruleLabel = "ResolveSeparators", ruleNumber = "W4",
                ruleError = "resolving separators"
            }, /* UBA_RULE_W4 */
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_UBA_ResolveTerminators, ruleLabel = "ResolveTerminators", ruleNumber = "W5",
                ruleError = "resolving terminators"
            }, /* UBA_RULE_W5 */
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_UBA_ResolveESCSET, ruleLabel = "ResolveESCSET", ruleNumber = "W6",
                ruleError = "resolving ES, CS, ET"
            }, /* UBA_RULE_W6 */
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_UBA_ResolveEN, ruleLabel = "ResolveEN", ruleNumber = "W7",
                ruleError = "resolving EN"
            }, /* UBA_RULE_W7 */
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_UBA_ResolvePairedBrackets, ruleLabel = "ResolvePairedBrackets", ruleNumber = "N0",
                ruleError = "resolving paired brackets"
            }, /* UBA_RULE_N0 */
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_UBA_ResolveNeutralsByContext, ruleLabel = "ResolveNeutralsByContext", ruleNumber = "N1",
                ruleError = "resolving neutrals by context"
            }, /* UBA_RULE_N1 */
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_UBA_ResolveNeutralsByLevel, ruleLabel = "ResolveNeutralsByLevel", ruleNumber = "N2",
                ruleError = "resolving neutrals by level"
            }, /* UBA_RULE_N2 */
            new RULE_TBL_ELEMENT()
            {
                ruleMethod = br_Dummy_Rule, ruleLabel = "Dummy", ruleNumber = "ZZ", ruleError = "Err"
            }
        };

        static int br_Dummy_Rule(BIDI_RULE_CONTEXT brcp)
        {
            return (1);
        }

        /*
         * SECTION: Miscellaneous property check utilities.
         */

        /*
         * br_IsIsolateInitiator
         *
         * Encapsulate the checking for Bidi_Class values of isolate
         * initiator types (LRI, RLI, FSI).
         */
#if NOTDEF
        static bool br_IsIsolateInitiator(BIDIPROP bc)
        {
            if ((bc == BIDI_LRI) || (bc == BIDI_RLI) || (bc == BIDI_FSI))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

#endif
        /*
         * br_IsIsolateControl
         *
         * Encapsulate the checking for Bidi_Class values of isolate
         * initiator types (LRI, RLI, FSI) or PDI.
         */

        static bool br_IsIsolateControl(BIDIPROP bc)
        {
            if ((bc == BIDIPROP.BIDI_LRI) || (bc == BIDIPROP.BIDI_RLI) || (bc == BIDIPROP.BIDI_FSI) ||
                (bc == BIDIPROP.BIDI_PDI))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /*
         * br_FirstSignificantBC
         *
         * Return the first Bidi_Class value in the run for a non-deleted
         * element of the run (or Bidi_None, if all elements of the run
         * are deleted).
         *
         * This function is used to identify a matching PDI in a discontiguous
         * run for a sequence, when the first element (or elements) of the run is actually
         * a deleted format character. This accounts for matching across such
         * deletions, when brp.first.bc results in the wrong value.
         */

        static BIDIPROP br_FirstSignificantBC(BIDIRUN brp)
        {
            foreach (var bdu in brp.elements)
            {
                if (bdu.level != NOLEVEL)
                {
                    return (bdu.bc);
                }
            }

            return (BIDIPROP.BIDI_None);
        }

        /******************************************************/

        /*
         * SECTION: Display of Debug Output
         */

        /*
         * BidiClassLabels
         *
         * Used for converting the enumerated Bidi_Class values to
         * readable, fixed-width labels.
         */
        static string[] BidiClassLabels = new string[(int)BIDIPROP.BIDI_MAX]
        {
            "NONE", "UNKN", "   L", "  ON", "   R", "  EN", "  ES", "  ET",
            "  AN", "  CS", "   B", "   S", "  WS", "  AL", " NSM", "  BN",
            " PDF", " LRE", " LRO", " RLE", " RLO", " LRI", " RLI", " FSI",
            " PDI"
        };

        /*
         * BidiClassLabelsTrimmed
         *
         * Like BidiClassLabels, but with the initial spaces trimmed
         * out, for non-justified display.
         */
        static string[] BidiClassLabelsTrimmed = new string[(int)BIDIPROP.BIDI_MAX]
        {
            "NONE", "UNKN", "L", "ON", "R", "EN", "ES", "ET",
            "AN", "CS", "B", "S", "WS", "AL", "NSM", "BN",
            "PDF", "LRE", "LRO", "RLE", "RLO", "LRI", "RLI", "FSI",
            "PDI"
        };

        /*
         * RunFragments
         *
         * Used to store all the string fragments used to produce the
         * display of runs and sequences.
         */
        enum RUNFRAGTYPE
        {
            RF_LL,
            RF_LR,
            RF_RL,
            RF_RR,
            RF_LOpen,
            RF_ROpen,
            RF_OpenL,
            RF_OpenR,
            RF_OpenOpen,
            RF_LInit,
            RF_RInit,
            RF_OpenInit,
            RF_PDIOpen,
            RF_ISOL,
            RF_None,
            RF_Last
        };

        static string[] RunFragments = new string[(int)RUNFRAGTYPE.RF_Last]
        {
            /* Used for both run and sequence displays */
            " <LL>", " <LR>", " <RL>", " <RR>",
            " <L--", " <R--",
            "---L>", "---R>",
            "-----",
            /* Used only for sequence displays */
            " <L-[", " <R-[",
            "----[", ".]---", ".....", "     "
        };

        static void br_PrintRunFragment(RUNFRAGTYPE rf)
        {
            printf(RunFragments[(int)rf]);
        }

        /*
         * br_DisplayBlankRun
         *
         * This prints out spaces for each element of a run.
         * It is used to justify the spacing of runs when
         * printing out runs for sequences.
         */

        static void br_DisplayBlankRun(BIDIRUN brp)
        {
            int i;

            for (i = 1; i <= brp.len; i++)
            {
                br_PrintRunFragment(RUNFRAGTYPE.RF_None);
            }
        }

        /*
         * br_DisplayIsolateRun
         *
         * This prints out a row of dots for each element of a run.
         * It is used to justify the spacing of runs when
         * printing out runs for sequences.
         */

        static void br_DisplayIsolateRun(BIDIRUN brp)
        {
            int i;

            for (i = 1; i <= brp.len; i++)
            {
                br_PrintRunFragment(RUNFRAGTYPE.RF_ISOL);
            }
        }

        /*
         * br_DisplayRunRange
         *
         * This prints out runs for runIDs in
         * the first..last range, according to the fragment
         * type specified.
         */

        static void br_DisplayRunRange(BIDIRUN brp, int first, int last, RUNFRAGTYPE rft)
        {
            if (first > last)
            {
                return;
            }

            var tbrp = brp;
            while (tbrp != null)
            {
                if ((tbrp.runID >= first) && (tbrp.runID <= last))
                {
                    if (rft == RUNFRAGTYPE.RF_ISOL)
                    {
                        br_DisplayIsolateRun(tbrp);
                    }
                    else
                    {
                        br_DisplayBlankRun(tbrp);
                    }
                }

                if (tbrp.runID > last)
                {
                    break;
                }

                tbrp = tbrp.next;
            }
        }

        /*
         * br_DisplayOneRun
         *
         * Display a single run as part of a run list.
         *
         * The sor and eor values are displayed by using an "L" or "R"
         * next to the arrows indicating the start and end of the runs.
         *
         * The seqSyntax paramater is False for printing out just
         * the list of level runs.
         *
         * The seqSyntax parameter is True for printing out level
         * runs as part of the isolating run sequence display, to
         * enhance it to show isolating runs correctly.
         */

        static void br_DisplayOneRun(BIDIRUN brp, int seqSyntax)
        {
            int i;

            for (i = 1; i <= brp.len; i++)
            {
                if (i == 1)
                {
                    if (brp.len == 1)
                    {
                        if (brp.sor == BIDIPROP.BIDI_L)
                        {
                            if (seqSyntax != 0 && brp.elements[brp.len - 1].bc_isoinit)
                            {
                                br_PrintRunFragment(RUNFRAGTYPE.RF_LInit);
                            }
                            else if (brp.eor == BIDIPROP.BIDI_L)
                            {
                                br_PrintRunFragment(RUNFRAGTYPE.RF_LL);
                            }
                            else
                            {
                                br_PrintRunFragment(RUNFRAGTYPE.RF_LR);
                            }
                        }
                        else
                        {
                            if (seqSyntax != 0 && brp.elements[0].bc_isoinit)
                            {
                                br_PrintRunFragment(RUNFRAGTYPE.RF_RInit);
                            }
                            else if (brp.eor == BIDIPROP.BIDI_L)
                            {
                                br_PrintRunFragment(RUNFRAGTYPE.RF_RL);
                            }
                            else
                            {
                                br_PrintRunFragment(RUNFRAGTYPE.RF_RR);
                            }
                        }
                    }
                    else
                    {
                        if (seqSyntax != 0 && (br_FirstSignificantBC(brp) == BIDIPROP.BIDI_PDI))
                        {
                            br_PrintRunFragment(RUNFRAGTYPE.RF_PDIOpen);
                        }
                        else if (brp.sor == BIDIPROP.BIDI_L)
                        {
                            br_PrintRunFragment(RUNFRAGTYPE.RF_LOpen);
                        }
                        else
                        {
                            br_PrintRunFragment(RUNFRAGTYPE.RF_ROpen);
                        }
                    }
                }
                else if (i == brp.len)
                {
                    if (seqSyntax != 0 && brp.elements[brp.len - 1].bc_isoinit)
                    {
                        br_PrintRunFragment(RUNFRAGTYPE.RF_OpenInit);
                    }
                    else if (brp.eor == BIDIPROP.BIDI_L)
                    {
                        br_PrintRunFragment(RUNFRAGTYPE.RF_OpenL);
                    }
                    else
                    {
                        br_PrintRunFragment(RUNFRAGTYPE.RF_OpenR);
                    }
                }
                else
                {
                    br_PrintRunFragment(RUNFRAGTYPE.RF_OpenOpen);
                }
            }
        }

        /*
         * br_DisplayRunList
         *
         * Dump a list of runs in a single display line, formatted to 
         * complement and align with the listing of levels.
         */
        static void br_DisplayRunList(BIDIRUN brp)
        {
            printf("  Runs:       ");
            var tbrp = brp;
            while (tbrp != null)
            {
                br_DisplayOneRun(tbrp, 0);
                tbrp = tbrp.next;
            }

            printf("\n");
        }

        /*
         * br_DisplayOneSeqRunList
         *
         * Process the list of runs associated with the sequence,
         * displaying each in turn.
         *
         * For proper display when a run list consists of more than
         * one run, use a special justification function for any
         * runs *between* the ones displayed, to visually show
         * the enclosed, isolated runs.
         */

        static void br_DisplayOneSeqRunList(BIDIRUN brp, BIDIRUNLISTELEMENT brlp)
        {
            var tbrlp = brlp;
            /* 
             * The between displays start having effect the second
             * time through the loop, if there is a gap between
             * the previous run displayed and the next run to
             * display.
             */
            var currentrunid = 0;
            var firstrun = true;
            while (tbrlp != null)
            {
                if (!firstrun)
                {
                    br_DisplayRunRange(brp, currentrunid,
                        tbrlp.run.runID - 1, RUNFRAGTYPE.RF_ISOL);
                }

                br_DisplayOneRun(tbrlp.run, 1);
                firstrun = false;
                currentrunid = tbrlp.run.runID + 1;
                tbrlp = tbrlp.next;
            }
        }

        /*
         * br_DisplaySequenceAtLevel
         *
         * Dump a single line of sequences at the specified level.
         *
         * Strategy: To provide appropriate justification of the runs
         * printed at each level, we print a blank run for each
         * run not part of the sequences at this level. To do this,
         * keep a currentrunid value. Each time a sequence run list
         * is displayed, update the currentrunid to the last run id
         * of that sequence run list. That makes it possible to
         * keep track of the runs *between* sequences which need
         * to be displayed as blanks to keep the justification
         * correct.
         */

        static void br_DisplaySequenceAtLevel(UBACONTEXT ctxt, int level)
        {
            printf("  Seqs (L={0:D2}):", level);
            var tirp = ctxt.theSequences;
            var currentrunid = 1;
            while (tirp != null)
            {
                if (tirp.level == level)
                {
                    /* printf ( "{seq {0}}", tirp.seqID ); */
                    /*
                     * display blank runs from the currentrunid to one less
                     * than the run id of the start of the runs in the sequence.
                     * If the first run in the sequence is also the very first
                     * run, the last value will be zero, and no
                     * justification is done.
                     */
                    br_DisplayRunRange(ctxt.theRuns, currentrunid,
                        tirp.theRuns.run.runID - 1, RUNFRAGTYPE.RF_None);
                    br_DisplayOneSeqRunList(ctxt.theRuns, tirp.theRuns);
                    /*
                     * Set the currentrunid to one more than the run id of the
                     * last run in the sequence.
                     */
                    currentrunid = tirp.lastRun.run.runID + 1;
                }

                tirp = tirp.next;
            }

            printf("\n");
        }

        /*
         * br_HaveSequenceAtLevel
         *
         * A quick check to see if there is *any* sequence defined
         * with this level. This check is used to skip over what would
         * otherwise be empty display lines.
         */

        static bool br_HaveSequenceAtLevel(ISOLATING_RUN_SEQUENCE irp, int level)
        {
            var tirp = irp;
            while (tirp != null)
            {
                if (tirp.level == level)
                {
                    return true;
                }

                tirp = tirp.next;
            }

            return false;
        }

        /*
         * br_DisplaySequenceList
         *
         * Dump a list of sequence in multiple display lines, formatted to 
         * complement and align with the listing of runs.
         *
         * A separate line is printed for the sequences at each embedding
         * level, from lowest to hightest, to better display the hierarchical
         * structure of the levels.
         *
         * The sos and eos values are displayed by using an "L" or "R"
         * next to the arrows indicating the start and end of the sequences.
         *
         * An isolate initiator is indicated with a "[" at the end of a run.
         * A PDI is inidicated with a "]" at the start of a run.
         *
         * If a sequence contains a nested isolating run sequence, that
         * nested sequence is displayed with a row of dots between the
         * "[...............]" brackets that mark its start and end.
         */
        static void br_DisplaySequenceList(UBACONTEXT ctxt)
        {
#if NOTDEF
            BIDIRUNLISTELEMENT tbrlp;
#endif
            int levelix;

            /*
             * This section is temporary, to display diagnostics
             * about the sequences until the full, hierarchical
             * display is worked out.
             */
#if NOTDEF
            printf("  Sequences:  ");
            tirp = ctxt.theSequences;
            while (tirp != null)
            {
                printf(" {0} (runs: ", tirp.seqID);
                tbrlp = tirp.theRuns;
                while (tbrlp != null)
                {
                    printf("{0}", tbrlp.run.runID);
                    if (tbrlp.next != null)
                    {
                        printf(",");
                    }

                    tbrlp = tbrlp.next;
                }

                printf(")");
                if (tirp.next != null)
                {
                    printf(",");
                }

                tirp = tirp.next;
            }

            printf("\n");
#endif
            /*
              * First scan the list of sequences to find the lowest and
              * highest levels.
              */
            var highestlevel = 0;
            var lowestlevel = maximum_depth + 1;
            var tirp = ctxt.theSequences;

            while (tirp != null)
            {
                if (tirp.level > highestlevel)
                {
                    highestlevel = tirp.level;
                }

                if (tirp.level < lowestlevel)
                {
                    lowestlevel = tirp.level;
                }

                tirp = tirp.next;
            }

            /*
             * Next scan the sequence list repeatedly, from lowest to highest
             * level, displaying a representation of all sequences at that level
             * on a separate line.
             *
             * Make several checks to throttle back display output, to keep
             * it from becoming unreadable for artificially elaborate test
             * cases:
             *
             * 1. Don't display levels higher than 99.
             * 2. Don't display more than 10 sequence levels in toto.
             * 3. Skip over levels which don't actually have sequences.
             */
            var linesDisplayed = 0;
            for (levelix = lowestlevel; levelix <= highestlevel; levelix++)
            {
                if (br_HaveSequenceAtLevel(ctxt.theSequences, levelix))
                {
                    br_DisplaySequenceAtLevel(ctxt, levelix);
                    linesDisplayed++;
                }

                if (linesDisplayed > 10)
                {
                    printf("  Sequence display has been truncated at 10 levels.\n");
                    break;
                }

                if (levelix >= 99)
                {
                    printf("  Sequence display has been truncated at level 99.\n");
                    break;
                }
            }
        }

        /*
         * br_LabelForDirection
         *
         * Provide a readable label for the enumerated direction type.
         */
        static string br_LabelForDirection(Paragraph_Direction pdir)
        {
            switch (pdir)
            {
                case Paragraph_Direction.Dir_LTR:
                    return ("Dir_LTR");
                case Paragraph_Direction.Dir_RTL:
                    return ("Dir_RTL");
                case Paragraph_Direction.Dir_Auto:
                    return ("Dir_Auto");
                default:
                    return ("Bad Value");
            }
        }

        /*
         * br_DisplayState
         *
         * Dump the complete current state of the context, showing
         * the characters, their Bidi_Class settings and their level
         * settings at the current point in the processing.
         *
         * TBD: Update the line handling for display, so this can automatically
         * deal with longer text input without creating bad wraps from long lines.
         * But it may be better to just leave this as is, and assume that long
         * lines will be handled in the programming editors likely to be
         * used by those examining the output.
         */
        static void br_DisplayState(UBACONTEXT ctxt)
        {
            BIDIUNIT endOfText;
            bool mismatch;
            bool started;
            string tmp;

            /* 
             * Use Trace11 as the master control for this state display.
             * If not set, just exit.
             *
             * TBD: Add more trace flags for finer control over the display
             */

            if (!Trace(Trace11))
            {
                return;
            }

            /*
             * If Trace14 is set and the dirtyBit is not set, then
             * exit without printing any display.
             */

            if (Trace(Trace14) && !(ctxt.dirtyBit != 0))
            {
                return;
            }

            printf("Current State: {1} ({0})\n", ctxt.state, (int)ctxt.state);

            // endOfText = ctxt.theText + ctxt.textLen;

            if (ctxt.state <= ALGORITHM_STATE.State_P3Done)
                /*
                 * Don't bother repeating this fixed information after every rule is applied.
                 */
            {
                tmp = br_LabelForDirection(ctxt.paragraphDirection);
                printf("Paragraph Dir: {0} ({1}), Paragraph Embed Level: {2}, TextLen: {3}\n\n",
                    (int)ctxt.paragraphDirection, tmp, ctxt.paragraphEmbeddingLevel,
                    ctxt.textLen);
            }

            /*
             * When printing output for the rules that identify runs (and
             * isolating run sequences for UBA63) also print out index positions
             * to help with identification of the spans.
             *
             * These are printed above the Text (if any) and above the Bidi_Class
             * values.
             */
            if ((ctxt.state == ALGORITHM_STATE.State_RunsDone) || (ctxt.state == ALGORITHM_STATE.State_X10Done))
            {
                printf("  Position:   ");
                for (int ix = 0; ix < ctxt.textUnits.Length; ix++)
                {
                    printf(" {0:D4}", ix);
                }

                printf("\n");
            }

            /*
             * Omit printing the text when processing BidiText.txt, because
             * that format has no original text -- only sequences of Bidi_Class
             * values.
             */
            if (GetFileFormat() == FORMAT_A)
            {
                printf("  Text:       ");
                foreach (var bdu in ctxt.textUnits)
                {
                    /*
                     * Adjust the printing of the code points, so that SMP
                     * code points don't cause the rest of the display to
                     * end up column-deregistered.
                     */
                    if (bdu.c > 0xFFFF)
                    {
                        printf("{0:X5}", bdu.c);
                    }
                    else
                    {
                        printf(" {0:X4}", bdu.c);
                    }
                }

                printf("\n");
            }

            printf("  Bidi_Class: ");
            foreach (var bdu in ctxt.textUnits)
            {
                printf(" {0}", BidiClassLabels[(int)bdu.bc]);
            }

            printf("\n");
            printf("  Levels:     ");
            foreach (var bdu in ctxt.textUnits)
            {
                if (bdu.level == NOLEVEL)
                {
                    printf("    x");
                }
                else
                {
                    printf(" {0,4:D}", bdu.level);
                }
            }

            printf("\n");
            /*
              * If the algorithm is complete and we are in the checking phase,
              * add listing of the expected levels. Check for mismatches, and
              * print an extra diagnostic line, if found.
              */
            if (ctxt.state == ALGORITHM_STATE.State_Complete)
            {
                printf("  Exp Levels: ");
                mismatch = false;
                foreach (var bdu in ctxt.textUnits)
                {
                    if (bdu.expLevel == NOLEVEL)
                    {
                        printf("    x");
                    }
                    else
                    {
                        printf(" {0,4:D}", bdu.expLevel);
                    }

                    if (bdu.level != bdu.expLevel)
                    {
                        mismatch = true;
                    }
                }

                printf("\n");
                if (mismatch)
                {
                    printf("  Mismatches: ");
                    foreach (var bdu in ctxt.textUnits)
                    {
                        if (bdu.level != bdu.expLevel)
                        {
                            printf("    ^");
                        }
                        else
                        {
                            printf("     ");
                        }
                    }

                    printf("\n");
                }
            }

            if ((ctxt.state >= ALGORITHM_STATE.State_RunsDone) && (ctxt.theRuns != null))
            {
                br_DisplayRunList(ctxt.theRuns);
            }

            if ((ctxt.state == ALGORITHM_STATE.State_X10Done) && (ctxt.theSequences != null))
            {
                br_DisplaySequenceList(ctxt);
            }

            printf("\n");
            if (ctxt.state >= ALGORITHM_STATE.State_L2Done)
            {
                printf("  Order:      [");
                started = false;
                foreach (var bdu in ctxt.textUnits)
                {
                    /* Skip any "deleted" elements. */
                    if (bdu.order != -1)
                    {
                        if (!started)
                        {
                            printf("{0}", bdu.order);
                            started = true;
                        }
                        else
                        {
                            printf(" {0}", bdu.order);
                        }
                    }
                }

                printf("]\n");
            }

            /*
              * If the algorithm is complete and we are in the checking phase,
              * add listing of the expected order.
              */
            if (ctxt.state == ALGORITHM_STATE.State_Complete)
            {
                if (ctxt.expOrder != null)
                {
                    printf("  Exp Order:  [{0}]\n", ctxt.expOrder);
                }
            }
        }

        /******************************************************/

        /*
         * SECTION: Paragraph Embedding Level Rules: P1 - P3
         */

        /*
         * br_UBA_ParagraphEmbeddingLevel
         *
         * This function runs Rules P2 and P3.
         *
         * The paragraph embedding level defaults to zero.
         *
         * Note that this reference implementation assumes that
         * paragraph breaking has already been done, so P1 is
         * basically out of scope. Input is assumed to already
         * consist of single "paragraph" units, as is the true
         * for all the test case data for BidiTest.txt and
         * for BidiCharacterTest.txt.
         *
         * If the directionality is Dir_RTL, that signals an
         * override of rules P2 and P3. Set the paragraph
         * embedding level to 1.
         *
         * If the directionality is Dir_Auto (default), scan
         * the text, and if the *first* character with strong
         * directionality is bc=R or bc=AL, set the paragraph
         * embedding level to 1.
         */

        static int br_UBA_ParagraphEmbeddingLevel(UBACONTEXT ctxt)
        {
            BIDIPROP bc;

            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA_ParagraphEmbeddingLevel [P2, P3]\n");
            }

            if (ctxt.paragraphDirection == Paragraph_Direction.Dir_RTL)
            {
                ctxt.paragraphEmbeddingLevel = 1;
            }
            else if (ctxt.paragraphDirection == Paragraph_Direction.Dir_Auto)
            {
                foreach (var bdu in ctxt.textUnits)
                {
                    bc = bdu.bc;
                    if (Trace(Trace9))
                    {
                        printf("Debug: bc={0} ({1})\n", bc, BidiClassLabelsTrimmed[(int)bc]);
                    }

                    if (bc == BIDIPROP.BIDI_L)
                    {
                        break;
                    }
                    else if ((bc == BIDIPROP.BIDI_R) || (bc == BIDIPROP.BIDI_AL))
                    {
                        ctxt.paragraphEmbeddingLevel = 1;
                        break;
                    }

                    /* Dropped through without finding a strong type yet. */
                }
            }

            ctxt.dirtyBit = 1;
            ctxt.state = ALGORITHM_STATE.State_P3Done;
            return (1);
        }

        /*
         * br_HasMatchingPDI
         *
         * Scan forwards from the current pointer, looking for a matching
         * PDI for an isolate initiator.
         *
         * This scan needs to check for multiple isolate sequences, and find the
         * PDI which matches this isolate initiator.
         *
         * The strategy is to create a dumb
         * level counter, starting at 1 and increment or decrement it
         * for each PDI or isolate initiator encountered. A match condition
         * consists of first encounter of a PDI (while scanning
         * forwards) when the dumb level counter is set to zero.
         *
         * Return 1 if a match is found, 0 if no match is found.
         */

        static bool br_HasMatchingPDI(BIDIUNIT[] textUnits, int current, int endOfText, ref int pdiPtr)
        {
            int dumblevelctr = 1;

            while (current < endOfText)
            {
                int index = current + 1;
                BIDIUNIT bdu = textUnits[index];
                /* Check the Bidi_Class */
                var bc = bdu.bc;
                if (Trace(Trace9))
                {
                    printf("Debug: br_HasMatchingPDI bc={0}, dumblevelctr={1}\n",
                        BidiClassLabelsTrimmed[(int)bdu.bc], dumblevelctr);
                }

                /* If we hit a PDI, decrement the level counter */
                if (bc == BIDIPROP.BIDI_PDI)
                {
                    dumblevelctr--;
                    /* 
                     * If the level counter has decremented back to zero, we have a match.
                     * Set a pointer to the PDI we have found.
                     */
                    if (dumblevelctr == 0)
                    {
                        pdiPtr = index;
                        return true;
                    }
                }
                /* If we hit another isolate initiator, increment the level counter */
                else if (bdu.bc_isoinit)
                {
                    dumblevelctr++;
                }

                /* Increment the unit pointer */
                current++;
            }

            /* Fell through without a match. Return 0. */
            return false;
        }

        /*
         * br_UBA63_ParagraphEmbeddingLevel
         *
         * This function runs Rules P2 and P3.
         *
         * The paragraph embedding level defaults to zero.
         *
         * If the directionality is Dir_RTL, that signals an
         * override of rules P2 and P3. Set the paragraph
         * embedding level to 1.
         *
         * If the directionality is Dir_Auto (default), scan
         * the text, and if the *first* character with strong
         * directionality is bc=R or bc=AL, set the paragraph
         * embedding level to 1.
         *
         * The difference between this rule for UBA62 and UBA63 is
         * that the UBA63 version needs to ignore any characters
         * between an isolate initiator and a matching PDI.
         */

        static int br_UBA63_ParagraphEmbeddingLevel(UBACONTEXT ctxt)
        {
            int pdiPtr = 0;

            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA63_ParagraphEmbeddingLevel [P2, P3]\n");
            }

            if (ctxt.paragraphDirection == Paragraph_Direction.Dir_RTL)
            {
                ctxt.paragraphEmbeddingLevel = 1;
            }
            else if (ctxt.paragraphDirection == Paragraph_Direction.Dir_Auto)
            {
                int current = 0;
                int endOfText = ctxt.textUnits.Length - 1;
                while (current <= endOfText)
                {
                    var bdu = ctxt.textUnits[current];
                    var bc = bdu.bc;
                    if (Trace(Trace9))
                    {
                        printf("Debug: bc={0} ({1})\n", bc, BidiClassLabelsTrimmed[(int)bc]);
                    }

                    if (bc == BIDIPROP.BIDI_L)
                    {
                        break;
                    }
                    else if ((bc == BIDIPROP.BIDI_R) || (bc == BIDIPROP.BIDI_AL))
                    {
                        ctxt.paragraphEmbeddingLevel = 1;
                        break;
                    }
                    else if (bdu.bc_isoinit)
                    {
                        var hasPDIMatch = br_HasMatchingPDI(ctxt.textUnits, current, endOfText, ref pdiPtr);
                        if (hasPDIMatch)
                        {
                            if (Trace(Trace9))
                            {
                                printf("Bingo!\n");
                            }

                            /*
                             * Set bdu past the PDI which marks the end of
                             * the isolated sequence.
                             */
                            current = pdiPtr + 1;
                        }
                        else
                        {
                            /* 
                             * If there is no matching PDI, leave
                             * the embedding level at 0 and return.
                             */
                            break;
                        }
                    }
                    /* Dropped through without finding a strong type yet. */
                    else
                    {
                        current++;
                    }
                }
            }

            ctxt.dirtyBit = 1;
            ctxt.state = ALGORITHM_STATE.State_P3Done;
            return (1);
        }

        /*************************************************************/

        /*
         * SECTION: Explicit Embedding Level Rules: X1 - X10
         */

        /* Directional Status Stack */
        static void br_InitStack()
        {
            stackTop = 0;
            stackMax = maximum_depth;
        }

        /*
         * Version-specific Stack Handling.
         *
         * The UBA62 stack handling only pushes an embedding level and
         * an override status on the stack.
         *
         * The UBA63 stack handling additionally pushes an isolate status
         * on the stack.
         *
         * To simplify the expression of the stack processing for this
         * implementation, while sacrificing some efficiency, the UBA62
         * versus UBA63 distinction is encapsulated in distinct routines
         * to assign values to a STATUSSTACKELEMENT. In this way, the
         * main stack operations can be expressed generically.
         */

        /*
         * br_AssembleStackElement_62
         *
         * Assign values to a stack element per UBA 6.2.
         */
        static void br_AssembleStackElement_62(STATUSSTACKELEMENT sptr, int level,
            D_Override_Status ors)
        {
            sptr.embedding_level = level;
            sptr.override_status = ors;
            sptr.isolate_status = 0;
        }

        /*
         * br_AssembleStackElement_63
         *
         * Assign values to a stack element per UBA 6.3.
         */
        static void br_AssembleStackElement_63(STATUSSTACKELEMENT sptr, int level,
            D_Override_Status ors, int isos)
        {
            sptr.embedding_level = level;
            sptr.override_status = ors;
            sptr.isolate_status = isos;
        }

        /* 
         * br_PushStack
         *
         * Push an element on the stack. 
         */
        static void br_PushStack(STATUSSTACKELEMENT sptr)
        {
            /* Check for stack full */
            if (stackTop < stackMax)
            {
                if (Trace(Trace3))
                {
                    if (GetUBAVersion() > (int)UBA_Version_Type.UBA62)
                    {
                        printf("Trace: br_PushStack, level={0}, override status={1}, isolate status={2}\n",
                            sptr.embedding_level, sptr.override_status, sptr.isolate_status);
                    }
                    else
                    {
                        printf("Trace: br_PushStack, level={0}, override status={1}\n",
                            sptr.embedding_level, sptr.override_status);
                    }
                }

                stackTop++;
                var stackTopObj = statusStack[stackTop];
                stackTopObj.embedding_level = sptr.embedding_level;
                stackTopObj.override_status = sptr.override_status;
                stackTopObj.isolate_status = sptr.isolate_status;
            }
            else
            {
                if (Trace(Trace3))
                {
                    printf("Trace: br_PushStack, stack full\n");
                }
            }
        }

        /* 
         * br_PopStack
         *
         * Pop an element off the stack.
         */
        static void br_PopStack(STATUSSTACKELEMENT sptr)
        {
            /* Check for stack empty */
            if (stackTop > 0)
            {
                var stackTopObj = statusStack[stackTop];
                if (Trace(Trace3))
                {
                    if (GetUBAVersion() > (int)UBA_Version_Type.UBA62)
                    {
                        printf("Trace: br_PopStack,  level={0}, override status={1}, isolate status={2}\n",
                            stackTopObj.embedding_level, stackTopObj.override_status, stackTopObj.isolate_status);
                    }
                    else
                    {
                        printf("Trace: br_PopStack,  level={0}, override status={1}\n",
                            stackTopObj.embedding_level, stackTopObj.override_status);
                    }
                }

                sptr.embedding_level = stackTopObj.embedding_level;
                sptr.override_status = stackTopObj.override_status;
                sptr.isolate_status = stackTopObj.isolate_status;
                stackTop--;
            }
            else
            {
                if (Trace(Trace3))
                {
                    printf("Trace: br_PopStack,  stack empty\n");
                }
            }
        }

        /* 
         * br_PeekStack
         *
         * Examine an element on the stack, but don't pop it.
         */
        static void br_PeekStack(STATUSSTACKELEMENT sptr)
        {
            /* Check for stack empty */
            if (stackTop > 0)
            {
                var stackTopObj = statusStack[stackTop];
                if (Trace(Trace3))
                {
                    printf("Trace: br_PeekStack, level={0}, override status={1}, isolate status={2}\n",
                        stackTopObj.embedding_level, stackTopObj.override_status, stackTopObj.isolate_status);
                }

                sptr.embedding_level = stackTopObj.embedding_level;
                sptr.override_status = stackTopObj.override_status;
                sptr.isolate_status = stackTopObj.isolate_status;
            }
            else
            {
                if (Trace(Trace3))
                {
                    printf("Trace: br_PeekStack, stack empty\n");
                }
            }
        }

        /*
         * br_StackEntryCount
         *
         * Return a count of how many elements are on the stack.
         */

        static int br_StackEntryCount()
        {
            return stackTop;
        }

        /*
         * Encapsulate the calculation of least greater odd or
         * even embedding levels.
         *
         * These functions return -1 if the resulting odd or even
         * embedding level would not be valid (exceeds the maximum
         * allowable level).
         */

        static int br_leastGreaterOddLevel(int level)
        {
            int templevel;

            if (level % 2 == 1)
            {
                templevel = level + 2;
            }
            else
            {
                templevel = level + 1;
            }

            return ((templevel > maximum_depth) ? -1 : templevel);
        }

        static int br_leastGreaterEvenLevel(int level)
        {
            int templevel;

            if (level % 2 == 0)
            {
                templevel = level + 2;
            }
            else
            {
                templevel = level + 1;
            }

            return ((templevel > maximum_depth - 1) ? -1 : templevel);
        }

        /*
         * br_HasMatch
         *
         * Scan backwards from the current pointer, looking for a matching
         * LRE, RLE, LRO, or RLO for a PDF.
         *
         * This scan needs to check for multiple embeddings, and find any
         * opening code which matches the PDF.
         *
         * This cannot use the level values in the BIDIUNIT vector, because
         * it is invoked from the code which is trying to determine the
         * embedding levels. Instead, the strategy is to create a dumb
         * level counter, starting at 1 and increment or decrement it
         * for each PDF or start control encountered. A match condition
         * consists of first encounter of a opening code (while scanning
         * backwards) when the dumb level counter is set to zero.
         *
         * Return 1 if a match is found, 0 if no match is found.
         */

        static bool br_HasMatch(BIDIUNIT[] textUnits, int current, int start)
        {
            int dumblevelctr = 1;

            while (current > start)
            {
                BIDIUNIT bdu = textUnits[current];
                /* Decrement the unit pointer */
                current--;
                /* Check the Bidi_Class */
                var bc = bdu.bc;
                if (Trace(Trace4))
                {
                    printf("Debug: br_HasMatch bc={0}, dumblevelctr={1}\n",
                        BidiClassLabelsTrimmed[(int)bdu.bc], dumblevelctr);
                }

                /* If we hit another PDF, increment the level counter */
                if (bc == BIDIPROP.BIDI_PDF)
                {
                    dumblevelctr++;
                }
                /* If we hit an opening code, decrement the level counter */
                else if ((bc == BIDIPROP.BIDI_LRE) || (bc == BIDIPROP.BIDI_RLE) ||
                         (bc == BIDIPROP.BIDI_LRO) || (bc == BIDIPROP.BIDI_RLO))
                {
                    dumblevelctr--;
                    /* If the level counter has decremented back to zero, we have a match. */
                    if (dumblevelctr == 0)
                    {
                        return true;
                    }
                }
            }

            /* Fell through without a match. Return 0. */
            return false;
        }

        /*
         * br_UBA_ExplicitEmbeddingLevels
         *
         * This function runs Rules X1 through X8.
         *
         * The paragraph embedding level defaults to zero.
         *
         */

        static int br_UBA_ExplicitEmbeddingLevels(UBACONTEXT ctxt)
        {
            int templevel;
            STATUSSTACKELEMENT stack_element = new STATUSSTACKELEMENT();

            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA_ExplicitEmbeddingLevels [X1-X8]\n");
            }

            ctxt.dirtyBit = 0;

            /*
             * X1:
             * Set the current embedding level to the paragraph embedding level.
             * Set the current directional override status to neutral.
             *
             * Because rules X2 through X8, which use the stack and refer
             * to the current embedding level and current override status,
             * are handled here in a single pass, these values can be
             * stored in local variables, and do not need to be saved
             * in the context.
             */
            var currentEmbeddingLevel = ctxt.paragraphEmbeddingLevel;
            var currentOverrideStatus = D_Override_Status.Override_Neutral;
            /*
             * Initialize the stack each time into this function, before
             * running the X2..X8 rules.
             */
            br_InitStack();

            /*
             * X2..X8:
             *
             * Process each character in the input, setting embedding levels
             * and override status.
             */
            for (int bdu = 0; bdu < ctxt.textUnits.Length; bdu++)
            {
                BIDIPROP bc = ctxt.textUnits[bdu].bc;
                if (Trace(Trace4))
                {
                    printf("Debug: bc={0} ({1})\n", bc, BidiClassLabelsTrimmed[(int)bc]);
                }

                switch (bc)
                {
                    case BIDIPROP.BIDI_RLE: /* X2 */
                        templevel = br_leastGreaterOddLevel(currentEmbeddingLevel);
                        if (templevel != -1)
                        {
                            br_AssembleStackElement_62(stack_element,
                                currentEmbeddingLevel, currentOverrideStatus);
                            br_PushStack(stack_element);
                            currentEmbeddingLevel = templevel;
                            currentOverrideStatus = D_Override_Status.Override_Neutral;
                        }

                        break;
                    case BIDIPROP.BIDI_LRE: /* X3 */
                        templevel = br_leastGreaterEvenLevel(currentEmbeddingLevel);
                        if (templevel != -1)
                        {
                            br_AssembleStackElement_62(stack_element,
                                currentEmbeddingLevel, currentOverrideStatus);
                            br_PushStack(stack_element);
                            currentEmbeddingLevel = templevel;
                            currentOverrideStatus = D_Override_Status.Override_Neutral;
                        }

                        break;
                    case BIDIPROP.BIDI_RLO: /* X4 */
                        templevel = br_leastGreaterOddLevel(currentEmbeddingLevel);
                        if (templevel != -1)
                        {
                            br_AssembleStackElement_62(stack_element,
                                currentEmbeddingLevel, currentOverrideStatus);
                            br_PushStack(stack_element);
                            currentEmbeddingLevel = templevel;
                            currentOverrideStatus = D_Override_Status.Override_RTL;
                        }

                        break;
                    case BIDIPROP.BIDI_LRO: /* X5 */
                        templevel = br_leastGreaterEvenLevel(currentEmbeddingLevel);
                        if (templevel != -1)
                        {
                            br_AssembleStackElement_62(stack_element,
                                currentEmbeddingLevel, currentOverrideStatus);
                            br_PushStack(stack_element);
                            currentEmbeddingLevel = templevel;
                            currentOverrideStatus = D_Override_Status.Override_LTR;
                        }

                        break;
                    case BIDIPROP.BIDI_PDF: /* X7 */
                        if (br_HasMatch(ctxt.textUnits, bdu, 0))
                        {
                            br_PopStack(stack_element);
                            currentEmbeddingLevel = stack_element.embedding_level;
                            currentOverrideStatus = stack_element.override_status;
                        }

                        break;
                    case BIDIPROP.BIDI_BN:
                        break;
                    case BIDIPROP.BIDI_B: /* X8 */
                        /*
                         * A paragraph break terminates all embedding contexts.
                         * Just set the level back to the paragraph embedding level.
                         * A BIDI_B should only be encountered as the very last element
                         * in a paragraph. If not, the paragraph chunking was not
                         * done correctly.
                         */
                        ctxt.textUnits[bdu].level = ctxt.paragraphEmbeddingLevel;
                        ctxt.dirtyBit = 1;
                        break;
                    default: /* X6 */
                        ctxt.textUnits[bdu].level = currentEmbeddingLevel;
                        if (currentOverrideStatus == D_Override_Status.Override_RTL)
                        {
                            ctxt.textUnits[bdu].bc = BIDIPROP.BIDI_R;
                            ctxt.textUnits[bdu].bc_numeric = false;
                        }
                        else if (currentOverrideStatus == D_Override_Status.Override_LTR)
                        {
                            ctxt.textUnits[bdu].bc = BIDIPROP.BIDI_L;
                            ctxt.textUnits[bdu].bc_numeric = false;
                        }

                        ctxt.dirtyBit = 1;
                        break;
                }

                if (Trace(Trace4))
                {
                    if (bc != ctxt.textUnits[bdu].bc)
                    {
                        printf("Debug: override Bidi_Class bc={0} {1}\n",
                            ctxt.textUnits[bdu].bc, BidiClassLabelsTrimmed[(int)ctxt.textUnits[bdu].bc]);
                    }
                }

                /* Advance to the next character to process. */
            }

            ctxt.state = ALGORITHM_STATE.State_X8Done;
            return (1);
        }

        /*
         * br_DecideParaLevel
         *
         * Run rules P2 and P3 on the range start to endOfText, and return
         * a paragraph level value.
         */

        static int br_DecideParaLevel(BIDIUNIT[] textUnits, int start, int endOfText)
        {
            BIDIPROP bc;
            int pdiPtr = 0;
            bool hasPDIMatch;

            var bdu = start;
            while (bdu < endOfText)
            {
                bc = textUnits[start].bc;
                if (Trace(Trace4))
                {
                    printf("Debug: bc={0} {1}\n", bc, BidiClassLabelsTrimmed[(int)bc]);
                }

                if (bc == BIDIPROP.BIDI_L)
                {
                    return (0);
                }
                else if ((bc == BIDIPROP.BIDI_R) || (bc == BIDIPROP.BIDI_AL))
                {
                    return (1);
                }
                else if (textUnits[start].bc_isoinit)
                {
                    hasPDIMatch = br_HasMatchingPDI(textUnits, bdu, endOfText, ref pdiPtr);
                    if (hasPDIMatch)
                    {
                        if (Trace(Trace4))
                        {
                            printf("Bingo!\n");
                        }

                        /*
                         * Set bdu past the PDI which marks the end of
                         * the isolated sequence.
                         */
                        bdu = pdiPtr + 1;
                        continue;
                    }
                    else
                    {
                        /* 
                         * If there is no matching PDI, return 0.
                         */
                        return (0);
                    }
                }

                /* Dropped through without finding a strong type yet. */
                bdu++;
            }

            return (0);
        }

        /*
         * br_UBA63_ExplicitEmbeddingLevels
         *
         * This function runs Rules X1 through X8.
         *
         * The paragraph embedding level defaults to zero.
         *
         * The UBA63 version of these rules is considerably
         * more complex than for UBA62. It takes into account
         * explicit embedding and override levels and *also*
         * handles explicit isolate sequences.
         *
         * Some state information is stored on the top element
         * of the stack, so a PeekStack function which examines
         * those values without popping the stack is required.
         */

        static int br_UBA63_ExplicitEmbeddingLevels(UBACONTEXT ctxt)
        {
            BIDIPROP bc;
            int templevel;
            STATUSSTACKELEMENT stack_element = new STATUSSTACKELEMENT();

            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA63_ExplicitEmbeddingLevels [X1-X8]\n");
            }

            ctxt.dirtyBit = 0;
            /*
             * X1:
             * Initialize the stack and the other variables.
             *
             * Because rules X2 through X8, which use the stack,
             * are handled here in a single pass, the required values can be
             * stored in local variables, and do not need to be saved
             * in the context.
             */

            /*
             * Initialize the stack each time into this function, before
             * running the X2..X8 rules.
             */
            br_InitStack();

            br_AssembleStackElement_63(stack_element,
                ctxt.paragraphEmbeddingLevel, D_Override_Status.Override_Neutral, 0);

            br_PushStack(stack_element);

            var overflowIsolateCount = 0;
            var overflowEmbeddingCount = 0;
            var validIsolateCount = 0;

            /*
             * X2..X8:
             *
             * Process each character in the input, setting embedding levels
             * and override status.
             */
            for (int ix = 0; ix < ctxt.textLen; ix++)
            {
                var bdu = ctxt.textUnits[ix];
                bc = bdu.bc;
                if (Trace(Trace4))
                {
                    printf("Debug: bc={0} {1}\n", bc, BidiClassLabelsTrimmed[(int)bc]);
                }

                switch (bc)
                {
                    case BIDIPROP.BIDI_RLE: /* X2 */
                        br_PeekStack(stack_element);
                        templevel = br_leastGreaterOddLevel(stack_element.embedding_level);
                        if ((templevel != -1) && (overflowIsolateCount == 0) && (overflowEmbeddingCount == 0))
                        {
                            br_AssembleStackElement_63(stack_element,
                                templevel, D_Override_Status.Override_Neutral, 0);
                            br_PushStack(stack_element);
                        }
                        else
                        {
                            if (overflowIsolateCount == 0)
                            {
                                overflowEmbeddingCount++;
                            }
                        }

                        break;
                    case BIDIPROP.BIDI_LRE: /* X3 */
                        br_PeekStack(stack_element);
                        templevel = br_leastGreaterEvenLevel(stack_element.embedding_level);
                        if ((templevel != -1) && (overflowIsolateCount == 0) && (overflowEmbeddingCount == 0))
                        {
                            br_AssembleStackElement_63(stack_element,
                                templevel, D_Override_Status.Override_Neutral, 0);
                            br_PushStack(stack_element);
                        }
                        else
                        {
                            if (overflowIsolateCount == 0)
                            {
                                overflowEmbeddingCount++;
                            }
                        }

                        break;
                    case BIDIPROP.BIDI_RLO: /* X4 */
                        br_PeekStack(stack_element);
                        templevel = br_leastGreaterOddLevel(stack_element.embedding_level);
                        if ((templevel != -1) && (overflowIsolateCount == 0) && (overflowEmbeddingCount == 0))
                        {
                            br_AssembleStackElement_63(stack_element,
                                templevel, D_Override_Status.Override_RTL, 0);
                            br_PushStack(stack_element);
                        }
                        else
                        {
                            if (overflowIsolateCount == 0)
                            {
                                overflowEmbeddingCount++;
                            }
                        }

                        break;
                    case BIDIPROP.BIDI_LRO: /* X5 */
                        br_PeekStack(stack_element);
                        templevel = br_leastGreaterEvenLevel(stack_element.embedding_level);
                        if ((templevel != -1) && (overflowIsolateCount == 0) && (overflowEmbeddingCount == 0))
                        {
                            br_AssembleStackElement_63(stack_element,
                                templevel, D_Override_Status.Override_LTR, 0);
                            br_PushStack(stack_element);
                        }
                        else
                        {
                            if (overflowIsolateCount == 0)
                            {
                                overflowEmbeddingCount++;
                            }
                        }

                        break;
                    case BIDIPROP.BIDI_RLI: /* X5a */
                        br_PeekStack(stack_element);
                        bdu.level = stack_element.embedding_level;
                        /*
                         * For UBA80 and later, if the directional override status of the last
                         * entry is not neutral, change the current bc of the RLI explicitly to
                         * either L or R, accordingly.
                         */
                        if (GetUBAVersion() >= (int)UBA_Version_Type.UBA80)
                        {
                            if (stack_element.override_status != D_Override_Status.Override_Neutral)
                            {
                                bdu.bc = (stack_element.override_status == D_Override_Status.Override_LTR) ? BIDIPROP.BIDI_L : BIDIPROP.BIDI_R;
                            }
                        }

                        ctxt.dirtyBit = 1;
                        templevel = br_leastGreaterOddLevel(stack_element.embedding_level);
                        if ((templevel != -1) && (overflowIsolateCount == 0) && (overflowEmbeddingCount == 0))
                        {
                            validIsolateCount++;
                            br_AssembleStackElement_63(stack_element,
                                templevel, D_Override_Status.Override_Neutral, 1);
                            br_PushStack(stack_element);
                        }
                        else
                        {
                            overflowIsolateCount++;
                        }

                        break;
                    case BIDIPROP.BIDI_LRI: /* X5b */
                        br_PeekStack(stack_element);
                        bdu.level = stack_element.embedding_level;
                        /*
                         * For UBA80 and later, if the directional override status of the last
                         * entry is not neutral, change the current bc of the LRI explicitly to
                         * either L or R, accordingly.
                         */
                        if (GetUBAVersion() >= (int)UBA_Version_Type.UBA80)
                        {
                            if (stack_element.override_status != D_Override_Status.Override_Neutral)
                            {
                                bdu.bc = (stack_element.override_status == D_Override_Status.Override_LTR) ? BIDIPROP.BIDI_L : BIDIPROP.BIDI_R;
                            }
                        }

                        ctxt.dirtyBit = 1;
                        templevel = br_leastGreaterEvenLevel(stack_element.embedding_level);
                        if ((templevel != -1) && (overflowIsolateCount == 0) && (overflowEmbeddingCount == 0))
                        {
                            validIsolateCount++;
                            br_AssembleStackElement_63(stack_element,
                                templevel, D_Override_Status.Override_Neutral, 1);
                            br_PushStack(stack_element);
                        }
                        else
                        {
                            overflowIsolateCount++;
                        }

                        break;
                    case BIDIPROP.BIDI_FSI: /* X5c This is a complicated mix of X5a/X5b */
                    {
                        int pdiPtr = 0;
                        int tmpParaEmbedLevel;

                        /* Check if there is a matching PDI */
                        var hasPDIMatch = br_HasMatchingPDI(ctxt.textUnits, ix, ctxt.textLen, ref pdiPtr);
                        if (hasPDIMatch)
                        {
                            tmpParaEmbedLevel = br_DecideParaLevel(ctxt.textUnits, ix + 1, pdiPtr);
                        }
                        else
                        {
                            tmpParaEmbedLevel = br_DecideParaLevel(ctxt.textUnits, ix + 1, ctxt.textLen);
                        }

                        br_PeekStack(stack_element);
                        bdu.level = stack_element.embedding_level;
                        /*
                         * For UBA80 and later, if the directional override status of the last
                         * entry is not neutral, change the current bc of the FSI explicitly to
                         * either L or R, accordingly.
                         */
                        if (GetUBAVersion() >= (int)UBA_Version_Type.UBA80)
                        {
                            if (stack_element.override_status != D_Override_Status.Override_Neutral)
                            {
                                bdu.bc = (stack_element.override_status == D_Override_Status.Override_LTR) ? BIDIPROP.BIDI_L : BIDIPROP.BIDI_R;
                            }
                        }

                        ctxt.dirtyBit = 1;
                        /*
                         * If the calculated paragraph embedding level is 1, treat
                         * this FSI as an RLI. Otherwise, treat it as an LRI.
                         */
                        if (tmpParaEmbedLevel == 1)
                        {
                            templevel = br_leastGreaterOddLevel(stack_element.embedding_level);
                        }
                        else
                        {
                            templevel = br_leastGreaterEvenLevel(stack_element.embedding_level);
                        }

                        if ((templevel != -1) && (overflowIsolateCount == 0) && (overflowEmbeddingCount == 0))
                        {
                            validIsolateCount++;
                            br_AssembleStackElement_63(stack_element,
                                templevel, D_Override_Status.Override_Neutral, 1);
                            br_PushStack(stack_element);
                        }
                        else
                        {
                            overflowIsolateCount++;
                        }
                    }
                        break;
                    case BIDIPROP.BIDI_PDI: /* X6a */
                        if (overflowIsolateCount > 0)
                        {
                            overflowIsolateCount--;
                        }
                        else if (validIsolateCount == 0)
                        {
                            /* do nothing */
                        }
                        else
                        {
                            overflowEmbeddingCount = 0;
                            var continuepopping = true;
                            while (continuepopping)
                            {
                                br_PeekStack(stack_element);
                                if (stack_element.isolate_status == 0)
                                {
                                    br_PopStack(stack_element);
                                }
                                else
                                {
                                    continuepopping = false;
                                }
                            }

                            br_PopStack(stack_element);
                            validIsolateCount--;
                        }

                        br_PeekStack(stack_element);
                        bdu.level = stack_element.embedding_level;
                        /*
                         * For UBA80 and later, if the directional override status of the last
                         * entry is not neutral, change the current bc of the PDI explicitly to
                         * either L or R, accordingly.
                         */
                        if (GetUBAVersion() >= (int)UBA_Version_Type.UBA80)
                        {
                            if (stack_element.override_status != D_Override_Status.Override_Neutral)
                            {
                                bdu.bc = (stack_element.override_status == D_Override_Status.Override_LTR) ? BIDIPROP.BIDI_L : BIDIPROP.BIDI_R;
                            }
                        }

                        ctxt.dirtyBit = 1;
                        break;
                    case BIDIPROP.BIDI_PDF: /* X7 */
                        if (overflowIsolateCount > 0)
                        {
                            /* do nothing */
                        }
                        else if (overflowEmbeddingCount > 0)
                        {
                            overflowEmbeddingCount--;
                        }
                        else
                        {
                            br_PeekStack(stack_element);
                            if (stack_element.isolate_status == 0)
                            {
                                if (br_StackEntryCount() >= 2)
                                {
                                    br_PopStack(stack_element);
                                }
                            }
                        }

                        break;
                    case BIDIPROP.BIDI_BN:
                        break;
                    case BIDIPROP.BIDI_B: /* X8 */
                        /*
                         * A paragraph break terminates all embedding contexts.
                         * Just set the level back to the paragraph embedding level.
                         * A BIDI_B should only be encountered as the very last element
                         * in a paragraph. If not, the paragraph chunking was not
                         * done correctly.
                         */
                        bdu.level = ctxt.paragraphEmbeddingLevel;
                        ctxt.dirtyBit = 1;
                        break;
                    default: /* X6 */
                        br_PeekStack(stack_element);
                        bdu.level = stack_element.embedding_level;
                        if (stack_element.override_status == D_Override_Status.Override_RTL)
                        {
                            bdu.bc = BIDIPROP.BIDI_R;
                            bdu.bc_numeric = false;
                        }
                        else if (stack_element.override_status == D_Override_Status.Override_LTR)
                        {
                            bdu.bc = BIDIPROP.BIDI_L;
                            bdu.bc_numeric = false;
                        }

                        ctxt.dirtyBit = 1;
                        break;
                }

                if (Trace(Trace4))
                {
                    if (bc != bdu.bc)
                    {
                        printf("Debug: override Bidi_Class bc={0} ({1})\n",
                            bdu.bc, BidiClassLabelsTrimmed[(int)bdu.bc]);
                    }
                }

                /* Advance to the next character to process. */
            }

            ctxt.state = ALGORITHM_STATE.State_X8Done;
            return (1);
        }

        /*
         * br_UBA_DeleteFormatCharacters
         *
         * This function runs Rule X9.
         *
         * Characters are not actually deleted. Instead, their level
         * is set to NOLEVEL, which allows ignoring them in later steps.
         *
         * This setting could, of course, be done more efficiently by
         * also setting these levels as part of the processing of X2..X8,
         * but is pulled out separately here to make the impact of
         * X9 clearer for the didactic implementation.
         */

        static int br_UBA_DeleteFormatCharacters(UBACONTEXT ctxt)
        {
            BIDIPROP bc;

            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA_DeleteFormatCharacters [X9]\n");
            }

            ctxt.dirtyBit = 0;

            foreach (var bdu in ctxt.textUnits)
            {
                bc = bdu.bc;
                switch (bc)
                {
                    case BIDIPROP.BIDI_RLE:
                    case BIDIPROP.BIDI_LRE:
                    case BIDIPROP.BIDI_RLO:
                    case BIDIPROP.BIDI_LRO:
                    case BIDIPROP.BIDI_PDF:
                    case BIDIPROP.BIDI_BN:
                        bdu.level = NOLEVEL;
                        ctxt.dirtyBit = 1;
                        break;
                }

                /* 
                 * Now reset the order positions.
                 * Use -1 as the position for all "deleted" characters.
                 * This is later checked when actually reordering levels.
                 */
                if (bdu.level == NOLEVEL)
                {
                    bdu.order = -1;
                    bdu.order2 = -1;
                }

                /* Advance to the next character to process. */
            }

            ctxt.state = ALGORITHM_STATE.State_X9Done;
            return (1);
        }

        /*
         * br_ConstructBidiRun
         *
         * Allocate and initialize a BIDIRUN. These structs are used to construct
         * a linked list of runs.
         */

        static BIDIRUN br_ConstructBidiRun(BIDIUNIT[] textUnits, int id, int level, int first,
            int last, int len)
        {
            BIDIRUN brp = new BIDIRUN();

            brp.runID = id;
            brp.seqID = 0;
            brp.level = level;
            brp.len = len;
            brp.elements = new BIDIUNIT[len];
            for (int i = 0; i < len; i++)
            {
                brp.elements[i] = textUnits[first + i];
            }

            brp.sor = BIDIPROP.BIDI_Unknown;
            brp.eor = BIDIPROP.BIDI_Unknown;
            brp.textChain = null;
            brp.next = null;

            return (brp);
        }

        /*
         * br_ConstructBidiRunListElement
         *
         * Allocate and initialize a BIDIRUNLISTELEMENT.
         */

        static BIDIRUNLISTELEMENT br_ConstructBidiRunListElement()
        {
            return new BIDIRUNLISTELEMENT();
        }

        /*
         * br_ConstructIsolatingRunSequence
         *
         * Allocate and initialize an ISOLATING_RUN_SEQUENCE. These structs are used to construct
         * a linked list of isolating run sequences.
         *
         * Start the list of runs associated with the sequence and assign brp to
         * the head of that list.
         */

        static ISOLATING_RUN_SEQUENCE br_ConstructIsolatingRunSequence(int id, BIDIRUN brp)
        {
            ISOLATING_RUN_SEQUENCE irp = new ISOLATING_RUN_SEQUENCE();
            BIDIRUNLISTELEMENT brlp = br_ConstructBidiRunListElement();

            brlp.run = brp;

            irp.seqID = id;
            irp.level = brp.level;
            irp.theRuns = brlp;
            irp.lastRun = brlp;
            irp.sos = BIDIPROP.BIDI_Unknown;
            irp.eos = BIDIPROP.BIDI_Unknown;
            irp.textChain = null;
            irp.next = null;

            return (irp);
        }

        /*
         * br_AppendBidiRun
         *
         * Append an allocated and initialized BIDIRUN to the linked list of
         * runs in the context.
         *
         * Maintain the lastRun pointer in the context to make this appending
         * easy for the linked list.
         */

        static void br_AppendBidiRun(UBACONTEXT ctxt, BIDIRUN brp)
        {
            if (ctxt.theRuns == null)
            {
                ctxt.theRuns = brp;
            }
            else
            {
                ctxt.lastRun.next = brp;
            }

            ctxt.lastRun = brp;
        }

        /*
         * br_AppendIsolatingRunSequence
         *
         * Append an allocated and initialized ISOLATING_RUN_SEQUENCE to the linked list of
         * sequences in the context.
         *
         * Maintain the lastSequence pointer in the context to make this appending
         * easy for the linked list.
         */

        static void br_AppendIsolatingRunSequence(UBACONTEXT ctxt, ISOLATING_RUN_SEQUENCE irp)
        {
            if (ctxt.theSequences == null)
            {
                ctxt.theSequences = irp;
            }
            else
            {
                ctxt.lastSequence.next = irp;
            }

            ctxt.lastSequence = irp;
        }

        /*
         * br_AppendBidiRunToSequence
         *
         * Append an allocated and initialized BIDIRUNLISTPTR (which contains
         * a pointer to a BIDIRUN) to the linked list of
         * runs in the isolating run sequence.
         */

        static void br_AppendBidiRunToSequence(ISOLATING_RUN_SEQUENCE irp, BIDIRUNLISTELEMENT brlp)
        {
            if (irp.theRuns == null)
            {
                irp.theRuns = brlp;
            }
            else
            {
                irp.lastRun.next = brlp;
            }

            irp.lastRun = brlp;
        }

        /*
         * br_SpanOneRun
         *
         * Take two pointers to a source BIDIUNIT vector.
         * Extract the first run containing characters all with
         * the same level value (or NOLEVEL).
         *
         * This spanning has to be tweaked a bit to work for
         * UBA63, because an isolate initiator needs to terminate
         * a level run:
         *
         *     R  RLI  PDF    R
         * <R-------[ <R-----R>  <== correct
         *
         * <R-----------R> <RR>  <== incorrect
         *
         * This tweak is a no-op for UBA62, which does not deal
         * with isolate format controls.
         *
         * Returns True if the spanning is done, False otherwise.
         */

        static bool br_SpanOneRun(BIDIUNIT[] textUnits, int first, int last,
            ref int next, ref int newlevel, ref int spanlen)
        {
            int level;
            bool isolateInitiatorFound;

            var bdu = first;
            var spanlevel = NOLEVEL;
            while (bdu <= last)
            {
                level = textUnits[bdu].level;
                isolateInitiatorFound = false;
                /* skip past "deleted" format characters marked with no level */
                if (level != NOLEVEL)
                {
                    if ((GetUBAVersion() > (int)UBA_Version_Type.UBA62) && textUnits[bdu].bc_isoinit)
                    {
                        isolateInitiatorFound = true;
                    }

                    /* the first time a valid level is hit, set spanlevel */
                    if (spanlevel == NOLEVEL)
                    {
                        spanlevel = level;
                    }
                    /* when level changes, break from the while loop */
                    else if (level != spanlevel)
                    {
                        break;
                    }
                }

                bdu++;
                if (isolateInitiatorFound)
                {
                    /* 
                     * Found an isolate initiator in UBA63 processing.
                     * Terminate the level run here, including the isolate
                     * initiator.
                     */
                    break;
                }
            }

            /* 
             * Set the newlevel. Note that this could be NOLEVEL if the
             * entire vector consists of BN or bidi embedding controls.
             */
            newlevel = spanlevel;
            spanlen = (int)(bdu - first);
            /* Now check whether we are at the end of the vector */
            if (bdu > last)
            {
                /* ran off the end of the vector while in a span */
                next = -1;
                return true; /* spanning done */
            }
            else
            {
                /* Change of level terminated span. Set next pointer. */
                next = bdu;
                return false; /* spanning not done */
            }
        }

        /*
         * br_UBA_CalculateSorEor
         *
         * Process the run list, calculating sor and eor values for
         * each run. Those values default to BIDI_Unknown when the
         * runs are first identified. But each needs to be set to
         * either L or R.
         */

        static void br_UBA_CalculateSorEor(UBACONTEXT ctxt)
        {
            int nextRunLevel;
            int higherLevel;

            var brp = ctxt.theRuns;
            if (brp == null)
            {
                /* No runs to process */
                return;
            }

            /*
             * Default the priorRunLevel for the first run to
             * the paragraph embedding level.
             */
            var priorRunLevel = ctxt.paragraphEmbeddingLevel;
            while (brp != null)
            {
                /*
                 * If we have reached the last run, set the nextRunLevel
                 * to the paragraphEmbedding Level, otherwise set it
                 * to the level of the next run.
                 */
                if (brp.next == null)
                {
                    nextRunLevel = ctxt.paragraphEmbeddingLevel;
                }
                else
                {
                    nextRunLevel = brp.next.level;
                }

                /*
                 * Set sor based on the higher of the priorRunLevel and
                 * the current level.
                 */
                higherLevel = (priorRunLevel > brp.level) ? priorRunLevel : brp.level;
                brp.sor = (higherLevel % 2 == 1) ? BIDIPROP.BIDI_R : BIDIPROP.BIDI_L;

                /*
                 * Set eor based on the higher of the nextRunLevel and
                 * the current level.
                 */
                higherLevel = (nextRunLevel > brp.level) ? nextRunLevel : brp.level;
                brp.eor = (higherLevel % 2 == 1) ? BIDIPROP.BIDI_R : BIDIPROP.BIDI_L;

                /*
                 * Set priorRunLevel to the current level and
                 * move to the next run in the list.
                 */
                priorRunLevel = brp.level;
                brp = brp.next;
            }
        }

        /*
         * br_UBA_CalculateSosEos
         *
         * Process the isolating run sequence list, calculating sos and eos values for
         * each sequence. Those values default to BIDI_Unknown when the
         * sequences are first identified. But each needs to be set to
         * either L or R.
         *
         * Strategy: Instead of recalculating all the sos and eos values from
         * scratch, as specified in X10, we can take a shortcut here, because
         * we already have sor and eor values assigned to all the level runs.
         * For any isolating run sequence, simply assign sos to the value of
         * sor for the *first* run in that sequence, and assign eos to the
         * value of eor for the *last* run in that sequence. This provides
         * equivalent values, and is more straightforward to implement and
         * understand.
         *
         * This strategy has to be modified for defective isolating run sequences,
         * where the sequence ends with an LRI/RLI/FSI.
         * In those cases the eot needs to be calculated based on
         * the paragraph embedding level, rather than from the level run.
         * Note that this only applies when an isolating run sequence
         * terminating in an LRI/RLI/FSI but with no matching PDI.
         * An example would be:
         *
         *    R  RLI    R
         * <L-----R> <RR>
         * <L------[          <== eot would be L, not R
         *           <RR>
         *
         */

        static void br_UBA_CalculateSosEos(UBACONTEXT ctxt)
        {
            int nextRunLevel;
            int higherLevel;

            var tirp = ctxt.theSequences;
            while (tirp != null)
            {
                /*
                 * First inherit the sos and eos values from the
                 * first and last runs in the sequence.
                 */
                tirp.sos = tirp.theRuns.run.sor;
                tirp.eos = tirp.lastRun.run.eor;
                /*
                 * Next adjust for the special case when an isolating
                 * run sequence terminates in an unmatched isolate
                 * initiator.
                 */
                if (tirp.lastRun.run.elements[tirp.lastRun.run.len - 1].bc_isoinit)
                {
                    nextRunLevel = ctxt.paragraphEmbeddingLevel;
                    higherLevel = (nextRunLevel > tirp.level) ? nextRunLevel : tirp.level;
                    tirp.eos = (higherLevel % 2 == 1) ? BIDIPROP.BIDI_R : BIDIPROP.BIDI_L;
                }

                tirp = tirp.next;
            }
        }

        /*
         * br_UBA_AddTextChainsForRuns
         *
         * Walk through a run list, allocating, initializing and
         * linking in a text chain for each run.
         */
        static int br_UBA_AddTextChainsForRuns(BIDIRUN brp)
        {
            int len;
            int bup;
            BIDIUNIT[] tcp;
            int tcp2;

            if (brp == null)
            {
                /* No runs to process */
                return (1);
            }

            var tbrp = brp;
            while (tbrp != null)
            {
                /* 
                 * Each run has a len defined already. Use that value
                 * to allocate a text chain.
                 */
                len = tbrp.len;
                tcp = new BIDIUNIT[len];

                /* Copy BIDIUNIT pointers for the level run into the text chain. */
                for (bup = 0, tcp2 = 0; bup < len; bup++, tcp2++)
                {
                    tcp[tcp2] = tbrp.elements[bup];
                }

                /* Attach the initialized text chain to the bidi run. */
                tbrp.textChain = tcp;

                tbrp = tbrp.next;
            }

            return (1);
        }

        /*
         * br_UBA_AddTextChainsForSequences
         *
         * Walk through a sequence list, allocating, initializing and
         * linking in a text chain for each isolating run sequence.
         */
        static int br_UBA_AddTextChainsForSequences(ISOLATING_RUN_SEQUENCE irp)
        {
            int len;
            BIDIUNIT[] tcp;
            int tcp2;
            BIDIRUNLISTELEMENT brlp;

            if (irp == null)
            {
                /* No sequences to process */
                return (1);
            }

            var tirp = irp;
            while (tirp != null)
            {
                /* 
                 * An isolating run sequence consists of a sequence of
                 * runs, which may be discontiguous. To find the length
                 * of text chain to allocate, we first need to traverse
                 * the run list, accumulating the lengths of the runs.
                 */
                len = 0;
                brlp = tirp.theRuns;
                while (brlp != null)
                {
                    len += brlp.run.len;
                    brlp = brlp.next;
                }

                /*
                 * Write the length value back into the sequence, to store
                 * the length of the calculated text chain.
                 */
                tirp.len = len;
                if (len == 0)
                {
                    printf("Error: sequence {0} associated will null runs.\n", tirp.seqID);
                    return (0);
                }

                tcp = new BIDIUNIT[len];

                if (Trace(Trace5))
                {
                    printf("Allocated text chain: len={0}\n", len);
                }

                /*
                 * Copy BIDIUNIT pointers for the isolating run sequence into the
                 * text chain.
                 *
                 * This differs from the initialization for the level runs, because
                 * we have to read sequentially through each level run and
                 * append the BIDIUNIT pointers to the allocated array.
                 *
                 * First initialize the tcp2 pointer to the allocated text chain.
                 */
                tcp2 = 0;
                /*
                 * Process the run list in order.
                 */
                brlp = tirp.theRuns;
                while (brlp != null)
                {
                    var brp = brlp.run;
                    /* Append this run to the text chain. */
                    for (int bup = 0; bup < brp.elements.Length; bup++, tcp2++)
                    {
                        tcp[tcp2] = brp.elements[bup];
                    }

                    brlp = brlp.next;
                }

                /* Attach the initialized text chain to the bidi run. */
                tirp.textChain = tcp;
                tirp.textStart = 0;

                tirp = tirp.next;
            }

            return (1);
        }

        /*
         * br_UBA_IdentifyRuns
         *
         * This function runs Rule X10.
         *
         * X10 is treated with a separate function.
         * It logically occurs after the completion of the application
         * of rules X2-X8 sequentionally through the input string
         * and after X9 has identified and tagged any NOLEVEL values.
         * All levels have to be set before the level runs can be
         * accurately identified.
         *
         * Instead of marking up the BIDIUNIT vector with more
         * character-by-character information, the runs are best
         * handled by a separate structure. A linked list of runs is hung
         * off the UBACONTEXT struct, with each run containing pointers
         * to its start and end BIDIUNITs. This list can then be
         * traversed by the subsequent rules which operate on a
         * run-by-run basis.
         *
         * Note that this code presupposes that the input vector is
         * at least one unit long. Tests for a zero-length vector
         * should occur before hitting this function.
         *
         * It is possible that an input vector may consist *entirely*
         * of units set to NOLEVEL. This would happen if all the
         * units were bc=BN or bidi embedding/override controls,
         * which would have been "deleted" by this point from the
         * input by setting their levels to NOLEVEL. In that case
         * the input vector will have no level spans at all.
         */

        static int br_UBA_IdentifyRuns(UBACONTEXT ctxt)
        {
            int bdunext = 0;
            BIDIRUN brp;
            int spanlevel = 0;
            int spanlen = 0;

            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA_IdentifyRuns [X10]\n");
            }

            ctxt.dirtyBit = 0;

            /* Initialize for looping to extract spans. */
            var spanid = 1;
            var spanningDone = false;

            var bdu /* point to start of span */ = 0;
            var bduend /* point to last BIDIUNIT in vector */ = bdu + (ctxt.textLen - 1);

            while (!spanningDone)
            {
                spanningDone = br_SpanOneRun(ctxt.textUnits, bdu, bduend, ref bdunext, ref spanlevel, ref spanlen);

                if (Trace(Trace5))
                {
                    printf("Spanned run id={0}, level={1}, pos={2}, len={3}\n", spanid,
                        spanlevel, bdu, spanlen);
                }

                /*
                 * Process the extracted span. If the spanlevel was
                 * set to NOLEVEL, skip this step, as there is no
                 * span to process.
                 *
                 * The minimum length
                 * of a span should be 1, in which case the start of
                 * the span and the end of the span point at the same
                 * BIDIUNIT.
                 */

                if (spanlevel != NOLEVEL)
                {
                    if (spanningDone)
                    {
                        brp = br_ConstructBidiRun(ctxt.textUnits, spanid, spanlevel,
                            bdu, bduend, spanlen);
                    }
                    else
                    {
                        brp = br_ConstructBidiRun(ctxt.textUnits, spanid, spanlevel,
                            bdu, bdunext - 1, spanlen);
                    }

                    if (brp == null)
                    {
                        br_ErrPrint("Error in allocation of bidi run.\n");
                        return (0);
                    }

                    /* 
                     * If we have a valid run, append it to the run list
                     * in the context.
                     */
                    br_AppendBidiRun(ctxt, brp);
                }

                /* If that was the last span, exit the while loop. */

                if (!spanningDone)
                {
                    /* Set bdu to the next span start, increment the span id and come around. */
                    bdu = bdunext;
                    spanid++;
                }
            }

            if (Trace(Trace5))
            {
                if (spanid == 1)
                {
                    printf("1 run identified\n");
                }
                else
                {
                    printf("{0} runs identified\n", spanid);
                }
            }

            /*
             * Add text chains for each run, for uniform rule application.
             */
            var rc = br_UBA_AddTextChainsForRuns(ctxt.theRuns);

            if (rc != 1)
            {
                return (rc);
            }

            /*
             * Now that we have identified the runs, calculate the sor and eor
             * values for each run.
             */
            br_UBA_CalculateSorEor(ctxt);

            /*
             * This rule always has an impact, so just always set the dirtyBit
             * on exit.
             */
            ctxt.dirtyBit = 1;

            if (GetUBAVersion() > (int)UBA_Version_Type.UBA62)
            {
                ctxt.state = ALGORITHM_STATE.State_RunsDone;
            }
            else
            {
                ctxt.state = ALGORITHM_STATE.State_X10Done;
            }

            return (1);
        }

        /*
         * br_UBA_IdentifyIsolatingRunSequences
         *
         * This function applies only to UBA63. Once the embedding
         * levels are identified, UBA63 requires further processing
         * to assign each of the level runs to an isolating run sequence.
         *
         * Each level run must be uniquely assigned to exactly one
         * isolating run sequence. Each isolating run sequence must
         * have at least one level run, but may have more.
         *
         * The exact details on how to match up isolating run sequences
         * with level runs are specified in BD13.
         *
         * The strategy taken here is to scan the level runs in order.
         *
         * If a level run is not yet assigned to an isolating run sequence,
         * its seqID will be zero. Create a new isolating run sequence
         * and add this level run to it.
         *
         * If the last BIDIUNIT of *this* level run is an isolate
         * initiator (LRI/RLI/FSI), then scan ahead in the list of
         * level runs seeking the next level run which meets the
         * following criteria:
         *   1. seqID = 0 (not yet assigned to an isolating run sequence)
         *   2. its level matches the level we are processing
         *   3. the first BIDIUNIT is a PDI
         * If all those conditions are met, assign that next level run
         * to this isolating run sequence (set its seqID, and append to
         * the list).
         *
         * Repeat until we hit a level run that doesn't terminate with
         * an isolate initiator or we hit the end of the list of level
         * runs.
         *
         * That terminates the definition of the isolating run sequence
         * we are working on. Append it to the list of isolating run
         * sequences in the UBACONTEXT.
         *
         * Then advance to the next level run which has not yet been
         * assigned to an isolating run sequence and repeat the process.
         *
         * Continue until all level runs have been assigned to an
         * isolating run sequence.
         */

        static int br_UBA_IdentifyIsolatingRunSequences(UBACONTEXT ctxt)
        {
            ISOLATING_RUN_SEQUENCE irp;
            BIDIRUN brp2;
            int savelevel;

            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA_IdentifyIsolatingRunSequences [X10]\n");
            }

            ctxt.dirtyBit = 0;

            var rc = 1;
            var seqid = 0;

            var brp = ctxt.theRuns;

            while (brp != null)
            {
                /*
                 * Skip past any run which already has a seqID assigned
                 * to it. Only process runs with seqID == 0.
                 */
                if (brp.seqID == 0)
                {
                    seqid++;
                    irp = br_ConstructIsolatingRunSequence(seqid, brp);
                    if (irp == null)
                    {
                        br_ErrPrint("Error in allocation of isolating run sequence.\n");
                        return (0);
                    }

                    br_AppendIsolatingRunSequence(ctxt, irp);
                    brp.seqID = seqid;
                    /*
                     * Next check whether this run ends in an isolate initiator.
                     * If so, scan ahead looking for the run with the matching PDI.
                     */
                    if (brp.elements[brp.len - 1].bc_isoinit)
                    {
                        if (Trace(Trace5))
                        {
                            printf("Debug: found trailing isolate initiator\n");
                        }

                        /*
                         * Use a temporary brp2 run pointer for this scan, so
                         * the outer loop resumes correctly from where it left
                         * off in the main scan through the runs.
                         */
                        brp2 = brp;
                        savelevel = brp.level;
                        while (brp2.next != null)
                        {
                            brp2 = brp2.next;
                            if (Trace(Trace5))
                            {
                                printf("Debug: runID={0}, seqID={1}, level={2}, first.bc={3}\n", brp2.runID,
                                    brp2.seqID, brp2.level, br_FirstSignificantBC(brp2));
                            }

                            if ((brp2.seqID == 0) && (brp2.level == savelevel) &&
                                (br_FirstSignificantBC(brp2) == BIDIPROP.BIDI_PDI))
                            {
                                var brlp =
                                    /*
                                 * We matched the criteria for adding this run to the
                                 * sequence. Construct a BIDIRUNLISTELEMENT and append
                                 * it to the sequence.
                                 */
                                    br_ConstructBidiRunListElement();
                                if (brlp == null)
                                {
                                    br_ErrPrint("Error in allocation of isolating run sequence.\n");
                                    /* Let the br_DropContext do the cleanup. Less messy. */
                                    return (0);
                                }

                                /* Set the seq ID of the run to the seq ID of this sequence. */
                                brp2.seqID = seqid;
                                brlp.run = brp2;
                                /* Append it to the seqeunce. */
                                br_AppendBidiRunToSequence(irp, brlp);
                                if (Trace(Trace5))
                                {
                                    printf("Appended run id={0} to sequence id={1}\n",
                                        brp2.runID, seqid);
                                }

                                /*
                                 * Check is the last unit in *this* run is also
                                 * an isolate initiator. If not, we are done with
                                 * this sequence. If so, come around and scan for
                                 * another run with a matching PDI.
                                 */
                                if (!(brp2.elements[brp2.len - 1].bc_isoinit))
                                {
                                    break;
                                }
                            }
                        }
                    }

                    if (Trace(Trace5))
                    {
                        printf("Scanned sequence id={0}, level={1}\n", seqid,
                            irp.level);
                    }
                }

                /* Advance to the next run */
                brp = brp.next;
            }

            if (Trace(Trace5))
            {
                if (seqid == 1)
                {
                    printf("1 sequence identified.\n");
                }
                else
                {
                    printf("{0} sequences identified.\n", seqid);
                }
            }

            /*
             * Add text chains for each sequence, for uniform rule application.
             */

            rc = br_UBA_AddTextChainsForSequences(ctxt.theSequences);

            /*
             * Now that we have identified the sequences, calculate the sos and eos
             * values for each sequence.
             */
            br_UBA_CalculateSosEos(ctxt);

            /*
             * This rule always has an impact, so just always set the dirtyBit
             * on exit.
             */
            ctxt.dirtyBit = 1;
            ctxt.state = ALGORITHM_STATE.State_X10Done;
            return (1);
        }

        /*******************************************************************/

        /*
         * SECTION: Resolving Weak Types: Rules W1-W7
         *
         * This section runs the resolving weak type rules of the UBA.
         *
         * In an optimized implementation, these resolutions would probably
         * be combined into fewer passes, but for clarity, this reference
         * implementation does a distinct pass for each rule, dealing with
         * different types of resolution. This also makes it easier to
         * see the separate resolutions as they occur in the display status,
         * if desired.
         *
         * Each rule processes a single text chain.
         *
         * The br_UBA_RuleDispatch handles the dispatch of rules and
         * decides whether they are applying to a list of runs (UBA62)
         * or to a list of isolating run sequences (UBA63).
         */

        /*
         * br_IsStrongType
         *
         * Encapsulate the checking for Bidi_Class values of strong
         * type (R, L, AL).
         */
#if NOTDEF
        static int br_IsStrongType(BIDIPROP bc)
        {
            if ((bc == BIDI_L) || (bc == BIDI_R) || (bc == BIDI_AL))
            {
                return (1);
            }
            else
            {
                return (0);
            }
        }

#endif
        /*
         * br_IsNeutralType
         *
         * Encapsulate the checking for Bidi_Class values of neutral
         * type (B, S, WS, ON).
         *
         * Note that BIDI_B is kept at the end of each "paragraph" to
         * be run through the algorithm, so it *can* occur as the very
         * last element of the paragraph and has to be checked here.
         *
         * To keep the expression of the rules fairly simple, the
         * extension of "neutral" to "NI" in UBA63, including all
         * the isolate format controls, is implemented in this rule
         * with a test on UBA version.
         *
         * Note that optimized implementations can speed up this kind
         * of checking by keeping precomputed boolean or bit arrays indexed by
         * Bidi_Class values and returning these kinds of True/False
         * queries by a single array lookup instead of using chains of
         * individual Bidi_Class equality tests.
         */

        static bool br_IsNeutralType(BIDIPROP bc)
        {
            if ((bc == BIDIPROP.BIDI_ON) || (bc == BIDIPROP.BIDI_WS) || (bc == BIDIPROP.BIDI_S) || (bc == BIDIPROP.BIDI_B))
            {
                return true;
            }
            else if ((GetUBAVersion() > (int)UBA_Version_Type.UBA62) && (br_IsIsolateControl(bc)))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /*
         * br_IsPriorContext
         *
         * Scan backwards in a text chain, checking if the first prior character matches
         * the bc value passed in. Skip over any "deleted" controls, which
         * have NOLEVEL.
         */

        static bool br_IsPriorContext(BIDIUNIT[] textChain, int buppfirst, int bupp, BIDIPROP bc)
        {
            if (bupp == buppfirst)
            {
                return false;
            }

            var tbupp = bupp - 1;
            while (tbupp >= buppfirst)
            {
                if (textChain[tbupp].bc == bc)
                {
                    return true;
                }
                else if (textChain[tbupp].level == NOLEVEL)
                {
                    tbupp--;
                }
                else
                {
                    return false;
                }
            }

            return false;
        }

        /*
         * br_IsFollowingContext
         *
         * Scan forwards in a text chain, checking if the first subsequent character matches
         * the bc value passed in. Skip over any "deleted" controls, which
         * have NOLEVEL.
         */

        static bool br_IsFollowingContext(BIDIUNIT[] textChain, int bupplast, int bupp, BIDIPROP bc)
        {
            if (bupp == bupplast)
            {
                return false;
            }

            var tbupp = bupp + 1;
            while (tbupp <= bupplast)
            {
                if (textChain[tbupp].bc == bc)
                {
                    return true;
                }
                else if (textChain[tbupp].level == NOLEVEL)
                {
                    tbupp++;
                }
                else
                {
                    return false;
                }
            }

            return false;
        }

        /*
         * br_UBA_ResolveCombiningMarks
         *
         * This is the method for Rule W1.
         *
         * Resolve combining marks for a single text chain.
         *
         * For each character in the text chain, examine its
         * Bidi_Class. For characters of bc=NSM, change the Bidi_Class
         * value to that of the preceding character. Formatting characters
         * (Bidi_Class RLE, LRE, RLO, LRO, PDF) and boundary neutral (Bidi_Class BN)
         * are skipped over in this calculation, because they have been
         * "deleted" by Rule X9.
         *
         * If a bc=NSM character occurs at the start of a text chain, it is given
         * the Bidi_Class of sot (either R or L).
         */

        static int br_UBA_ResolveCombiningMarks(BIDI_RULE_CONTEXT brcp)
        {
            int dirtyBit = 0;

            /*
             * Default the priorbc to the sot value passed in.
             */
            var priorbc = brcp.sot;
            /*
             * Process the text chain from first to last BIDIUNIT,
             * checking the Bidi_Class of each character.
             */
            var bupp = brcp.textStart;
            var buppend = bupp + brcp.len;
            while (bupp < buppend)
            {
                if (brcp.textChain[bupp].bc == BIDIPROP.BIDI_NSM)
                    /* Reset any NSM to the Bidi_Class stored in priorbc. */
                {
                    brcp.textChain[bupp].bc = priorbc;
                    if ((priorbc == BIDIPROP.BIDI_AN) || (priorbc == BIDIPROP.BIDI_EN))
                    {
                        brcp.textChain[bupp].bc_numeric = true;
                    }

                    dirtyBit = 1;
                }
                /* For a "deleted" BIDIUNIT, do nothing. */
                else if (brcp.textChain[bupp].level != NOLEVEL)
                {
                    /* For all other Bidi_Class, set priorbc to the current value. */
                    priorbc = brcp.textChain[bupp].bc;
                }

                bupp++;
            }

            return (dirtyBit);
        }

        /*
         * br_UBA_ResolveEuropeanNumbers
         *
         * This is the method for Rule W2.
         *
         * Resolve European numbers for a single text chain.
         *
         * For each character in the text chain, examine its
         * Bidi_Class. For characters of bc=EN, scan back to find the first
         * character of strong type (or sot). If the strong type is bc=AL,
         * change the Bidi_Class EN to AN. Formatting characters
         * (Bidi_Class RLE, LRE, RLO, LRO, PDF) and boundary neutral (Bidi_Class BN)
         * are skipped over in this calculation, because they have been
         * "deleted" by Rule X9.
         */

        static int br_UBA_ResolveEuropeanNumbers(BIDI_RULE_CONTEXT brcp)
        {
            BIDIPROP firststrongbc;
            int bupp;
            int bupp2;
            int dirtyBit = 0;

            var buppfirst = brcp.textStart;
            var bupplast = buppfirst + brcp.len - 1;
            /*
             * Process the text chain in reverse from last to first BIDIUNIT,
             * checking the Bidi_Class of each character.
             */
            for (bupp = bupplast; bupp >= buppfirst; bupp--)
            {
                if (brcp.textChain[bupp].bc == BIDIPROP.BIDI_EN)
                    /* For any EN found, scan back in the run to find the first strong type */
                {
                    /* Default firststrongbc to sot */
                    firststrongbc = brcp.sot;
                    if (bupp > buppfirst)
                    {
                        for (bupp2 = bupp - 1; bupp2 >= buppfirst; bupp2--)
                        {
                            if ((brcp.textChain[bupp2].bc == BIDIPROP.BIDI_L) || (brcp.textChain[bupp2].bc == BIDIPROP.BIDI_R) || (brcp.textChain[bupp2].bc == BIDIPROP.BIDI_AL))
                            {
                                firststrongbc = brcp.textChain[bupp2].bc;
                                break;
                            }
                        }
                    }

                    /*
                     * Check if the first strong type is AL. If so
                     * reset this EN to AN. This change does not affect
                     * the bc_numeric flag.
                     */
                    if (firststrongbc == BIDIPROP.BIDI_AL)
                    {
                        brcp.textChain[bupp].bc = BIDIPROP.BIDI_AN;
                        dirtyBit = 1;
                    }
                }
            }

            return (dirtyBit);
        }

        /*
         * br_UBA_ResolveAL
         *
         * This is the method for Rule W3.
         *
         * Resolve Bidi_Class=AL for a single text chain.
         *
         * For each character in the text chain, examine its
         * Bidi_Class. For characters of bc=AL, change the Bidi_Class
         * value to R.
         */

        static int br_UBA_ResolveAL(BIDI_RULE_CONTEXT brcp)
        {
            int bupp;
            int dirtyBit = 0;

            var buppfirst = brcp.textStart;
            var bupplast = buppfirst + brcp.len - 1;
            /*
             * Process the text chain from first to last BIDIUNIT,
             * checking the Bidi_Class of each character.
             */
            for (bupp = buppfirst; bupp <= bupplast; bupp++)
            {
                if (brcp.textChain[bupp].bc == BIDIPROP.BIDI_AL)
                    /* Reset any AL to the Bidi_Class R. */
                {
                    brcp.textChain[bupp].bc = BIDIPROP.BIDI_R;
                    dirtyBit = 1;
                }
            }

            return (dirtyBit);
        }

        /*
         * br_UBA_ResolveSeparators
         *
         * This is the method for Rule W4.
         *
         * Resolve Bidi_Class=ES and CS for a single text chain.
         *
         * For each character in the text chain, examine its
         * Bidi_Class.
         *
         * For characters of bc=ES, check if they are *between* EN.
         * If so, change their Bidi_Class to EN.
         *
         * For characters of bc=CS, check if they are *between* EN
         * or between AN. If so, change their Bidi_Class to match.
         *
         * Update the bc_numeric flag for any ES or CS changed.
         */

        static int br_UBA_ResolveSeparators(BIDI_RULE_CONTEXT brcp)
        {
            int bupp;
            int dirtyBit = 0;

            var buppfirst = brcp.textStart;
            var bupplast = buppfirst + brcp.len - 1;
            /*
             * Process the text chain from first to last BIDIUNIT,
             * checking the Bidi_Class of each character.
             */
            for (bupp = buppfirst; bupp <= bupplast; bupp++)
            {
                if (brcp.textChain[bupp].bc == BIDIPROP.BIDI_ES)
                    /* Check to see if ES is in context EN ES EN */
                {
                    if (br_IsPriorContext(brcp.textChain, buppfirst, bupp, BIDIPROP.BIDI_EN) &&
                        br_IsFollowingContext(brcp.textChain, bupplast, bupp, BIDIPROP.BIDI_EN))
                    {
                        brcp.textChain[bupp].bc = BIDIPROP.BIDI_EN;
                        brcp.textChain[bupp].bc_numeric = true;
                        dirtyBit = 1;
                    }
                }
                else if (brcp.textChain[bupp].bc == BIDIPROP.BIDI_CS)
                    /* Check to see if CS is in context EN CS EN  or AN CS AN */
                {
                    if (br_IsPriorContext(brcp.textChain, buppfirst, bupp, BIDIPROP.BIDI_EN) &&
                        br_IsFollowingContext(brcp.textChain, bupplast, bupp, BIDIPROP.BIDI_EN))
                    {
                        brcp.textChain[bupp].bc = BIDIPROP.BIDI_EN;
                        brcp.textChain[bupp].bc_numeric = true;
                        dirtyBit = 1;
                    }
                    else if (br_IsPriorContext(brcp.textChain, buppfirst, bupp, BIDIPROP.BIDI_AN) &&
                             br_IsFollowingContext(brcp.textChain, bupplast, bupp, BIDIPROP.BIDI_AN))
                    {
                        brcp.textChain[bupp].bc = BIDIPROP.BIDI_AN;
                        brcp.textChain[bupp].bc_numeric = true;
                        dirtyBit = 1;
                    }
                }
            }

            return (dirtyBit);
        }

        /*
         * br_UBA_ResolveTerminators
         *
         * This is the method for Rule W5.
         *
         * Resolve Bidi_Class=ET for a single text chain.
         *
         * For each character in the text chain, examine its
         * Bidi_Class.
         *
         * For characters of bc=ET, check if they are *next to* EN.
         * If so, change their Bidi_Class to EN. This includes
         * ET on either side of EN, so the context on both sides
         * needs to be checked.
         *
         * Because this rule applies to indefinite sequences of ET,
         * and because the context which triggers any change is
         * adjacency to EN, the strategy taken here is to seek for
         * EN first. If found, scan backwards, changing any eligible
         * ET to EN. Then scan forwards, changing any eligible ET
         * to EN. Then continue the search from the point of the
         * last ET changed (if any).
         * 
         * Update the bc_numeric flag for any ET changed.
         */

        static int br_UBA_ResolveTerminators(BIDI_RULE_CONTEXT brcp)
        {
            int bupp2;
            int dirtyBit = 0;

            var buppfirst = brcp.textStart;
            var bupplast = buppfirst + brcp.len - 1;

            /*
             * Process the run from first to last BIDIUNIT,
             * checking the Bidi_Class of each character.
             */
            var bupp = buppfirst;
            while (bupp <= bupplast)
            {
                if (brcp.textChain[bupp].bc == BIDIPROP.BIDI_EN)
                    /* Check to see if there are any adjacent ET */
                {
                    /* First scan left for ET, skipping any NOLEVEL characters */
                    if (bupp > buppfirst)
                    {
                        for (bupp2 = bupp - 1; bupp2 >= buppfirst; bupp2--)
                        {
                            if (brcp.textChain[bupp2].bc == BIDIPROP.BIDI_ET)
                            {
                                brcp.textChain[bupp2].bc = BIDIPROP.BIDI_EN;
                                brcp.textChain[bupp2].bc_numeric = true;
                                dirtyBit = 1;
                            }
                            else if (brcp.textChain[bupp2].level != NOLEVEL)
                            {
                                break;
                            }
                        }
                    }

                    /* Next scan right for ET, skipping any NOLEVEL characters */
                    if (bupp < bupplast)
                    {
                        for (bupp2 = bupp + 1; bupp2 <= bupplast; bupp2++)
                        {
                            if (brcp.textChain[bupp2].bc == BIDIPROP.BIDI_ET)
                            {
                                brcp.textChain[bupp2].bc = BIDIPROP.BIDI_EN;
                                brcp.textChain[bupp2].bc_numeric = true;
                                dirtyBit = 1;
                            }
                            else if (brcp.textChain[bupp2].level != NOLEVEL)
                            {
                                break;
                            }
                        }

                        /*
                         * If we scanned ahead, reset bupp to bupp2, to prevent 
                         * reprocessing characters
                         * that have already been checked and/or changed.
                         */
                        bupp = bupp2;
                    }
                    else
                        /*
                         * Otherwise just increment bupp and come around.
                         */
                    {
                        bupp++;
                    }
                }
                else
                {
                    /* Increment bupp and come around. */
                    bupp++;
                }
            }

            return (dirtyBit);
        }

        /*
         * br_UBA_ResolveESCSET
         *
         * This is the method for Rule W6.
         *
         * Resolve remaining Bidi_Class=ES, CS, or ET for a single text chain.
         *
         * For each character in the text chain, examine its
         * Bidi_Class. For characters of bc=ES, bc=CS, or bc=ET, change 
         * the Bidi_Class value to ON. This resolves any remaining
         * separators or terminators which were not already processed
         * by Rules W4 and W5.
         */

        static int br_UBA_ResolveESCSET(BIDI_RULE_CONTEXT brcp)
        {
            int bupp;
            int dirtyBit = 0;

            var buppfirst = brcp.textStart;
            var bupplast = buppfirst + brcp.len - 1;

            /*
             * Process the text chain from first to last BIDIUNIT,
             * checking the Bidi_Class of each character.
             */
            for (bupp = buppfirst; bupp <= bupplast; bupp++)
            {
                if ((brcp.textChain[bupp].bc == BIDIPROP.BIDI_ES) || (brcp.textChain[bupp].bc == BIDIPROP.BIDI_CS) ||
                    (brcp.textChain[bupp].bc == BIDIPROP.BIDI_ET))
                    /* Reset any ES CS ET to the Bidi_Class ON. */
                {
                    brcp.textChain[bupp].bc = BIDIPROP.BIDI_ON;
                    dirtyBit = 1;
                }
            }

            return (dirtyBit);
        }

        /*
         * br_UBA_ResolveEN
         *
         * This is the method for Rule W7.
         *
         * Resolve Bidi_Class=EN for a single level text chain.
         *
         * Process the text chain in reverse order. For each character in the text chain, examine its
         * Bidi_Class. For characters of bc=EN, scan back to find the first strong
         * directional type. If that type is L, change the Bidi_Class
         * value of the number to L.
         */

        static int br_UBA_ResolveEN(BIDI_RULE_CONTEXT brcp)
        {
            int bupp;
            int bupp2;
            BIDIPROP bc;
            int dirtyBit = 0;

            var buppfirst = brcp.textStart;
            var bupplast = buppfirst + brcp.len - 1;
            /*
             * Process the text chain from last to first BIDIUNIT,
             * checking the Bidi_Class of each character.
             */
            for (bupp = bupplast; bupp >= buppfirst; bupp--)
            {
                if (brcp.textChain[bupp].bc == BIDIPROP.BIDI_EN)
                    /* Scan back to find the first strong type,
                     * R, L, or sot.
                     */
                {
                    bc = BIDIPROP.BIDI_None;
                    if (bupp == buppfirst)
                    {
                        bc = brcp.sot;
                    }
                    else
                    {
                        bupp2 = bupp - 1;
                        while (bupp2 >= buppfirst)
                        {
                            bc = brcp.textChain[bupp2].bc;
                            if ((bc == BIDIPROP.BIDI_L) || (bc == BIDIPROP.BIDI_R))
                            {
                                break;
                            }

                            if (bupp2 == buppfirst)
                            {
                                bc = brcp.sot;
                                break;
                            }

                            bupp2--;
                        }
                    }

                    /*
                     * If the first strong type is L, reset the
                     * bc of the number to L. Update the bc_numeric 
                     * flag accordingly.
                     */
                    if (bc == BIDIPROP.BIDI_L)
                    {
                        brcp.textChain[bupp].bc = BIDIPROP.BIDI_L;
                        brcp.textChain[bupp].bc_numeric = false;
                        dirtyBit = 1;
                    }
                }
            }

            return (dirtyBit);
        }

        /*******************************************************************/

        /*
         * SECTION: Resolving Neutral Types: Rules N0-N2
         *
         * This section runs the resolving neutral type rules of the UBA.
         *
         * Each rule processes a text chain from start to finish.
         */

        /*
         * Context scanning utility functions.
         *
         * These are partly shared by both Rule N0 and Rule N1.
         *
         * These functions differ from
         * those used for resolving weak types, in that they skip by any
         * sequence of neutral types encountered, looking for the first
         * strong type, instead of checking only the immediately adjacent
         * context.
         *
         * If the routines hit the edge of a run, they return true or
         * false based on the type of sot or eot for that run.
         *
         * Rather than collapsing these six scanning functions into a
         * single context checking function, they are unrolled here into
         * six separate functions, to make each one's processing clearer.
         */

        /*
         * br_IsPriorContextL
         *
         * Scan backwards in a text chain, checking if the first non-neutral character is an "L" type.
         * Skip over any "deleted" controls, which have NOLEVEL, as well as any neutral types.
         */

        static bool br_IsPriorContextL(BIDIUNIT[] textChain, int buppfirst, int bupp, BIDIPROP sot)
        {
            if (bupp == buppfirst)
            {
                return sot == BIDIPROP.BIDI_L;
            }

            var tbupp = bupp - 1;
            while (tbupp >= buppfirst)
            {
                if (textChain[tbupp].bc == BIDIPROP.BIDI_L)
                {
                    return true;
                }
                else if (textChain[tbupp].level == NOLEVEL)
                {
                    tbupp--;
                }
                else if (br_IsNeutralType(textChain[tbupp].bc))
                {
                    tbupp--;
                }
                else
                {
                    return false;
                }
            }

            return sot == BIDIPROP.BIDI_L;
        }

        /*
         * br_IsFollowingContextL
         *
         * Scan forwards in a text chain, checking if the first non-neutral character is an "L" type.
         * Skip over any "deleted" controls, which have NOLEVEL, as well as any neutral types.
         */

        static bool br_IsFollowingContextL(BIDIUNIT[] textChain, int bupplast, int bupp, BIDIPROP eot)
        {
            if (bupp == bupplast)
            {
                return eot == BIDIPROP.BIDI_L;
            }

            var tbupp = bupp + 1;
            while (tbupp <= bupplast)
            {
                if (textChain[tbupp].bc == BIDIPROP.BIDI_L)
                {
                    return true;
                }
                else if (textChain[tbupp].level == NOLEVEL)
                {
                    tbupp++;
                }
                else if (br_IsNeutralType(textChain[tbupp].bc))
                {
                    tbupp++;
                }
                else
                {
                    return false;
                }
            }

            return eot == BIDIPROP.BIDI_L;
        }

        /*
         * br_IsPriorContextR
         *
         * Used by Rule N0.
         *
         * Scan backwards in a text chain, checking if the first non-neutral character is an "R" type.
         * Skip over any "deleted" controls, which have NOLEVEL, as well as any neutral types.
         */

        static int br_IsPriorContextR(BIDIUNIT[] textChain, int buppfirst, int bupp, BIDIPROP sot)
        {
            if (bupp == buppfirst)
            {
                return ((sot == BIDIPROP.BIDI_R) ? 1 : 0);
            }

            var tbupp = bupp - 1;
            while (tbupp >= buppfirst)
            {
                if (textChain[tbupp].bc == BIDIPROP.BIDI_R)
                {
                    return (1);
                }
                else if (textChain[tbupp].level == NOLEVEL)
                {
                    tbupp--;
                }
                else if (br_IsNeutralType(textChain[tbupp].bc))
                {
                    tbupp--;
                }
                else
                {
                    return (0);
                }
            }

            return ((sot == BIDIPROP.BIDI_R) ? 1 : 0);
        }

        /*
         * br_IsFollowingContextR
         *
         * Used by Rule N0.
         *
         * Scan forwards in a text chain, checking if the first non-neutral character is an "R" type.
         * Skip over any "deleted" controls, which have NOLEVEL, as well as any neutral types.
         */

        static int br_IsFollowingContextR(BIDIUNIT[] textChain, int bupplast, int bupp, BIDIPROP eot)
        {
            if (bupp == bupplast)
            {
                return ((eot == BIDIPROP.BIDI_R) ? 1 : 0);
            }

            var tbupp = bupp + 1;
            while (tbupp <= bupplast)
            {
                if (textChain[tbupp].bc == BIDIPROP.BIDI_R)
                {
                    return (1);
                }
                else if (textChain[tbupp].level == NOLEVEL)
                {
                    tbupp++;
                }
                else if (br_IsNeutralType(textChain[tbupp].bc))
                {
                    tbupp++;
                }
                else
                {
                    return (0);
                }
            }

            return ((eot == BIDIPROP.BIDI_R) ? 1 : 0);
        }

        /*
         * br_IsPriorContextRANEN
         *
         * Used by Rule N1.
         *
         * Scan backwards in a text chain, checking if the first non-neutral character is an "R" type.
         * (BIDI_R, BIDI_AN, BIDI_EN) Skip over any "deleted" controls, which
         * have NOLEVEL, as well as any neutral types.
         */

        static bool br_IsPriorContextRANEN(BIDIUNIT[] textChain, int buppfirst, int bupp, BIDIPROP sot)
        {
            if (bupp == buppfirst)
            {
                return sot == BIDIPROP.BIDI_R;
            }

            var tbupp = bupp - 1;
            while (tbupp >= buppfirst)
            {
                if ((textChain[tbupp].bc == BIDIPROP.BIDI_R) || (textChain[tbupp].bc_numeric))
                {
                    return true;
                }
                else if (textChain[tbupp].level == NOLEVEL)
                {
                    tbupp--;
                }
                else if (br_IsNeutralType(textChain[tbupp].bc))
                {
                    tbupp--;
                }
                else
                {
                    return false;
                }
            }

            return sot == BIDIPROP.BIDI_R;
        }

        /*
         * br_IsFollowingContextRANEN
         *
         * Used by Rule N1.
         *
         * Scan forwards in a text chain, checking if the first non-neutral character is an "R" type.
         * (BIDI_R, BIDI_AN, BIDI_EN) Skip over any "deleted" controls, which
         * have NOLEVEL, as well as any neutral types.
         */

        static bool br_IsFollowingContextRANEN(BIDIUNIT[] textChain, int bupplast, int bupp, BIDIPROP eot)
        {
            if (bupp == bupplast)
            {
                return eot == BIDIPROP.BIDI_R;
            }

            var tbupp = bupp + 1;
            while (tbupp <= bupplast)
            {
                if ((textChain[tbupp].bc == BIDIPROP.BIDI_R) || (textChain[tbupp].bc_numeric))
                {
                    return true;
                }
                else if (textChain[tbupp].level == NOLEVEL)
                {
                    tbupp++;
                }
                else if (br_IsNeutralType(textChain[tbupp].bc))
                {
                    tbupp++;
                }
                else
                {
                    return false;
                }
            }

            return eot == BIDIPROP.BIDI_R;
        }

        /* 
         * Bracket Pairing Stack
         *
         * The bracket stack is specific to Rule N0. 
         */

        static void br_InitBracketStack()
        {
            bracketStackTop = 0;
            bracketStackMax = MAXPAIRINGDEPTH;
        }

        /* 
         * br_PushBracketStack
         *
         * Push an element on the stack. 
         *
         * For UBA63 (and UBA70), the behavior for a stack overflow
         * here was implementation defined.
         *
         * Starting with UBA80, a stack overflow must terminate
         * processing of bracket pairs for the immediate isolating
         * run. To implement this, br_PushBracketStack now
         * returns a value:
         *   1  Stack had room, push was successful.
         *   0  Stack was full, push failed.
         */
        static int br_PushBracketStack(BRACKETSTACKELEMENT sptr)
        {
            /* Check for stack full */
            if (bracketStackTop < bracketStackMax)
            {
                if (Trace(Trace6))
                {
                    printf("Trace: br_PushBracketStack, bracket={0:X4}, pos={1}\n",
                        sptr.bracket, sptr.pos);
                }

                bracketStackTop++;
                bracketStack[bracketStackTop].bracket = sptr.bracket;
                bracketStack[bracketStackTop].pos = sptr.pos;
                return (1);
            }
            else
            {
                if (Trace(Trace6))
                {
                    printf("Trace: br_PushBracketStack, stack full\n");
                }

                return (0);
            }
        }

        /* 
         * br_PopBracketStack
         *
         * Pop n elements off the stack.
         * This pop does not recover element data. It just
         * moves the stack pointer.
         *
         * If n is greater than the stack depth, just empty the stack.
         */
        static void br_PopBracketStack(int n)
        {
            var stackDepth = bracketStackTop;
            if (stackDepth > n)
            {
                bracketStackTop -= n;
            }
            else
            {
                bracketStackTop = 0;
            }

            if (Trace(Trace6))
            {
                printf("Trace: br_PopBracketStack,  #elements={0}\n", n);
            }
        }

        /* 
         * br_PeekBracketStack
         *
         * Examine an element at depth n on the stack, but don't pop it.
         * A depth of 1 is defined as the top of the stack.
         *
         * Return 1 for peek o.k.
         * Return 0 for stack empty or peek past bottom.
         */
        static int br_PeekBracketStack(BRACKETSTACKELEMENT sptr, int depth)
        {
            var tsptr = bracketStackTop - depth + 1;
            if (Trace(Trace6))
            {
                printf("Trace: br_PeekBracketStack, stack={0}, top={1}, tsptr={2}\n", bracketStack,
                    bracketStackTop, tsptr);
            }

            /* Check for stack empty */
            if (tsptr > 0)
            {
                if (Trace(Trace6))
                {
                    printf("Trace: br_PeekBracketStack, bracket={0:X4}, pos={1}\n",
                        bracketStack[tsptr].bracket, bracketStack[tsptr].pos);
                }

                sptr.bracket = bracketStack[tsptr].bracket;
                sptr.pos = bracketStack[tsptr].pos;
                return (1);
            }
            else
            {
                return (0);
            }
        }

        /*
         * pairingList
         *
         * The bracket pair list is specific to Rule N0.
         */

        /*
         * br_ConstructPair
         *
         * Allocate and initiate a PAIRINGELEMENT
         */

        static PAIRINGELEMENT br_ConstructPair(int pos1, int pos2)
        {
            PAIRINGELEMENT pair = new PAIRINGELEMENT
            {
                openingpos = pos1,
                closingpos = pos2,
                next = null
            };
            return pair;
        }

        /*
         * br_AppendToPairList
         *
         * Append a pair to the pairList.
         */

        static void br_AppendToPairList(PAIRINGELEMENT pair)
        {
            if (pairList == null)
            {
                pairList = pair;
            }
            else
            {
                var tpair = pairList;
                while (tpair.next != null)
                {
                    tpair = tpair.next;
                }

                tpair.next = pair;
            }
        }

        /*
         * br_DropPairList
         *
         * Clean up the allocations in the pairList when done.
         * Also reset the static pairList to null.
         */

        static void br_DropPairList()
        {
            // PAIRINGELEMENT tpair;
            // PAIRINGELEMENT tpair2;
            //
            // tpair = pairList;
            // while (tpair != null)
            // {
            //     tpair2 = tpair.next;
            //     tpair.next = null;
            //     tpair = tpair2;
            // }

            pairList = null;
        }

        /*
         * br_SeekOpeningBracketMatch
         *
         * Seek an opening bracket pair for the closing bracket
         * passed in.
         *
         * This is a stack based search.
         * Start with the top element in the stack and search
         * downwards until we either find a match or reach the
         * bottom of the stack.
         *
         * If we find a match, construct and append the bracket
         * pair to the pairList. Then pop the stack for all the
         * levels down to the level where we found the match.
         * (This approach is designed to discard pairs that
         * are not cleanly nested.)
         *
         * If we search all the way to the bottom of the stack
         * without finding a match, just return without changing
         * state. This represents a closing bracket with no
         * opening bracket to match it. Just discard and move on.
         */

        static int br_SeekOpeningBracketMatch(U_Int_32 closingcp, int pos)
        {
            int rc;
            PAIRINGELEMENT pair;
            BRACKETSTACKELEMENT bracketData = new BRACKETSTACKELEMENT();

            var depth = 1;
            while (depth <= MAXPAIRINGDEPTH)
            {
                rc = br_PeekBracketStack(bracketData, depth);
                if (rc == 0)
                {
                    /* 
                     * Either the bracket stack is empty or the value of
                     * depth exceeds the current depth of the stack.
                     * Return no match.
                     */
                    if (Trace(Trace6))
                    {
                        printf("Debug: br_PeekBracketStack rc=0, depth={}\n", depth);
                    }

                    return (0);
                }

                /*
                 * The basic test is for the closingcp equal to the bpb value
                 * stored in the bracketData. But to account for the canonical
                 * equivalences for U+2329 and U+232A, tack on extra checks here
                 * for the asymmetrical matches. This hard-coded check avoids
                 * having to require full normalization of all the bracket code
                 * points before checking. It is highly unlikely that additional
                 * canonical singletons for bracket pairs will be added to future
                 * versions of the UCD.
                 */
                if ((bracketData.bracket == closingcp) ||
                    ((bracketData.bracket == 0x232A) && (closingcp == 0x3009)) ||
                    ((bracketData.bracket == 0x3009) && (closingcp == 0x232A)))
                {
                    /*
                     * This is a match. Construct a pair and append
                     * it to the pair list.
                     */
                    pair = br_ConstructPair(bracketData.pos, pos);
                    if (pair == null)
                    {
                        /* clean up any list allocation already done */
                        br_DropPairList();
                        return (0);
                    }
                    else
                    {
                        if (Trace(Trace7))
                        {
                            printf("Appended pair: opening pos {0}, closing pos {1}\n",
                                pair.openingpos, pair.closingpos);
                        }

                        br_AppendToPairList(pair);
                        /* pop through the stack to this depth */
                        br_PopBracketStack(depth);
                        return (1);
                    }
                }

                depth++;
            }

            /* Not reached, but return no match */
            return (0);
        }

        /*
         * br_DisplayPairList
         *
         * For debugging the list sorting, print out a display
         * of the contents of the pair list.
         */

        static void br_DisplayPairList(PAIRINGELEMENT theList)
        {
            printf("Pair list: ");
            var pair = theList;
            var loopcounter = 0;
            while (pair != null)
            {
                if (loopcounter > MAXPAIRINGDEPTH)
                {
                    /* 
                     * This should not occur, but bail out here,
                     * to avoid problems in list processing
                     * during development.
                     */
                    br_ErrPrint("Error: Loop limit exceeded in br_DisplayPairList\n");
                    return;
                }

                printf(" {0}{1},{2}{3}", '{', pair.openingpos, pair.closingpos, '}');
                pair = pair.next;
                loopcounter++;
            }

            printf("\n");
        }

        /*
         * br_InsertPairInList
         *
         * Insert into a pair list in sorted order by the openingpos value
         * of the pair.
         *
         * Always returns a pointer to the head of the list.
         */

        static PAIRINGELEMENT br_InsertPairInList(PAIRINGELEMENT theList, PAIRINGELEMENT pair)
        {
            PAIRINGELEMENT plp2;

            /*
             * If the list is empty, set pair.next to null and return pair
             * as the head of the list.
             */
            if (theList == null)
            {
                /* Create the head of the list */
                if (Trace(Trace7))
                {
                    printf("Create head\n");
                }

                pair.next = null;
                return (pair);
            }

            var plp = theList;
            /*
             * To insert at the head of the list, set pair.next to plp
             * and return pair as the head of the list.
             */
            if (pair.openingpos < plp.openingpos)
            {
                /* Insert at the head of the list */
                if (Trace(Trace7))
                {
                    printf("Insert at head\n");
                }

                pair.next = plp;
                return (pair);
            }
            else
            {
                /*
                 * Scan down the list searching for an insertion point.
                 *
                 * The exist always return theList unchanged as the head
                 * of the list.
                 */
                while (plp != null)
                {
                    plp2 = plp.next;
                    if (plp2 == null)
                    {
                        /* Append at the end of the list */
                        if (Trace(Trace7))
                        {
                            printf("Append at end\n");
                        }

                        pair.next = null;
                        plp.next = pair;
                        return (theList);
                    }
                    else if (pair.openingpos < plp2.openingpos)
                    {
                        /* Insert at this point in the list */
                        plp.next = pair;
                        pair.next = plp2;
                        if (Trace(Trace7))
                        {
                            printf("Insert in middle\n");
                        }

                        return (theList);
                    }

                    plp = plp2;
                    if (Trace(Trace7))
                    {
                        printf("Seeking insertion point...\n");
                    }
                }
            }

            /*
             * Not reached
             */
            return (theList);
        }

        /* 
         * br_SortPairList
         *
         * Because of the way the stack
         * processing works, the pairs may not be in the best order
         * in the pair list for further processing. Sort them
         * by position order of the opening bracket.
         *
         * Strategy: Nothing fancy here. These sort pair lists
         * will typically be fairly short, so this reference
         * implementation doesn't worry about seeking sort
         * optimization schemes. A temporary pair list is
         * constructed. The existing pair list is traversed
         * once and moved into the temporary pair list by
         * a list insertion sort. Then the pairList head
         * is reset to the head of this sorted list.
         *
         * This scheme isn't the fastest possible sorting, but
         * is saves doing any reallocation of the pairs in
         * the list, simply rehooking them in sorted order
         * in a new list.
         */

        static void br_SortPairList()
        {
            PAIRINGELEMENT plp2;

            if (Trace(Trace7))
            {
                printf("Trace: Entering br_SortPairList\n");
                br_DisplayPairList(pairList);
            }

            PAIRINGELEMENT tempPairList = null;
            var plp = pairList;
            var loopcounter = 0;
            if (Trace(Trace7))
            {
                printf("loopcounter:sort action\n");
            }

            while (plp != null)
            {
                if (loopcounter > MAXPAIRINGDEPTH)
                {
                    /* 
                     * Should not occur, but do a safety check
                     * to prevent runaway processing during
                     * development.
                     */
                    br_ErrPrint("Error: Loop limit exceeded in br_SortPairList\n");
                    return;
                }

                loopcounter++;
                if (Trace(Trace7))
                {
                    printf("{0}:", loopcounter);
                }

                /* Save the next pointer in the list */
                plp2 = plp.next;
                /* Insert plp into the new list */
                tempPairList = br_InsertPairInList(tempPairList, plp);
                /* Advance plp to the saved next pointer */
                plp = plp2;
            }

            /* Reset pairList to the sorted tempPairList */

            pairList = tempPairList;

            if (Trace(Trace7))
            {
                printf("Trace: Exiting br_SortPairList\n");
                br_DisplayPairList(pairList);
            }

            return;
        }

        /*
        * br_SetBracketPairBC
        *
        * Set the Bidi_Class of a bracket pair, based on the
        * direction determined by the N0 rule processing in
        * br_ResolveOnePair().
        *
        * The direction passed in will either be BIDI_R or BIDI_L.
        *
        * This setting is abstracted in a function here, rather than
        * simply being done inline in br_ResolveOnePair, because of
        * an edge case added to rule N0 as of UBA80. For UBA63 (and
        * UBA70), no special handling of combining marks following
        * either of the brackets is done. However, starting with UBA80,
        * there is an edge case fix-up done which echoes the processing
        * of rule W1. The text run needs to be scanned to find any
        * combining marks (orig_bc=NSM) following a bracket which has
        * its Bidi_Class changed by N0. Then those combining marks
        * can again be adjusted to match the Bidi_Class of the
        * bracket they apply to. This is an odd edge case, as combining
        * marks do not typically occur with brackets, but the UBA80
        * specification is now explicit about requiring this fix-up
        * to be done.
        */

        static void br_SetBracketPairBC(BIDIUNIT[] textChain, int buppopening,
            int buppclosing, int bupplast, BIDIPROP direction)
        {
            int bupp;
            textChain[buppopening].bc = direction;
            textChain[buppclosing].bc = direction;
            if (GetUBAVersion() >= (int)UBA_Version_Type.UBA80)
                /*
                 * Here is the tricky part.
                 *
                 * First scan from the opening bracket for any subsequent
                 * character whose *original* Bidi_Class was NSM, and set
                 * the current bc for it to direction also, to match the bracket.
                 * Break out of the loop at the first character with any other
                 * original Bidi_Class, so that this change only impacts
                 * actual combining mark sequences.
                 *
                 * 2020-03-27 note: This scanning for original combining marks
                 * must also scan past any intervening NOLEVEL characters,
                 * typically bc=BN characters removed earlier by rule X9.
                 * Such sequences may, for example involve a ZWJ or ZWNJ,
                 * or in bizarre edge cases involve other bc=BN characters
                 * such as ZWSP. The latter would be defective combining character
                 * sequences, but also need to be handled here.
                 *
                 * Then repeat the process for the matching closing bracket.
                 *
                 * The processing for the opening bracket is bounded to the
                 * right by the position of the matching closing bracket.
                 * The processing for the closing bracket is bounded to the
                 * right by the end of the text run.
                 */
            {
                for (bupp = buppopening + 1; bupp <= buppclosing - 1; bupp++)
                {
                    if (textChain[bupp].orig_bc == BIDIPROP.BIDI_NSM)
                    {
                        textChain[bupp].bc = direction;
                    }
                    else if (textChain[bupp].level != NOLEVEL)
                    {
                        break;
                    }
                }

                for (bupp = buppclosing + 1; bupp <= bupplast; bupp++)
                {
                    if (textChain[bupp].orig_bc == BIDIPROP.BIDI_NSM)
                    {
                        textChain[bupp].bc = direction;
                    }
                    else if (textChain[bupp].level != NOLEVEL)
                    {
                        break;
                    }
                }
            }
        }

        /*
         * br_ResolveOnePair
         *
         * Resolve the embedding levels of one pair of matched brackets.
         *
         * This determination is based on the embedding direction.
         * See BD3 in the UBA specification.
         *
         * If embedding level is even, embedding direction = L.
         * If embedding level is odd,  embedding direction = R.
         */

        static void br_ResolveOnePair(BIDI_RULE_CONTEXT brcp, int firstpos, int lastpos)
        {
            int bupp;
            BIDIPROP tempbc;

            var buppfirst = brcp.textStart;
            var bupplast = buppfirst + brcp.len - 1;
            var buppopening = buppfirst + firstpos;
            var buppclosing = buppfirst + lastpos;

            /*
             * First establish the embedding direction, based on the embedding
             * level passed in as a paramter with the context for this text chain.
             */
            var embeddingdirection = (brcp.level % 2 == 1) ? BIDIPROP.BIDI_R : BIDIPROP.BIDI_L;
            var oppositedirection = (embeddingdirection == BIDIPROP.BIDI_L) ? BIDIPROP.BIDI_R : BIDIPROP.BIDI_L;
            var strongtypefound = false;

            /*
             * Next check for a strong type (R or L) between firstpos and lastpos,
             * i.e., one between the matched brackets. If a strong type is found
             * which matches the embedding direction, then set the type of both
             * brackets to match the embedding direction, too.
             */
            if (firstpos < lastpos - 1)
            {
                for (bupp = buppopening + 1; bupp <= buppclosing - 1; bupp++)
                {
                    /*
                     * For the purposes of this direction checking, any EN or AN
                     * which have not been reset by the Weak rules count as
                     * BIDI_R. Set up a temporary bc variable based on mapping
                     * any EN or AN value to BIDI_R.
                     */
                    tempbc = (((brcp.textChain[bupp].bc) == BIDIPROP.BIDI_R) ||
                              (brcp.textChain[bupp].bc_numeric)) ? BIDIPROP.BIDI_R :
                        ((brcp.textChain[bupp].bc) == BIDIPROP.BIDI_L) ? BIDIPROP.BIDI_L : BIDIPROP.BIDI_None;
                    if (tempbc == embeddingdirection)
                    {
                        if (Trace(Trace7))
                        {
                            printf("Debug: Strong direction e between brackets\n");
                        }

                        /* N0 Step b, */
                        br_SetBracketPairBC(brcp.textChain, buppopening, buppclosing, bupplast, embeddingdirection);
                        return;
                    }
                    else if (tempbc == oppositedirection)
                    {
                        strongtypefound = true;
                    }
                }
            }

            /*
             * Next branch on whether we found a strong type opposite the embedding
             * direction between the brackets or not.
             */

            if (strongtypefound)
            {
                if (Trace(Trace7))
                {
                    printf("Debug: Strong direction o between brackets\n");
                }

                /*
                 * First attempt to resolve direction by checking the prior context for
                 * a strong type matching the opposite direction. N0 Step c1.
                 */
                if (((oppositedirection == BIDIPROP.BIDI_L) &&
                     (br_IsPriorContextL(brcp.textChain, buppfirst, buppopening, brcp.sot))) ||
                    ((oppositedirection == BIDIPROP.BIDI_R) &&
                     (br_IsPriorContextRANEN(brcp.textChain, buppfirst, buppopening, brcp.sot))))
                {
                    br_SetBracketPairBC(brcp.textChain, buppopening, buppclosing, bupplast, oppositedirection);
                }
                /*
                 * Next attempt to resolve direction by checking the following context for
                 * a strong type matching the opposite direction. Former N0 Step c2a. Removed.
                 */
#if NOTDEF
                else if (((oppositedirection == BIDIPROP.BIDI_L) &&
                          (br_IsFollowingContextL(brcp.textChain, bupplast, buppclosing, brcp.eot))) ||
                         ((oppositedirection == BIDIPROP.BIDI_R) &&
                          (br_IsFollowingContextRANEN(brcp.textChain, bupplast, buppclosing, brcp.eot))))
                {
                    br_SetBracketPairBC(brcp.textChain, buppopening, buppclosing, bupplast, oppositedirection);
                }
#endif
                else
                {
                    /*
                     * No strong type matching the oppositedirection was found either
                     * before or after these brackets in this text chain. Resolve the
                     * brackets based on the embedding direction. N0 Step c2.
                     */
                    br_SetBracketPairBC(brcp.textChain, buppopening, buppclosing, bupplast, embeddingdirection);
                }
            }
            else
            {
                /* 
                 * No strong type was found between the brackets. Leave
                 * the brackets with unresolved direction.
                 */
                if (Trace(Trace7))
                {
                    printf("Debug: No strong direction between brackets\n");
                }
            }
        }

        /*
         * br_ResolvePairEmbeddingLevels
         *
         * Scan through the pair list, resolving the embedding
         * levels of each pair of matched brackets.
         */

        static void br_ResolvePairEmbeddingLevels(BIDI_RULE_CONTEXT brcp)
        {
            var plp =
                /*
             * Scan through the pair list and resolve each pair in order.
             */
                pairList;
            while (plp != null)
            {
                /*
                 * Now for each pair, we have the first and last position
                 * of the substring in this isolating run sequence
                 * enclosed by those brackets (inclusive
                 * of the brackets). Resolve that individual pair.
                 */
                br_ResolveOnePair(brcp, plp.openingpos, plp.closingpos);
                plp = plp.next;
            }

            return;
        }

        /*
         * br_UBA_ResolvePairedBrackets
         *
         * This is the method for Rule N0. (New in UBA63)
         *
         * Resolve paired brackets for a single text chain.
         *
         * For each character in the text chain, examine its
         * Bidi_Class. For any character with the bpt value open or close,
         * scan its context seeking a matching paired bracket. If found,
         * resolve the type of both brackets to match the embedding
         * direction.
         *
         * For UBA63 (and unchanged in UBA70), the error handling for
         * a stack overflow was unspecified for this rule.
         *
         * Starting with UBA80, the exact stack size is specified (63),
         * and the specification declares that if a stack overflow
         * condition is encountered, the BD16 processing for this
         * particular isolating run ceases immediately. This condition
         * does not treated as a fatal error, however, so the rule
         * should not return an error code here, which would stop
         * all processing for *all* runs of the input string.
         *
         * For clarity in this reference implementation, this same
         * behavior is now also used when running UBA63 (or UBA70)
         * rules.
         */

        static int br_UBA_ResolvePairedBrackets(BIDI_RULE_CONTEXT brcp)
        {
            int bupp;
            int pos;
            int rc;

            /*
             * Initialize the bracket stack and the pair list.
             */

            br_InitBracketStack();
            pairList = null;

            var buppfirst = brcp.textStart;
            var bupplast = buppfirst + brcp.len - 1;

            /*
             * Process the text chain from first to last BIDIUNIT,
             * checking the Bidi_Paired_Bracket_Type of each character.
             *
             * BD16 examples used 1-based indexing, but this implementation
             * used 0-based indexing, for consistency with other processing
             * of text chains.
             *
             * UBA80 provided a clarification that the testing for bracket
             * pairs only applies to brackets whose current Bidi_Class is
             * still BIDI_ON at this point in the algorithm. This testing
             * is now done. It can affect outcomes for complicated mixes
             * of explicit embedding controls and bracket pairs, but does
             * not seem to have ever been tested in conformance test cases
             * for earlier implementations. In this bidiref implementation,
             * this check is not assumed to apply to UBA63 (and UBA70),
             * in order to maintain backward compatibility with the bidiref
             * implementation for those versions. By the UBA80 rules,
             * any bracket that has been forced to R or L already
             * by the resolution of explicit embedding (see X6) is simply
             * passed over for bracket pair matching.
             */
            var testONisNotRequired = GetUBAVersion() < (int)UBA_Version_Type.UBA80;
            for (bupp = buppfirst, pos = 0; bupp <= bupplast; bupp++, pos++)
            {
                if ((brcp.textChain[bupp].bpt != BPTPROP.BPT_None) &&
                    (brcp.textChain[bupp].bc == BIDIPROP.BIDI_ON) || testONisNotRequired)
                {
                    if (brcp.textChain[bupp].bpt == BPTPROP.BPT_O)
                    {
                        /* Process an opening bracket. Push on the stack. */
                        BRACKETSTACKELEMENT bracketData = new BRACKETSTACKELEMENT();
                        bracketData.bracket = brcp.textChain[bupp].bpb;
                        bracketData.pos = pos;
                        rc = br_PushBracketStack(bracketData);
                        if (rc == 0)
                            /* 
                             * Stack overflow. Stop processing this text chain. 
                             * Return 0 to indicate no change was made.
                             */
                        {
                            return (0);
                        }
                    }
                    else
                    {
                        /* 
                         * Process a closing bracket.
                         * br_SeekOpeningBracketMatch handles the search
                         * through the stack and the pairslist building if
                         * any match is found.
                         */
                        rc = br_SeekOpeningBracketMatch(brcp.textChain[bupp].c, pos);
                        if ((rc == 1) && (Trace(Trace7)))
                        {
                            printf("Matched bracket\n");
                        }
                    }
                }
            }

            if (pairList == null)
            {
                /* 
                 * The pairList pointer will still be null if no paired brackets
                 * were found. In this case, no further processing is
                 * necessary. Just return 0 (no change to set the dirtyBit).
                 */
                return (0);
            }

            /* 
             * Do further processing on the calculated pair list.
             *
             * First sort the pair list.
             */

            br_SortPairList();

            /*
             * Next scan through the pair list, resolving the
             * embedding levels of each identified pair of brackets.
             */

            br_ResolvePairEmbeddingLevels(brcp);

            /* Clean up the pair list before return. */

            br_DropPairList();

            return (1);
        }

        /*
         * br_UBA_ResolveNeutralsByContext
         *
         * This is the method for Rule N1.
         *
         * Resolve neutrals by context for a single text chain.
         *
         * For each character in the text chain, examine its
         * Bidi_Class. For any character of neutral type, examine its
         * context.
         *
         * L N L -. L L L
         * R N R -. R R R [note that AN and EN count as R for this rule]
         *
         * Here "N" stands for "any sequence of neutrals", so the neutral
         * does not have to be immediately adjacent to a strong type
         * to be resolved this way.
         */

        static int br_UBA_ResolveNeutralsByContext(BIDI_RULE_CONTEXT brcp)
        {
            int bupp;
            int dirtyBit = 0;

            var buppfirst = brcp.textStart;
            var bupplast = buppfirst + brcp.len - 1;

            /*
             * Process the text chain from first to last BIDIUNIT,
             * checking the Bidi_Class of each character.
             */
            for (bupp = buppfirst; bupp <= bupplast; bupp++)
            {
                if (br_IsNeutralType(brcp.textChain[bupp].bc))
                {
                    /* Check to see if N is in context L N L */
                    if (br_IsPriorContextL(brcp.textChain, buppfirst, bupp, brcp.sot) &&
                        br_IsFollowingContextL(brcp.textChain, bupplast, bupp, brcp.eot))
                    {
                        brcp.textChain[bupp].bc = BIDIPROP.BIDI_L;
                        dirtyBit = 1;
                    }
                    /* Check to see if N is in context R N R */
                    else if (br_IsPriorContextRANEN(brcp.textChain, buppfirst, bupp, brcp.sot) &&
                             br_IsFollowingContextRANEN(brcp.textChain, bupplast, bupp, brcp.eot))
                    {
                        brcp.textChain[bupp].bc = BIDIPROP.BIDI_R;
                        dirtyBit = 1;
                    }
                }
            }

            return (dirtyBit);
        }

        /*
         * br_UBA_ResolveNeutralsByLevel
         *
         * This is the method for Rule N2.
         *
         * Resolve neutrals by level for a single text chain.
         *
         * For each character in the text chain, examine its
         * Bidi_Class. For any character of neutral type, examine its
         * embedding level and resolve accordingly.
         *
         * N -. e
         * where e = L for an even level, R for an odd level
         */

        static int br_UBA_ResolveNeutralsByLevel(BIDI_RULE_CONTEXT brcp)
        {
            int bupp;
            int dirtyBit = 0;

            var buppfirst = brcp.textStart;
            var bupplast = buppfirst + brcp.len - 1;

            /*
             * Process the text chain from first to last BIDIUNIT,
             * checking the Bidi_Class of each character.
             */
            for (bupp = buppfirst; bupp <= bupplast; bupp++)
            {
                if (br_IsNeutralType(brcp.textChain[bupp].bc))
                {
                    /* Check to see if N is in even embedding level */
                    if (brcp.textChain[bupp].level % 2 == 0)
                    {
                        brcp.textChain[bupp].bc = BIDIPROP.BIDI_L;
                    }
                    else
                    {
                        brcp.textChain[bupp].bc = BIDIPROP.BIDI_R;
                    }

                    dirtyBit = 1;
                }
            }

            return (dirtyBit);
        }

        /*******************************************************************/

        /*
         * SECTION: Resolving Implicit Levels: Rules I1-I2
         *
         * This section runs the resolving implicit levels rules of the UBA.
         *
         * Each pass processes the text, and does not need to work on a
         * run-by-run basis, because it is simply resolving all the implicit
         * levels in all runs.
         */

        /*
         * br_UBA_ResolveImplicitLevels
         *
         * This function runs Rules I1 and I2 together.
         */

        static int br_UBA_ResolveImplicitLevels(UBACONTEXT ctxt)
        {
            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA_ResolveImplicitLevels [I1, I2]\n");
            }

            ctxt.dirtyBit = 0;
            for (int ix = 0; ix < ctxt.textLen; ix++)
            {
                var bdu = ctxt.textUnits[ix];
                if (bdu.level != NOLEVEL)
                {
                    var oddlevel = (bdu.level % 2 == 1);
                    if (oddlevel)
                    {
                        if ((bdu.bc == BIDIPROP.BIDI_L) || (bdu.bc_numeric))
                        {
                            bdu.level += 1;
                            ctxt.dirtyBit = 1;
                        }
                    }
                    else
                    {
                        if (bdu.bc == BIDIPROP.BIDI_R)
                        {
                            bdu.level += 1;
                            ctxt.dirtyBit = 1;
                        }
                        else if (bdu.bc_numeric)
                        {
                            bdu.level += 2;
                            ctxt.dirtyBit = 1;
                        }
                    }
                }

                /* Advance to the next character to process. */
            }

            ctxt.state = ALGORITHM_STATE.State_I2Done;
            return (1);
        }

        /*******************************************************************/

        /*
         * SECTION: Reordering Resolved Levels: Rules L1-L4
         *
         * This section runs the reordering resolved levels rules of the UBA.
         *
         * Each pass processes the text, and does not need to work on a
         * run-by-run basis.
         *
         * Note: This reference implementation assumes that the text to
         * reorder consists of a single line, because it is running the
         * algorithm in the absence of any actual rendering context with
         * access to glyphs, knowledge of line length, and access to a
         * line breaking algorithm.
         */

        /*
         * br_UBA_ResetWhitespaceLevels
         *
         * This function runs Rule L1.
         *
         * The strategy here for Rule L1 is to scan forward through
         * the text searching for segment separators or paragraph
         * separators. If a segment separator or paragraph
         * separator is found, it is reset to the paragraph embedding
         * level. Then scan backwards from the separator to
         * find any contiguous stretch of whitespace characters
         * and reset any which are found to the paragraph embedding
         * level, as well. When we reach the *last* character in the
         * text (which will also constitute, by definition, the last
         * character in the line being processed here), check if it
         * is whitespace. If so, reset it to the paragraph embedding
         * level. Then scan backwards to find any contiguous stretch
         * of whitespace characters and reset those as well.
         *
         * These checks for whitespace are done with the *original*
         * Bidi_Class values for characters, not the resolved values.
         *
         * As for many rules, this rule simply ignores any character
         * whose level has been set to NOLEVEL, which is the way
         * this reference algorithm "deletes" boundary neutrals and
         * embedding and override controls from the text.
         */

        static int br_UBA_ResetWhitespaceLevels(UBACONTEXT ctxt)
        {
            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA_ResetWhitespaceLevels [L1]\n");
            }

            ctxt.dirtyBit = 0;
            for (int ix = 0; ix < ctxt.textLen; ix++)
            {
                var bdu = ctxt.textUnits[ix];
                BIDIUNIT bdu2;
                if ((bdu.orig_bc == BIDIPROP.BIDI_S) || (bdu.orig_bc == BIDIPROP.BIDI_B))
                {
                    if (bdu.level != ctxt.paragraphEmbeddingLevel)
                    {
                        /* Only set the dirtyBit if the level has actually changed. */
                        bdu.level = ctxt.paragraphEmbeddingLevel;
                        ctxt.dirtyBit = 1;
                    }

                    /* scan back looking for contiguous whitespace */
                    if (ix > 0)
                    {
                        int ix2 = ix - 1;
                        while (ix2 >= 0)
                        {
                            bdu2 = ctxt.textUnits[ix2];
                            if (bdu2.orig_bc == BIDIPROP.BIDI_WS)
                            {
                                bdu2.level = ctxt.paragraphEmbeddingLevel;
                            }
                            else if (bdu2.level == NOLEVEL)
                            {
                                /* skip over *deleted* controls */
                            }
                            else
                            {
                                /* break out of loop for any other character */
                                break;
                            }

                            ix2--;
                        }
                    }
                }

                /* If at end of string, scan back checking for terminal whitespace */
                if (ix == ctxt.textLen - 1)
                {
                    int ix2 = ix;
                    while (ix2 >= 0)
                    {
                        bdu2 = ctxt.textUnits[ix2];
                        if (bdu2.orig_bc == BIDIPROP.BIDI_WS)
                        {
                            bdu2.level = ctxt.paragraphEmbeddingLevel;
                            ctxt.dirtyBit = 1;
                        }
                        else if (bdu2.level == NOLEVEL)
                        {
                            /* skip over *deleted* controls */
                        }
                        else
                        {
                            /* break out of loop for any other character */
                            break;
                        }

                        ix2--;
                    }
                }

                /* Advance to the next character to process. */
            }

            ctxt.state = ALGORITHM_STATE.State_L1Done;
            return (1);
        }

        /*
         * br_UBA63_ResetWhitespaceLevels
         *
         * This function runs Rule L1.
         *
         * As for br_UBA_ResetWhitespaceLevels, except that for
         * UBA63, the scanback context which is reset to the
         * paragraph embedding level includes all contiguous
         * sequences of whitespace *and* any isolate format
         * controls. The test for the Bidi_Class of the isolate
         * format controls could be done on either the current
         * or original Bidi_Class, as the preceding rules do not
         * change the Bidi_Class of isolate format controls, but
         * all tests are done here on the original Bidi_Class
         * values in the BIDIUNIT, for consistency.
         */

        static int br_UBA63_ResetWhitespaceLevels(UBACONTEXT ctxt)
        {
            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA63_ResetWhitespaceLevels [L1]\n");
            }

            ctxt.dirtyBit = 0;
            for (int ix = 0; ix < ctxt.textLen; ix++)
            {
                var bdu = ctxt.textUnits[ix];
                BIDIUNIT bdu2;
                if ((bdu.orig_bc == BIDIPROP.BIDI_S) || (bdu.orig_bc == BIDIPROP.BIDI_B))
                {
                    if (bdu.level != ctxt.paragraphEmbeddingLevel)
                    {
                        /* Only set the dirtyBit if the level has actually changed. */
                        bdu.level = ctxt.paragraphEmbeddingLevel;
                        ctxt.dirtyBit = 1;
                    }

                    /* scan back looking for contiguous whitespace */
                    if (ix > 0)
                    {
                        int ix2 = ix - 1;
                        while (ix2 >= 0)
                        {
                            bdu2 = ctxt.textUnits[ix2];
                            if (bdu2.orig_bc == BIDIPROP.BIDI_WS)
                            {
                                bdu2.level = ctxt.paragraphEmbeddingLevel;
                            }
                            else if (br_IsIsolateControl(bdu2.orig_bc))
                            {
                                bdu2.level = ctxt.paragraphEmbeddingLevel;
                            }
                            else if (bdu2.level == NOLEVEL)
                            {
                                /* skip over *deleted* controls */
                            }
                            else
                            {
                                /* break out of loop for any other character */
                                break;
                            }

                            ix2--;
                        }
                    }
                }

                /* If at end of string, scan back checking for terminal whitespace */
                if (ix == ctxt.textLen - 1)
                {
                    int ix2 = ix;
                    while (ix2 >= 0)
                    {
                        bdu2 = ctxt.textUnits[ix2];
                        if (bdu2.orig_bc == BIDIPROP.BIDI_WS)
                        {
                            bdu2.level = ctxt.paragraphEmbeddingLevel;
                            ctxt.dirtyBit = 1;
                        }
                        else if (br_IsIsolateControl(bdu2.orig_bc))
                        {
                            bdu2.level = ctxt.paragraphEmbeddingLevel;
                            ctxt.dirtyBit = 1;
                        }
                        else if (bdu2.level == NOLEVEL)
                        {
                            /* skip over *deleted* controls */
                        }
                        else
                        {
                            /* break out of loop for any other character */
                            break;
                        }

                        ix2--;
                    }
                }

                /* Advance to the next character to process. */
            }

            ctxt.state = ALGORITHM_STATE.State_L1Done;
            return (1);
        }

        /*
         * br_ReverseRange
         *
         * For a specified range firstpos to lastpos, invert the position
         * values stored in the order field in the array of BIDIUNITs.
         *
         * Rather than reordering in place, which requires fancier manipulation
         * of indices, just use a spare order array in the BIDIUNIT vector.
         * Write the values into the range in reverse order, then copy them
         * back into the main order array in the reversed order.
         * This is a simple and easy to understand approach.
         */

        static void br_ReverseRange(UBACONTEXT ctxt, int firstpos, int lastpos)
        {
            int ix;
            int newpos;

            /*
             * First copy the range from the order field into the order2 field
             * in reversed order.
             */
            for (ix = firstpos, newpos = lastpos; ix <= lastpos; ix++, newpos--)
            {
                ctxt.textUnits[ix].order2 = ctxt.textUnits[newpos].order;
            }

            /*
             * Then copy the order2 values back into the order field.
             */
            for (ix = firstpos; ix <= lastpos; ix++)
            {
                ctxt.textUnits[ix].order = ctxt.textUnits[ix].order2;
            }
        }

        /*
         * br_UBA_ReverseLevels
         *
         * This function runs Rule L2.
         *
         * Find the highest level among the resolved levels.
         * Then from that highest level down to the lowest odd
         * level, reverse any contiguous runs at that level or higher.
         */

        static int br_UBA_ReverseLevels(UBACONTEXT ctxt)
        {
            int ix;
            int level;
            int firstpos;
            int lastpos;

            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA_ReverseLevels [L2]\n");
            }

            ctxt.dirtyBit = 0;

            /*
             * First scan the text to determine the highest level and
             * the lowest odd level.
             */
            var highestlevel = 0;
            var lowestoddlevel = maximum_depth + 1;
            var nolevels = true;

            foreach (var bdu in ctxt.textUnits)
            {
                if (bdu.level != NOLEVEL)
                {
                    /* Found something other than NOLEVEL. */
                    nolevels = false;
                    if (bdu.level > highestlevel)
                    {
                        highestlevel = bdu.level;
                    }

                    if ((bdu.level % 2 == 1) && (bdu.level < lowestoddlevel))
                    {
                        lowestoddlevel = bdu.level;
                    }
                }
            }

            if (Trace(Trace8))
            {
                if (nolevels)
                {
                    printf("Debug: No levels found.\n");
                }
                else if (lowestoddlevel == maximum_depth + 1)
                {
                    printf("Debug: highestlevel={0}, lowestoddlevel=NONE\n", highestlevel);
                }
                else
                {
                    printf("Debug: highestlevel={0}, lowestoddlevel={1}\n", highestlevel,
                        lowestoddlevel);
                }
            }

            /* If there are no levels set, don't bother with reordering anything. */

            if (nolevels)
            {
                ctxt.state = ALGORITHM_STATE.State_L2Done;
                return (1);
            }

            /* 
             * Next reverse contiguous runs at each level (or higher),
             * starting with the highest level and decrementing to
             * the lowest odd level.
             */
            for (level = highestlevel; level >= lowestoddlevel; level--)
            {
                if (Trace(Trace8))
                {
                    printf("Level {0}:", level);
                }

                /*
                 * For each relevant level, scan the text to find
                 * contiguous ranges at that level (or higher).
                 * A *significant* contiguous range is figured as
                 * one that contains at least two characters with
                 * an explicit level. Don't bother reversing ranges
                 * which contain only one character with an explicit
                 * level and then one or more trailing NOLEVEL "deleted"
                 * characters.
                 */
                ix = 0;
                var inrange = false;
                var significantrange = false;
                firstpos = -1;
                lastpos = -1;
                while (ix < ctxt.textLen)
                {
                    var bdu = ctxt.textUnits[ix];
                    if (bdu.level >= level)
                    {
                        if (!inrange)
                        {
                            inrange = true;
                            firstpos = ix;
                        }
                        else /* Hit a second explicit level character. */
                        {
                            significantrange = true;
                            lastpos = ix;
                        }
                    }
                    else if (bdu.level == NOLEVEL)
                    {
                        /* don't break ranges for "deleted" controls */
                        if (inrange)
                        {
                            lastpos = ix;
                        }
                    }
                    else
                    {
                        /* End of a range. Reset the range flag and reverse the range. */
                        inrange = false;
                        if ((lastpos > firstpos) && significantrange)
                        {
                            if (Trace(Trace8))
                            {
                                printf(" reversing {0} to {1} ", firstpos, lastpos);
                            }

                            br_ReverseRange(ctxt, firstpos, lastpos);
                            ctxt.dirtyBit = 1;
                        }

                        firstpos = -1;
                        lastpos = -1;
                    }

                    /* Advance to the next character to process. */

                    ix++;
                }

                /* 
                 * If we reached the end of the input while the inrange flag is
                 * set, then we need to do a reversal before exiting this level.
                 */
                if (inrange && (lastpos > firstpos) && significantrange)
                {
                    if (Trace(Trace8))
                    {
                        printf(" reversing {0} to {1} ", firstpos, lastpos);
                    }

                    br_ReverseRange(ctxt, firstpos, lastpos);
                    ctxt.dirtyBit = 1;
                }

                if (Trace(Trace8))
                {
                    printf("\n");
                }
            }

            ctxt.state = ALGORITHM_STATE.State_L2Done;
            return (1);
        }

        /*******************************************************************/

        /*
         * SECTION: Main invocation of UBA.
         *
         * Running the algorithm is broken up into sequential steps which
         * mirror the rule sections in UAX #9.
         */

        /*
         * br_UBA_RuleDispatch
         *
         * The rule dispatcher abstracts the version-specific extraction
         * of text chains from the UBACONTEXT and dispatches text chains,
         * one at a time, to the appropriate rule method.
         *
         * This enables better encapsulation of the distinction between
         * UBA62 rules, which apply to lists of level runs, and
         * UBA63 rules, which apply to isolating run sequences (which
         * in turn consist of sets of level runs).
         *
         * The logic for the rule itself, which is applied on a per text chain
         * basis, can be abstracted to the ruleMethod, which often does
         * not need to be made version-specific.
         *
         * The return value from the ruleMethod is used to set the
         * dirtyBit in the UBACONTEXT.
         *
         * br_UBA_RuleDispatch is only used for rules which can be
         * analyzed as applying independently to a single text chain, and
         * which are not version-specific in their application to the runs.
         * (Currently the W rules and the N rules.)
         *
         * br_UBA_RuleDispatch also handles printing out an error message,
         * if any, and printing the debug display diagnostics, to cut
         * down on repetitive code in the main UBA call routines.
         */

        static int br_UBA_RuleDispatch(UBACONTEXT ctxt, BIDI_RULE_TYPE rule)
        {
            BIDI_RULE_CONTEXT brc = new BIDI_RULE_CONTEXT();

            var rtp = bidi_rules[(int)rule];

            if (Trace(Trace1))
            {
                printf($"Trace: Entering br_UBA_{rtp.ruleLabel} [{rtp.ruleNumber}]\n");
            }

            ctxt.dirtyBit = 0;

            var rc = 1;

            if (GetUBAVersion() == (int)UBA_Version_Type.UBA62)
            {
                BIDIRUN brp = ctxt.theRuns;

                while (brp != null)
                {
                    /* Initialize the BIDI_RULE_CONTEXT. */
                    brc.textChain = brp.textChain;
                    brc.textStart = brp.textStart;
                    brc.len = brp.len;
                    brc.level = brp.level;
                    brc.sot = brp.sor;
                    brc.eot = brp.eor;

                    /*
                     * Invoke the appropriate rule method for this rule,
                     * applying it to the text chain for this run.
                     */

                    rc = rtp.ruleMethod(brc);
                    if (rc == 1)
                    {
                        ctxt.dirtyBit = 1;
                    }
                    else if (rc == -1)
                    {
                        break;
                    }

                    /* Advance to the next run */
                    brp = brp.next;
                }
            }
            else
            {
                ISOLATING_RUN_SEQUENCE irp = ctxt.theSequences;

                while (irp != null)
                {
                    /* Initialize the BIDI_RULE_CONTEXT. */
                    brc.textChain = irp.textChain;
                    brc.textStart = irp.textStart;
                    brc.len = irp.len;
                    brc.level = irp.level;
                    brc.sot = irp.sos;
                    brc.eot = irp.eos;

                    /*
                     * Invoke the appropriate rule method for this rule,
                     * applying it to the text chain for this run.
                     */

                    rc = rtp.ruleMethod(brc);
                    if (rc == 1)
                    {
                        ctxt.dirtyBit = 1;
                    }
                    else if (rc == -1)
                    {
                        break;
                    }

                    /* Advance to the next run */
                    irp = irp.next;
                }
            }

            if (rc == -1)
            {
                br_ErrPrint($"Error in: {rtp.ruleError}.\n");
            }

            switch (rule)
            {
                case BIDI_RULE_TYPE.UBA_RULE_W1:
                    ctxt.state = ALGORITHM_STATE.State_W1Done;
                    break;
                case BIDI_RULE_TYPE.UBA_RULE_W2:
                    ctxt.state = ALGORITHM_STATE.State_W2Done;
                    break;
                case BIDI_RULE_TYPE.UBA_RULE_W3:
                    ctxt.state = ALGORITHM_STATE.State_W3Done;
                    break;
                case BIDI_RULE_TYPE.UBA_RULE_W4:
                    ctxt.state = ALGORITHM_STATE.State_W4Done;
                    break;
                case BIDI_RULE_TYPE.UBA_RULE_W5:
                    ctxt.state = ALGORITHM_STATE.State_W5Done;
                    break;
                case BIDI_RULE_TYPE.UBA_RULE_W6:
                    ctxt.state = ALGORITHM_STATE.State_W6Done;
                    break;
                case BIDI_RULE_TYPE.UBA_RULE_W7:
                    ctxt.state = ALGORITHM_STATE.State_W7Done;
                    break;
                case BIDI_RULE_TYPE.UBA_RULE_N0:
                    ctxt.state = ALGORITHM_STATE.State_N0Done;
                    break;
                case BIDI_RULE_TYPE.UBA_RULE_N1:
                    ctxt.state = ALGORITHM_STATE.State_N1Done;
                    break;
                case BIDI_RULE_TYPE.UBA_RULE_N2:
                    ctxt.state = ALGORITHM_STATE.State_N2Done;
                    break;
            }

            br_DisplayState(ctxt);

            return ((rc == -1) ? 0 : 1);
        }

        /*
         * br_UBA_WRulesDispatch
         *
         * Dispatch rules W1-W7.
         *
         * The dispatch and order of application is the same for UBA62
         * and for UBA63.
         */

        static int br_UBA_WRulesDispatch(UBACONTEXT ctxt)
        {
            var rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_W1);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_W2);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_W3);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_W4);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_W5);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_W6);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_W7);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            return (BR_TESTOK);
        }

        /*
         * br_UBA_62
         *
         * Run the Version 6.2 UBA algorithm on the text (or Bidi_Class vector) stored in
         * the ctxt pointer.
         */

        static int br_UBA_62(UBACONTEXT ctxt)
        {
            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA_62\n");
            }

            br_DisplayState(ctxt);

            var rc = br_UBA_ParagraphEmbeddingLevel(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in: processing paragraph embedding level.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA_ExplicitEmbeddingLevels(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in: processing explicit embedding levels.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA_DeleteFormatCharacters(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in: processing deletion of format characters.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA_IdentifyRuns(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in identifying runs.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            /*
             * Next we start a stretch of rules which apply systematically
             * to the list of level runs (for UBA62).
             *
             * For compactness and abstraction, these are dispatched
             * through a central rule handler which handles rules of
             * this generic type.
             */

            rc = br_UBA_WRulesDispatch(ctxt);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_N1);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_N2);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_ResolveImplicitLevels(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in resolving implicit levels.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA_ResetWhitespaceLevels(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in resetting whitespace levels.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA_ReverseLevels(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in reversing levels.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            return (BR_TESTOK);
        }

        /*
         * br_UBA_63
         *
         * Run the Version 6.3 UBA algorithm on the text (or Bidi_Class vector) stored in
         * the ctxt pointer.
         */

        static int br_UBA_63(UBACONTEXT ctxt)
        {
            if (Trace(Trace1))
            {
                printf("Trace: Entering br_UBA_63\n");
            }

            br_DisplayState(ctxt);

            var rc = br_UBA63_ParagraphEmbeddingLevel(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in processing paragraph embedding level.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA63_ExplicitEmbeddingLevels(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in processing explicit embedding levels.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA_DeleteFormatCharacters(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in processing deletion of format characters.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA_IdentifyRuns(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in identifying runs.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA_IdentifyIsolatingRunSequences(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in identifying isolating run sequences.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            /*
             * Next we start a stretch of rules which apply systematically
             * to the list of isolating run sequences (for UBA63).
             *
             * For compactness and abstraction, these are dispatched
             * through a central rule handler which handles rules of
             * this generic type.
             */

            rc = br_UBA_WRulesDispatch(ctxt);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_N0);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_N1);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_RuleDispatch(ctxt, BIDI_RULE_TYPE.UBA_RULE_N2);
            if (rc != 1)
            {
                return (BR_TESTERR);
            }

            rc = br_UBA_ResolveImplicitLevels(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in resolving implicit levels.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA63_ResetWhitespaceLevels(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in resetting whitespace levels.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            rc = br_UBA_ReverseLevels(ctxt);
            if (rc != 1)
            {
                br_ErrPrint("Error in reversing levels.\n");
                return (BR_TESTERR);
            }

            br_DisplayState(ctxt);

            return (BR_TESTOK);
        }

        /*
         * br_UBA
         *
         * Run the UBA algorithm on the text stored in
         * the ctxt pointer.
         *
         * Check which version to run. Set any required
         * global parameters, and dispatch appropriately.
         */

        static int br_UBA(UBACONTEXT ctxt)
        {
            int rc;

            if (GetUBAVersion() == (int)UBA_Version_Type.UBA62)
            {
                maximum_depth = MAX_DEPTH_62;
                rc = br_UBA_62(ctxt);
            }
            else
            {
                maximum_depth = MAX_DEPTH_63;
                rc = br_UBA_63(ctxt);
            }

            return (rc);
        }

        /*******************************************************************/

        /*
         * br_Check
         *
         * Check the result of running the UBA algorithm
         * against criteria for what the expected outcome
         * is supposed to be.
         *
         * The expected results for the levels and the order
         * are simply stored as strings hung off the UBACONTEXT,
         * parsed from the test case data. So when we hit
         * br_Check, the finishing of the checking involves
         * doing the parsing of that expected results data.
         * Conceivably, there may be errors in that data, including
         * syntax errors, so it has to be checked at this step
         * during parsing, to provide meaningful results.
         */

        static int br_Check(UBACONTEXT ctxt)
        {
            // BIDIUNITPTR bdu;
            StringBuilder localbuf2 = new StringBuilder();

            if (Trace(Trace1))
            {
                printf("\nTrace: Entering br_Check\n");
            }

            /* 
             * Start by checking that the calculated resolved paragraph
             * embedding level matches the expected value provided
             * with the test case data.
             *
             * This step is omitted for FORMAT_B, because BidiTest.txt does
             * not supply the expected resolved paragraph embedding level.
             */

            if (GetFileFormat() == FORMAT_A)
            {
                if (ctxt.paragraphEmbeddingLevel != ctxt.expEmbeddingLevel)
                {
                    if (Trace(Trace0))
                    {
                        printf("Mismatch in expected paragraph embedding level\n");
                    }

                    return (BR_TESTERR);
                }
            }

            /*
             * The tests for matching expected levels and order are more
             * complicated to express. Display the context again.
             * When the algorithm is State_Complete, this will also add
             * display of the expected levels and the expected order,
             * parsed from the test case.
             */

            br_DisplayState(ctxt);

            foreach (var bdu in ctxt.textUnits)
            {
                if (bdu.level != bdu.expLevel)
                {
                    if (Trace(Trace0))
                    {
                        printf("Level does not match expected level in test case\n");
                    }

                    return (BR_TESTERR);
                }
            }

            /*
             * For order, just compare the expOrder string parsed from
             * the test case, with an order string constructed from the
             * order fields in theText. This is the same as the string
             * constructed for display, which seems to be matching the
             * test case values o.k.
             *
             * TBD: Figure out how to compare expected results when there
             * are NOLEVEL values in the text to be reordered. This may
             * require revisiting the reorder function first. As a
             * first approximation to the expected order results, just
             * omit printing any resolved order for an element with
             * resolved level of NOLEVEL.
             *
             * TBD: Consider whether a more elaborate testing of expected
             * order makes any sense to bother with. In that case, it
             * might make sense to do the parsing of the expected order
             * when the test data is first attached, rather than doing
             * this handling after the fact.
             */

            if (ctxt.expOrder == null)
            {
                /* 
                 * If no expected order has been attached, there is
                 * no point in continuing. Just return PASS.
                 */
                return (BR_TESTOK);
            }

            var ix = 0;
            var started = false;
            /* Null out the localbuf2 accumulator */
            foreach (var bdu in ctxt.textUnits)
            {
                /* Skip any "deleted" elements */
                if (bdu.order != -1)
                {
                    if (!started)
                    {
                        /* Don't add an initial space to the first element. */
                        started = true;
                    }
                    else
                    {
                        localbuf2.Append(' ');
                    }

                    localbuf2.Append(bdu.order);
                }
            }

            /* 
             * The order values have all been appended.
             * Now compare against the expected value.
             */
            if (strcmp(ctxt.expOrder, localbuf2.ToString()) != 0)
            {
                if (Trace(Trace0))
                {
                    printf("\nOrder [{0}] does not match expected order [{1}] in test case\n",
                        localbuf2, ctxt.expOrder);
                }

                return (BR_TESTERR);
            }

            return (BR_TESTOK);
        }


        /*
         * brtest.c
         *
         * Module to run a single test case, consisting of
         * the string input to submit to the UBA
         * processing and the expected output of the UBA processing.
         *
         * This module defines the public API for running a test.
         *
         * Exports:
         *  br_ProcessOneTestCase
         *  br_QueryOneTestCase
         */

        /***********************************************************/

        /*
         * SECTION: Allocation of UBACONTEXT
         */

        /*
         * textBuf
         *
         * Static allocation of a text vector of BIDIUNIT.
         * The static allocation of this buffer allows for its
         * reuse without repeated allocations and deallocations,
         * when running long suites of test cases.
         */
        static BIDIUNIT[] b_textBuf = new BIDIUNIT[BR_MAXINPUTLEN];


        /*
         * br_ConstructBidiUnitVector()
         *
         * Initialize the text values from some source.
         * Look up GC and BC values for each character,
         * and initialize all the levels to zero.
         */

        static BIDIUNIT[] br_ConstructBidiUnitVector(int len, U_Int_32[] text,
            int fileFormat)
        {
            BIDIUNIT[] bp = new BIDIUNIT[len];
            for (int i = 0; i < len; i++)
            {
                BIDIUNIT tbp = new BIDIUNIT();
                bp[i] = tbp;
                var tc = text[i];
                if (fileFormat == FORMAT_A)
                {
                    /*
                     * For FORMAT_A, the text vector actually contains
                     * code points. Look up 
                     * Bidi_Class and initialize on that basis.
                     */
                    tbp.c = tc;
                    tbp.bc = br_GetBC(tc);
                    tbp.bpb = br_GetBPB(tc);
                    tbp.bpt = br_GetBPT(tc);
                }
                else
                {
                    /*
                     * For FORMAT_B, the text vector contains
                     * only Bidi_Class values. Copy those over into
                     * the bc and orig_bc fields. Initialize c to a U+FFFF
                     * noncharacter value, so it will be clear what
                     * is going on.
                     */
                    tbp.c = 0xFFFF;
                    tbp.bc = (BIDIPROP)tc; // TODO Check it
                    tbp.bpb = BPB_None;
                    tbp.bpt = BPTPROP.BPT_None;
                }

                /*
                 * Save the original Bidi_Class value.
                 */
                tbp.orig_bc = tbp.bc;
                /*
                 * Set all accelerator flags.
                 */
                tbp.bc_numeric = false;
                tbp.bc_isoinit = false;
                if ((tbp.bc == BIDIPROP.BIDI_AN) || (tbp.bc == BIDIPROP.BIDI_EN))
                {
                    tbp.bc_numeric = true;
                }
                else if ((tbp.bc == BIDIPROP.BIDI_LRI) || (tbp.bc == BIDIPROP.BIDI_RLI) || (tbp.bc == BIDIPROP.BIDI_FSI))
                {
                    tbp.bc_isoinit = true;
                }

                /*
                 * Initialize all the levels to zero.
                 */
                tbp.level = 0;
                tbp.expLevel = 0;
                /*
                 * Initialize the order fields based on index.
                 */
                tbp.order = i;
                tbp.order2 = i;
            }

            return (bp);
        }

        /*
         * br_ConstructContext
         *
         * Allocate a UBACONTEXT and initialize it with input
         * values. Set calculated and expected results values to
         * defaults.
         */

        static UBACONTEXT br_ConstructContext(int len, U_Int_32[] text,
            Paragraph_Direction paraDirection)
        {
            UBACONTEXT p = new UBACONTEXT();
            /*
             * The way the BIDIUNIT vector is constructed will
             * depend on the input file format.
             */
            var fileFormat = GetFileFormat();

            var bp = br_ConstructBidiUnitVector(len, text, fileFormat);

            p.state = ALGORITHM_STATE.State_Initialized;
            p.testId = 0;
            p.dirtyBit = 0;
            /*
             * The enumerated Paragraph_Direction. 
             * Dir_Auto will be resolved to either LTR or RTL, depending
             * on the first strong type.
             */
            p.paragraphDirection = paraDirection;
            p.paragraphEmbeddingLevel = 0;
            p.textLen = len;
            p.textUnits = bp;
            p.theRuns = null;
            p.lastRun = null;
            p.theSequences = null;
            p.lastSequence = null;
            p.expEmbeddingLevel = -1; /* default to illegal value */
            p.expOrder = null;
            return (p);
        }

        /*
         * br_AttachExpectedResults
         *
         * Attach the expected results parsed from a test case to
         * an existing UBACONTEXT. This can be used by br_Check
         * to check whether the processing of a test case should be
         * judged to pass or fail.
         *
         * If any allocations fail here, br_DropContext will be
         * called after return, so just let it do any cleanup
         * needed.
         */

        static int br_AttachExpectedResults(UBACONTEXT ctxt,
            int expEmbeddingLevel, char[] expLevels, char[] expOrder)
        {
            // char* tmp;
            int rc;
            int level = 0;
            // BIDIUNITPTR bdu;
            // BIDIUNITPTR endOfText;
            char[] localbuf = new char[BUFFERLEN];

            /* 
             * Parse the expected levels and store in
             * the expLevel field in theText.
             *
             * First check that the number of expected levels matches
             * the text length.
             */
            var sp = 0;
            var len = 0;
            while (expLevels[sp] != '\0')
            {
                sp = copySubField(localbuf, expLevels, sp);
                len++;
            }

            if (len != ctxt.textLen)
            {
                br_ErrPrint("Expected levels in test case input data do not match text length.\n");
                return (BR_INITERR);
            }

            /* Next commit to evaluating and storing the expected levels. */

            // bdu = ctxt.theText;
            // endOfText = ctxt.theText + ctxt.textLen;
            sp = 0;
            foreach (var bdu in ctxt.textUnits)
            {
                sp = copySubField(localbuf, expLevels, sp);
                /*
                 * Note that an expected level associated with a deleted element
                 * in the input (BN, bidi format controls) is represented with an "x".
                 * Check for those values and interpret as NOLEVEL, before attempting to
                 * evaluate the parsed value as an integral numeric value for a level.
                 */
                if (strcmp(localbuf, "x") == 0)
                {
                    bdu.expLevel = NOLEVEL;
                }
                else
                {
                    rc = br_Evaluate(charArrToString(localbuf), ref level);

                    if (rc != 1)
                    {
                        if (strlen(localbuf) > 20)
                        {
                            /* truncate bad data */
                            localbuf[20] = '\0';
                            strcat(localbuf, "...");
                        }

                        br_ErrPrint($"Bad value \"{localbuf}\" in expected level in test case input data.\n");
                        return (BR_INITERR);
                    }

                    bdu.expLevel = level;
                }

                // bdu++;
            }

            // len = (int)strlen(expOrder);
            /*
             * Even a zero-length string is a valid expected order, if all
             * of the elements of the string resolve to NOLEVEL.
             */
            // tmp = (char*)malloc(len + 1);
            // if (tmp == null)
            // {
            //     return (BR_ALLOCERR);
            // }
            //
            // strcpy(tmp, expOrder);
            ctxt.expOrder = charArrToString(expOrder);

            ctxt.expEmbeddingLevel = expEmbeddingLevel;

            return (BR_TESTOK);
        }

        /***********************************************************/

        /*
         * SECTION: Dropping of UBACONTEXT
         */

        /*
         * br_DropRun
         *
         * Deallocate dynamically allocated BIDIRUN struct.
         */

        static void br_DropRun(BIDIRUNPTR brp)
        {
            // if (brp.textChain != null)
            // {
            //     free(brp.textChain);
            // }
            //
            // free(brp);
        }

        /*
         * br_DropSequence
         *
         * Deallocate dynamically allocated ISOLATING_RUN_SEQUENCE struct.
         */

        static void br_DropSequence(IRSEQPTR irp)
        {
            // BIDIRUNLISTPTR brl1, brl2;
            //
            // /* deallocate any attached list of runs first */
            // brl1 = irp.theRuns;
            // while (brl1 != null)
            // {
            //     brl2 = brl1.next;
            //     free(brl1);
            //     brl1 = brl2;
            // }
            //
            // if (irp.textChain != null)
            // {
            //     free(irp.textChain);
            // }
            //
            // free(irp);
        }

        /*
         * br_DropContext
         *
         * Deallocate dynamically allocated context data.
         */

        static void br_DropContext(UBACONTEXT ctxt)
        {
//             BIDIRUNPTR br1, br2;
//             IRSEQPTR ir1, ir2;
//
//             if (ctxt == null)
//             {
//                 return;
//             }
// #if NOTDEF
//             if ( ctxt.theText != null )
//             {
//     	        free ( ctxt.theText);
//             }
// #endif
//             /* Hand over hand list disposal of run list */
//             br1 = ctxt.theRuns;
//             while (br1 != null)
//             {
//                 br2 = br1.next;
//                 br_DropRun(br1);
//                 br1 = br2;
//             }
//
//             /* Hand over hand list disposal of sequence list */
//             ir1 = ctxt.theSequences;
//             while (ir1 != null)
//             {
//                 ir2 = ir1.next;
//                 br_DropSequence(ir1);
//                 ir1 = ir2;
//             }
//
//             if (ctxt.expOrder != null)
//             {
//                 free(ctxt.expOrder);
//             }
//
//             free(ctxt);
        }

        /***********************************************************/

        /*
         * SECTION: Routines to run test cases
         */

        /*
         * br_RunOneTest
         *
         * Take a UBACONTEXT, run the UBA algorithm on it, and
         * check the results against the expected results.
         *
         * The checkResults parameter can be set to 0 to skip the
         * checking when running static tests during implementation
         * development.
         */

        static int br_RunOneTest(UBACONTEXT ctxt, bool checkResults)
        {
            /* Run the UBA algorithm steps on the text. */
            var rc =
                br_UBA(ctxt);
            if (rc != BR_TESTOK)
            {
                return (rc);
            }

            /* Mark the algorithm state as complete. */

            ctxt.state = ALGORITHM_STATE.State_Complete;

            /* Examine the results in the context data for correctness. */

            if (checkResults)
            {
                rc = br_Check(ctxt);
                if (rc != BR_TESTOK)
                {
                    return (rc);
                }
            }

            return (BR_TESTOK); /* Success */
        }

        /*
         * br_ProcessOneTestCase
         *
         * Public API exported from the library.
         *
         * Take input values for one test case from the input file.
         *
         * Construct a UBACONTEXT and run that test case.
         *
         * If expEmbeddingLevel is -1, that is a signal that there
         * are no expected results. Run the test case without them.
         * (expLevels and expOrder should be set to null, but will
         * be ignored in any case).
         *
         * If expEmbeddingLevel is 0 or 1, that is a signal that there
         * are expected results. Attach them to the context.
         *
         * Returns BR_TESTOK (=1) if all goes well.
         * Error return values:
         *   BR_INITERR   -2  Initialization failure (problem with input).
         *   BR_ALLOCERR  -3  Error in context construction.
         *   BR_TESTERR   -4  Bad return from UBA processing
         */

        static int br_ProcessOneTestCase(Int64 testCaseNumber, U_Int_32[] text, int textLen,
            int paragraphDirection, int expEmbeddingLevel, char[] expLevels, char[] expOrder)
        {
            int rc;
            char[] errString = new char[80];

            if (Trace(Trace2))
            {
                printf("\nTrace: Entering br_ProcessOneTestCase\n");
            }

            /*
             * This is a public API. Double check that the br_Init call was
             * done correctly before making this call.
             */

            if (!br_GetInitDone())
            {
                br_ErrPrint("Error: Initialization not completed.\n");
                return (BR_INITERR);
            }

            var ctxt = br_ConstructContext(textLen, text, (Paragraph_Direction)paragraphDirection);

            if (ctxt == null)
            {
                br_ErrPrint("Error: Bad return in context construction.\n");
                return (BR_ALLOCERR);
            }

            var checkResults = (expEmbeddingLevel >= 0);

            if (checkResults)
            {
                rc = br_AttachExpectedResults(ctxt, expEmbeddingLevel, expLevels,
                    expOrder);

                if (rc != BR_TESTOK)
                {
                    br_ErrPrint("Error: Unable to attach expected results to context.\n");
                    br_DropContext(ctxt);
                    return (rc);
                }
            }

            /*
             * Set the testCaseNumber into the context, so it can be used to
             * tag trace output.
             */
            ctxt.testId = testCaseNumber;

            if (Trace(Trace11))
            {
                printf("\n===================== Testcase {0} =====================\n",
                    testCaseNumber);
            }

            rc = br_RunOneTest(ctxt, checkResults);

            if ((rc != BR_TESTOK) && Trace(Trace0))
            {
                br_ErrPrint($"Error: Bad return {rc} from UBA processing.\n");
            }

            br_DropContext(ctxt);

            return (rc);
        }

        static string truncationErr = "Error: Output value was truncated.\n";

        /*
         * br_FormatLevelOutput
         *
         * Take the list of levels and format it as a string, using the
         * format of BidiCharacterTest.txt. Push the result into the
         * output parameter.
         *
         * Return BR_TESTOK (=1) if o.k.
         * Return BR_OUTPUTERR (=-1) and print a truncation error if the output parameter is too short.
         */

        static int br_FormatLevelOutput(char[] d, int dlen, UBACONTEXT ctxt)
        {
            var dp = 0;
            var dend = 0 + dlen;
            /* Initialize output parameter to an empty string. */
            d[dp] = '\0';
            foreach (var bdu in ctxt.textUnits)
            {
                if (dp > 0)
                {
                    if (dp < dend - 1)
                    {
                        d[dp] = ' ';
                        dp++;
                        d[dp] = '\0';
                    }
                    else
                    {
                        br_ErrPrint(truncationErr);
                        return (BR_OUTPUTERR);
                    }
                }

                var localbuf = bdu.level == NOLEVEL ? "x" : bdu.level.ToString();
                int tlen = localbuf.Length;
                if (dp < dend - tlen)
                {
                    for (int i = 0; i < tlen; i++)
                    {
                        d[dp] = localbuf[i];
                        dp++;
                        d[dp] = '\0';
                    }
                }
                else
                {
                    br_ErrPrint(truncationErr);
                    return (BR_OUTPUTERR);
                }
            }

            return (BR_TESTOK);
        }

        /*
         * br_FormatOrderOutput
         *
         * Take the order list and format it as a string, using the
         * format of BidiCharacterTest.txt. Push the result into the
         * output parameter.
         *
         * Return BR_TESTOK (=1) if o.k.
         * Return BR_OUTPUTERR (=-1) and print a truncation error if the output parameter is too short.
         */

        static int br_FormatOrderOutput(char[] d, int dlen, UBACONTEXT ctxt)
        {
            var dp = 0;
            var dend = 0 + dlen;
            /* Initialize output parameter to an empty string. */
            d[dp] = '\0';
            foreach (var bdu in ctxt.textUnits)
            {
                if (bdu.order != NOLEVEL)
                {
                    if (dp > 0)
                    {
                        if (dp < dend - 1)
                        {
                            d[dp] = ' ';
                            dp++;
                            d[dp] = '\0';
                        }
                        else
                        {
                            br_ErrPrint(truncationErr);
                            return (BR_OUTPUTERR);
                        }
                    }

                    var localbuf = bdu.order.ToString();
                    int tlen = localbuf.Length;
                    if (dp < dend - tlen)
                    {
                        for (int i = 0; i < tlen; i++)
                        {
                            d[dp] = localbuf[i];
                            dp++;
                            d[dp] = '\0';
                        }
                    }
                    else
                    {
                        br_ErrPrint(truncationErr);
                        return (BR_OUTPUTERR);
                    }
                }
            }

            return (BR_TESTOK);
        }

        /*
         * br_QueryOneTestCase
         *
         * Public API exported from the library.
         *
         * Take input values for one test case.
         *
         * Construct a UBACONTEXT and run that test case.
         *
         * Do no checking against expected values. Instead, return
         * the results in the output parameters for embeddingLevel,
         * levels, and order, and let the client process them.
         *
         * Returns BR_TESTOK (=1) if all goes well.
         * Error return values:
         *   BR_OUTPUTERR -1  Error in formatting output parameters.
         *   BR_INITERR   -2  Initialization not completed before call.
         *   BR_ALLOCERR  -3  Error in context construction.
         *   BR_TESTERR   -4  Bad return from UBA processing
         */

        static int br_QueryOneTestCase(U_Int_32[] text, int textLen,
            int paragraphDirection, ref int embeddingLevel, char[] levels, int levelsLen,
            char[] order, int orderLen)
        {
            /*
             * This is a public API. Double check that the br_Init call was
             * done correctly before making this call.
             */

            if (!br_GetInitDone())
            {
                return (BR_INITERR);
            }

            var ctxt = br_ConstructContext(textLen, text, (Paragraph_Direction)paragraphDirection);

            if (ctxt == null)
            {
                embeddingLevel = -1;
                levels[0] = '\0';
                order[0] = '\0';
                return (BR_ALLOCERR);
            }

            var rc = br_RunOneTest(ctxt, false);

            if (rc != BR_TESTOK)
            {
                embeddingLevel = -1;
                levels[0] = '\0';
                order[0] = '\0';
                br_DropContext(ctxt);
                return (BR_TESTERR);
            }

            /*
             * Now extract the results from the UBACONTEXT and push into
             * the client output parameters.
             */

            embeddingLevel = ctxt.paragraphEmbeddingLevel;

            rc = br_FormatLevelOutput(levels, levelsLen, ctxt);

            var rc2 = br_FormatOrderOutput(order, orderLen, ctxt);

            br_DropContext(ctxt);

            if (rc != BR_TESTOK)
            {
                return (rc);
            }
            else if (rc2 != BR_TESTOK)
            {
                return (rc2);
            }

            return (BR_TESTOK);
        }

        static int br_QueryOneTestCase(U_Int_32[] text, int textLen, int paragraphDirection,
            ref int embeddingLevel, ref List<int> outLevels, ref List<int> outOrders)
        {
            /*
             * This is a public API. Double check that the br_Init call was
             * done correctly before making this call.
             */

            if (!br_GetInitDone())
            {
                return (BR_INITERR);
            }

            var ctxt = br_ConstructContext(textLen, text, (Paragraph_Direction)paragraphDirection);

            if (ctxt == null)
            {
                embeddingLevel = -1;
                return (BR_ALLOCERR);
            }

            var rc = br_RunOneTest(ctxt, false);

            if (rc != BR_TESTOK)
            {
                embeddingLevel = -1;
                br_DropContext(ctxt);
                return (BR_TESTERR);
            }

            /*
             * Now extract the results from the UBACONTEXT and push into
             * the client output parameters.
             */

            embeddingLevel = ctxt.paragraphEmbeddingLevel;

            foreach (var bdu in ctxt.textUnits)
            {
                outLevels.Add(bdu.level);
                outOrders.Add(bdu.order);
            }

            br_DropContext(ctxt);
            return (BR_TESTOK);
        }


        /*
         * brutils.c
         *
         * Collection of utility functions used by other modules.
         * These utility functions are used both by modules inside
         * the library and by functions in the bidiref executable.
         *
         * Exports:
         *  GetFileFormat
         *  SetFileFormat
         *
         *  GetUBAVersion
         *  GetUBAVersionStr
         *  SetUBAVarsion
         *  
         *  Trace
         *  TraceOn
         *  TraceOff
         *  TraceOffAll
         *
         *  convertHexToInt
         *  br_Evaluate
         *  copyField
         *  copySubField
         *  br_GetBCFromLabel
         *  br_ErrPrint
         */

        /*
         * SECTION: Generic version information.
         */

        static int ubaVersion = (int)UBA_Version_Type.UBA150; /* Default to UBA150 */
        static int fileFormat = FORMAT_A; /* Format of input data file */

        static string[] versionNums =
        {
            "X.X", "6.2", "6.3", "7.0", "8.0", "9.0", "10.0", "11.0", "12.0", "13.0", "14.0", "15.0", "def:15.0"
        };

        static int GetFileFormat()
        {
            return (fileFormat);
        }

        static void SetFileFormat(int format)
        {
            fileFormat = format;
        }

        static int GetUBAVersion()
        {
            return (ubaVersion);
        }

        static string GetUBAVersionStr()
        {
            return (versionNums[ubaVersion]);
        }

        /*
         * SetUBAVersion()
         *
         * Set ubaVersion to the version passed in.
         *
         * UBACUR (unspecified) will default to latest version.
         */
        static void SetUBAVersion(int version)
        {
            ubaVersion = version;
        }

        /***********************************************************/

        /*
         * SECTION: Traces.
         *
         * This set of routines supports a very simple set of trace
         * flags, with set, unset, and check functions, to enable
         * precise control over what types of debug output are
         * printed during execution.
         *
         * One 32-bit traceFlags variable is defined here, which
         * allows up to 32 individual trace bits to be defined for
         * the flags.
         *
         * The actual values of the trace flags are established
         * in bidiref.h.
         *
         * Initial default trace flag settings are established
         * based on chosen debug levels during program start, but
         * can be changed programmatically anytime during execution.
         */
        static U_Int_32 traceFlags = 0; /* Trace flags for control of debug output */

        /*
         * Trace: check a trace bit
         */
        static bool Trace(U_Int_32 traceValue)
        {
            return (((traceValue & traceFlags) == traceValue));
        }

        /*
         * TraceOn: turn on a trace bit.
         */
        static void TraceOn(U_Int_32 traceValue)
        {
            traceFlags |= traceValue;
        }

        /*
         * TraceOff: turn off a trace bit.
         */
        static void TraceOff(U_Int_32 traceValue)
        {
            traceFlags &= ~traceValue;
        }

        /*
         * TraceOffAll: turn off all trace bits.
         */
        static void TraceOffAll()
        {
            traceFlags = 0;
        }

        /***********************************************************/

        /*
         * SECTION: Error Printing
         */

        /*
         * br_ErrPrint
         *
         * Encapsulate console error logging, so it can be turned on
         * or off simply with a trace flag.
         */
        static void br_ErrPrint(string errString)
        {
            if (Trace(Trace15))
            {
                printf(errString);
            }
        }

        /***********************************************************/

        /*
         * SECTION: Numeric evaluation utility routines.
         */

        /*
         * Convert an "ASCII" representation of sb/mb int to real int.
         *
         * Bounded to 0 .. 0xFFFFFFFF.
         *
         * i.e. "c2ba" to 0xC2BA, "8000000A" to 0x8000000A, etc.
         *
         * RETURNS:
         *    0  converted o.k.
         *   -1  input string too long to convert or too short to be valid.
         *   -2  non-hex digit input.
         */

        static int convertHexToInt(ref U_Int_32 dest, char[] source)
        {
            return convertHexToInt(ref dest, charArrToString(source));
        }

        static int convertHexToInt(ref U_Int_32 dest, string source)
        {
            dest = 0;
            if (string.IsNullOrEmpty(source) || source.Length > 8 || source.Length < 4)
            {
                return -1;
            }

            dest = Convert.ToUInt32(source, 16);
            return 0;

            // int slen;
            // int i;
            // char *sp;
            // char b1;
            // unsigned char n1;
            //
            // slen = (int) strlen ( source );
            // if ( ( slen > 8 ) || ( slen < 4 ) )
            // {
            //     return ( -1 );
            // }
            // sp = source;
            // *dest = 0;
            // for ( i = 0; i < slen; i++ )
            // {
            //     *dest <<= 4;
            //     b1 = (char)toupper(*sp++);
            //     if ( ( b1 >= 0x41 ) && ( b1 <= 0x46 ) )
            //     {
            //         n1 = (unsigned char)(b1 - 55);
            //     }
            //     else if ( ( b1 >= 0x30 ) && ( b1 <= 0x39 ) )
            //     {
            //         n1 = (unsigned char)(b1 - 48);
            //     }
            //     else
            //     {
            //         return ( -2 );
            //     }
            //     *dest |= n1;
            // }
            //
            // return 0 ;
        }

        /*
         * br_Evaluate
         *
         * Use the error checking strtol() function, rather
         * that atoi(), to provide a modicum of validation for
         * bad data in the test case file.
         *
         * This routine does not bother checking underflow
         * and overflow cases or the cast, because it is far more likely
         * that typos or missing data will be the source of
         * the problem, rather than monstrously large numbers.
         */
        static int br_Evaluate(string data, ref int outval)
        {
            outval = Convert.ToInt32(data, 10);
            return 1;
            // char* endptr;
            // long val;
            //
            // errno = 0;
            // val = strtol(data, &endptr, 10);
            // if ((errno != 0) && (val == 0))
            // {
            //     *outval = 0;
            //     return (-1);
            // }
            //
            // if (endptr == data)
            // {
            //     *outval = 0;
            //     return (-1);
            // }
            //
            // *outval = (int)val;
            // return (1);
        }

        /***********************************************************/

        /*
         * SECTION: Field parsing utility routines.
         */
        // private const char DELIM = ';';


        /*
         * copyField
         *
         * Copy one semicolon-delimited field from a line parsed from
         * a UCD-style property file.
         *
         * This routine does not stop on spaces, but *does* stop on
         * EOLN characters, which are not trimmed from lines by
         * the generic line parsers.
         *
         * This routine, like all others in this set of utilities,
         * returns a pointer to the start of the *next* field in
         * in the src string, so it can be called repeated to parse
         * out multiple fields.
         *
         * Check for s = "" or *s = '\0' as an end of input condition.
         */
        static int copyField(char[] dest, string src, int index)
        {
            return copyField(dest, src.ToCharArray(), index);
        }

        static int copyField(char[] dest, char[] src, int index)
        {
            var sp = index;
            var dp = 0;
            while ((src[sp] != DELIM) && (src[sp] != '\0') && (src[sp] != '\n'))
            {
                dest[dp++] = src[sp++];
            }

            dest[dp] = '\0';
            if ((src[sp] == DELIM) || (src[sp] == '\n'))
            {
                sp++;
            }

            return sp;
        }

        /*
         * copySubField
         *
         * As for copyField, but copies one space-delimited subfield
         * out of a multi-value field.
         *
         * This is used, for instance, to parse out a sequence of
         * levels, one at a time.
         *
         * This routine does not check for EOLN, because it assumes
         * the src string is itself an already-parsed null-delimited
         * string.
         */
        static int copySubField(char[] dest, string src, int index)
        {
            return copySubField(dest, src.ToCharArray(), index);
        }

        static int copySubField(char[] dest, char[] src, int index)
        {
            var sp = index;
            var dp = 0;
            while ((src[sp] != ' ') && (src[sp] != '\0'))
            {
                dest[dp++] = src[sp++];
            }

            dest[dp] = '\0';
            if ((src[sp] == ' '))
            {
                sp++;
            }

            return sp;
        }

        /***********************************************************/

        /*
         * SECTION: Property alias parsing utility routines.
         */

        /*
         * br_GetBCFromLabel
         *
         * Get a Bidi_Class enumerated value from a string parsed
         * from a bidi field in a test case, from UnicodeData.txt,
         * or some other source. This supports turning the
         * symbolic alias (assumed here to be expressed in
         * uppercase, as in UnicodeData.txt) into a defined
         * enumerated value, for use in further processing.
         */
        static BIDIPROP br_GetBCFromLabel(string src)
        {
            return br_GetBCFromLabel(src.ToCharArray());
        }

        static BIDIPROP br_GetBCFromLabel(char[] src)
        {
            int strlen = Bidi.strlen(src);
            if ((strlen > 3) || (strlen < 1))
            {
                return (BIDIPROP.BIDI_Unknown);
            }

            switch (src[0])
            {
                case 'L':
                    if (strlen == 1)
                        return (BIDIPROP.BIDI_L);
                    else if (strcmp(src, "LRE") == 0)
                        return (BIDIPROP.BIDI_LRE);
                    else if (strcmp(src, "LRO") == 0)
                        return (BIDIPROP.BIDI_LRO);
                    else if (strcmp(src, "LRI") == 0)
                        return (BIDIPROP.BIDI_LRI);
                    else return (BIDIPROP.BIDI_Unknown);
                case 'R':
                    if (strlen == 1)
                        return (BIDIPROP.BIDI_R);
                    else if (strcmp(src, "RLE") == 0)
                        return (BIDIPROP.BIDI_RLE);
                    else if (strcmp(src, "RLO") == 0)
                        return (BIDIPROP.BIDI_RLO);
                    else if (strcmp(src, "RLI") == 0)
                        return (BIDIPROP.BIDI_RLI);
                    else return (BIDIPROP.BIDI_Unknown);
                case 'B':
                    if (strlen == 1)
                        return (BIDIPROP.BIDI_B);
                    else if (strcmp(src, "BN") == 0)
                        return (BIDIPROP.BIDI_BN);
                    else return (BIDIPROP.BIDI_Unknown);
                case 'S': return (BIDIPROP.BIDI_S);
                case 'P':
                    if (strcmp(src, "PDF") == 0)
                        return (BIDIPROP.BIDI_PDF);
                    if (strcmp(src, "PDI") == 0)
                        return (BIDIPROP.BIDI_PDI);
                    else return (BIDIPROP.BIDI_Unknown);
                case 'N':
                    if (strcmp(src, "NSM") == 0)
                        return (BIDIPROP.BIDI_NSM);
                    else return (BIDIPROP.BIDI_Unknown);
                case 'F':
                    if (strcmp(src, "FSI") == 0)
                        return (BIDIPROP.BIDI_FSI);
                    else return (BIDIPROP.BIDI_Unknown);
                case 'E':
                    switch (src[1])
                    {
                        case 'N': return (BIDIPROP.BIDI_EN);
                        case 'S': return (BIDIPROP.BIDI_ES);
                        case 'T': return (BIDIPROP.BIDI_ET);
                        default: return (BIDIPROP.BIDI_Unknown);
                    }
                case 'A':
                    switch (src[1])
                    {
                        case 'N': return (BIDIPROP.BIDI_AN);
                        case 'L': return (BIDIPROP.BIDI_AL);
                        default: return (BIDIPROP.BIDI_Unknown);
                    }
                case 'C':
                    switch (src[1])
                    {
                        case 'S': return (BIDIPROP.BIDI_CS);
                        default: return (BIDIPROP.BIDI_Unknown);
                    }
                case 'W':
                    switch (src[1])
                    {
                        case 'S': return (BIDIPROP.BIDI_WS);
                        default: return (BIDIPROP.BIDI_Unknown);
                    }
                case 'O':
                    switch (src[1])
                    {
                        case 'N': return (BIDIPROP.BIDI_ON);
                        default: return (BIDIPROP.BIDI_Unknown);
                    }
                default: return (BIDIPROP.BIDI_Unknown);
            }
        }

        public static List<int> TextTagIndexList = new List<int>();
        public static List<bool> TextTagFlagList = new List<bool>();
        public static List<int> TextLTRIndexList = new List<int>();
        private static void InnerLogicalToVisual(FastStringBuilder buffer)
        {
            int resEmbeddingLevel = 0;

            int paragraphDirection = (int)Paragraph_Direction.Dir_Auto;
            var testUbaVersion = UBA_Version_Type.UBA150;

            ClearLog();

            TraceOff(TraceAll);
            // TraceOn ( Trace4 | Trace7 | Trace9 | Trace12 );
            // TraceOn ( Trace3 | Trace5 | Trace6 | Trace8 );
            // TraceOn(Trace0 | Trace1 | Trace2 | Trace11 | Trace15);
            // TraceOn(TraceAll);


            TraceOff(Trace14);
            // TraceOn ( Trace14 );

            int ubaInputSeqLen = 0;
            U_Int_32[] ubaInputSeq = new U_Int_32[buffer.Length];
            for (int i = 0; i < buffer.Length; i++)
            {
                int unicode32CodePoint = buffer.Get(i);
                ubaInputSeq[ubaInputSeqLen++] = (U_Int_32)unicode32CodePoint;
            }

            var rc = br_InitWithPath((int)testUbaVersion, "ucd");
            if (rc != 1)
            {
                printf("Error in initialization.\n");
                return;
            }

            var ubaLevelSeq = new List<int>();
            var ubaOrderSeq = new List<int>();

            rc = br_QueryOneTestCase(ubaInputSeq, ubaInputSeqLen, paragraphDirection,
                ref resEmbeddingLevel, ref ubaLevelSeq, ref ubaOrderSeq);

            if (rc == 1)
            {
                printf("Paragraph Embedding Level: {0}\n", resEmbeddingLevel);
                printf("Resolved Levels: [{0}]\n", string.Join(" ", ubaLevelSeq));
                printf("Resolved Order:  [{0}]\n", string.Join(" ", ubaOrderSeq));

                buffer.Clear();
                for (int i = 0; i < ubaOrderSeq.Count; i++)
                {
                    int index = ubaOrderSeq[i];
                    if (index >= 0)
                    {
                        var c = ubaInputSeq[index];
                        var l = ubaLevelSeq[index];
                        bool isVisualIgnoreContains = VisualIgnoreSet.Contains(c);
                        if (!isVisualIgnoreContains)
                        {
                            bool isLTR = l % 2 == 0;
                            if (!isLTR)
                            {
                                if (MirroredCharsMap.TryGetValue(c, out var cp_new))
                                {
                                    c = cp_new;
                                }
                            }
                            buffer.Append((int)c);
                        }

                        for (int j = 0; j < TextTagIndexList.Count; j++)
                        {
                            if (!TextTagFlagList[j] && TextTagIndexList[j] == index)
                            {
                                TextTagIndexList[j] = buffer.Length - 1;
                                TextTagFlagList[j] = true;
                                TextLTRIndexList[j] = buffer.Length - 1;
                                if (l%2 == 0) // 说明是LTR 那么需要找到最后一个LTR字符才是真正的位置
                                {
                                    TextLTRIndexList[j] += 1;
                                    for (int k = index + 1; k < ubaLevelSeq.Count; k++)
                                    {
                                        if (ubaLevelSeq[k] % 2 != 0)
                                            break;
                                        else
                                            TextLTRIndexList[j] += 1;
                                    }
                                }
                                // break; 不能break 存在多个标签在同一个位置的情况
                            }
                        }
                    }
                }
            }
            else
            {
                printf("An error occurred (return code {0}).\n", rc);
            }
        }


        private static readonly Dictionary<U_Int_32, U_Int_32> MirroredCharsMap = new Dictionary<U_Int_32, U_Int_32>()
        {
            [0x0028] = 0x0029, // o # LEFT PARENTHESIS
            [0x0029] = 0x0028, // c # RIGHT PARENTHESIS
            [0x005B] = 0x005D, // o # LEFT SQUARE BRACKET
            [0x005D] = 0x005B, // c # RIGHT SQUARE BRACKET
            [0x007B] = 0x007D, // o # LEFT CURLY BRACKET
            [0x007D] = 0x007B, // c # RIGHT CURLY BRACKET
            [0x0F3A] = 0x0F3B, // o # TIBETAN MARK GUG RTAGS GYON
            [0x0F3B] = 0x0F3A, // c # TIBETAN MARK GUG RTAGS GYAS
            [0x0F3C] = 0x0F3D, // o # TIBETAN MARK ANG KHANG GYON
            [0x0F3D] = 0x0F3C, // c # TIBETAN MARK ANG KHANG GYAS
            [0x169B] = 0x169C, // o # OGHAM FEATHER MARK
            [0x169C] = 0x169B, // c # OGHAM REVERSED FEATHER MARK
            [0x2045] = 0x2046, // o # LEFT SQUARE BRACKET WITH QUILL
            [0x2046] = 0x2045, // c # RIGHT SQUARE BRACKET WITH QUILL
            [0x207D] = 0x207E, // o # SUPERSCRIPT LEFT PARENTHESIS
            [0x207E] = 0x207D, // c # SUPERSCRIPT RIGHT PARENTHESIS
            [0x208D] = 0x208E, // o # SUBSCRIPT LEFT PARENTHESIS
            [0x208E] = 0x208D, // c # SUBSCRIPT RIGHT PARENTHESIS
            [0x2308] = 0x2309, // o # LEFT CEILING
            [0x2309] = 0x2308, // c # RIGHT CEILING
            [0x230A] = 0x230B, // o # LEFT FLOOR
            [0x230B] = 0x230A, // c # RIGHT FLOOR
            [0x2329] = 0x232A, // o # LEFT-POINTING ANGLE BRACKET
            [0x232A] = 0x2329, // c # RIGHT-POINTING ANGLE BRACKET
            [0x2768] = 0x2769, // o # MEDIUM LEFT PARENTHESIS ORNAMENT
            [0x2769] = 0x2768, // c # MEDIUM RIGHT PARENTHESIS ORNAMENT
            [0x276A] = 0x276B, // o # MEDIUM FLATTENED LEFT PARENTHESIS ORNAMENT
            [0x276B] = 0x276A, // c # MEDIUM FLATTENED RIGHT PARENTHESIS ORNAMENT
            [0x276C] = 0x276D, // o # MEDIUM LEFT-POINTING ANGLE BRACKET ORNAMENT
            [0x276D] = 0x276C, // c # MEDIUM RIGHT-POINTING ANGLE BRACKET ORNAMENT
            [0x276E] = 0x276F, // o # HEAVY LEFT-POINTING ANGLE QUOTATION MARK ORNAMENT
            [0x276F] = 0x276E, // c # HEAVY RIGHT-POINTING ANGLE QUOTATION MARK ORNAMENT
            [0x2770] = 0x2771, // o # HEAVY LEFT-POINTING ANGLE BRACKET ORNAMENT
            [0x2771] = 0x2770, // c # HEAVY RIGHT-POINTING ANGLE BRACKET ORNAMENT
            [0x2772] = 0x2773, // o # LIGHT LEFT TORTOISE SHELL BRACKET ORNAMENT
            [0x2773] = 0x2772, // c # LIGHT RIGHT TORTOISE SHELL BRACKET ORNAMENT
            [0x2774] = 0x2775, // o # MEDIUM LEFT CURLY BRACKET ORNAMENT
            [0x2775] = 0x2774, // c # MEDIUM RIGHT CURLY BRACKET ORNAMENT
            [0x27C5] = 0x27C6, // o # LEFT S-SHAPED BAG DELIMITER
            [0x27C6] = 0x27C5, // c # RIGHT S-SHAPED BAG DELIMITER
            [0x27E6] = 0x27E7, // o # MATHEMATICAL LEFT WHITE SQUARE BRACKET
            [0x27E7] = 0x27E6, // c # MATHEMATICAL RIGHT WHITE SQUARE BRACKET
            [0x27E8] = 0x27E9, // o # MATHEMATICAL LEFT ANGLE BRACKET
            [0x27E9] = 0x27E8, // c # MATHEMATICAL RIGHT ANGLE BRACKET
            [0x27EA] = 0x27EB, // o # MATHEMATICAL LEFT DOUBLE ANGLE BRACKET
            [0x27EB] = 0x27EA, // c # MATHEMATICAL RIGHT DOUBLE ANGLE BRACKET
            [0x27EC] = 0x27ED, // o # MATHEMATICAL LEFT WHITE TORTOISE SHELL BRACKET
            [0x27ED] = 0x27EC, // c # MATHEMATICAL RIGHT WHITE TORTOISE SHELL BRACKET
            [0x27EE] = 0x27EF, // o # MATHEMATICAL LEFT FLATTENED PARENTHESIS
            [0x27EF] = 0x27EE, // c # MATHEMATICAL RIGHT FLATTENED PARENTHESIS
            [0x2983] = 0x2984, // o # LEFT WHITE CURLY BRACKET
            [0x2984] = 0x2983, // c # RIGHT WHITE CURLY BRACKET
            [0x2985] = 0x2986, // o # LEFT WHITE PARENTHESIS
            [0x2986] = 0x2985, // c # RIGHT WHITE PARENTHESIS
            [0x2987] = 0x2988, // o # Z NOTATION LEFT IMAGE BRACKET
            [0x2988] = 0x2987, // c # Z NOTATION RIGHT IMAGE BRACKET
            [0x2989] = 0x298A, // o # Z NOTATION LEFT BINDING BRACKET
            [0x298A] = 0x2989, // c # Z NOTATION RIGHT BINDING BRACKET
            [0x298B] = 0x298C, // o # LEFT SQUARE BRACKET WITH UNDERBAR
            [0x298C] = 0x298B, // c # RIGHT SQUARE BRACKET WITH UNDERBAR
            [0x298D] = 0x2990, // o # LEFT SQUARE BRACKET WITH TICK IN TOP CORNER
            [0x298E] = 0x298F, // c # RIGHT SQUARE BRACKET WITH TICK IN BOTTOM CORNER
            [0x298F] = 0x298E, // o # LEFT SQUARE BRACKET WITH TICK IN BOTTOM CORNER
            [0x2990] = 0x298D, // c # RIGHT SQUARE BRACKET WITH TICK IN TOP CORNER
            [0x2991] = 0x2992, // o # LEFT ANGLE BRACKET WITH DOT
            [0x2992] = 0x2991, // c # RIGHT ANGLE BRACKET WITH DOT
            [0x2993] = 0x2994, // o # LEFT ARC LESS-THAN BRACKET
            [0x2994] = 0x2993, // c # RIGHT ARC GREATER-THAN BRACKET
            [0x2995] = 0x2996, // o # DOUBLE LEFT ARC GREATER-THAN BRACKET
            [0x2996] = 0x2995, // c # DOUBLE RIGHT ARC LESS-THAN BRACKET
            [0x2997] = 0x2998, // o # LEFT BLACK TORTOISE SHELL BRACKET
            [0x2998] = 0x2997, // c # RIGHT BLACK TORTOISE SHELL BRACKET
            [0x29D8] = 0x29D9, // o # LEFT WIGGLY FENCE
            [0x29D9] = 0x29D8, // c # RIGHT WIGGLY FENCE
            [0x29DA] = 0x29DB, // o # LEFT DOUBLE WIGGLY FENCE
            [0x29DB] = 0x29DA, // c # RIGHT DOUBLE WIGGLY FENCE
            [0x29FC] = 0x29FD, // o # LEFT-POINTING CURVED ANGLE BRACKET
            [0x29FD] = 0x29FC, // c # RIGHT-POINTING CURVED ANGLE BRACKET
            [0x2E22] = 0x2E23, // o # TOP LEFT HALF BRACKET
            [0x2E23] = 0x2E22, // c # TOP RIGHT HALF BRACKET
            [0x2E24] = 0x2E25, // o # BOTTOM LEFT HALF BRACKET
            [0x2E25] = 0x2E24, // c # BOTTOM RIGHT HALF BRACKET
            [0x2E26] = 0x2E27, // o # LEFT SIDEWAYS U BRACKET
            [0x2E27] = 0x2E26, // c # RIGHT SIDEWAYS U BRACKET
            [0x2E28] = 0x2E29, // o # LEFT DOUBLE PARENTHESIS
            [0x2E29] = 0x2E28, // c # RIGHT DOUBLE PARENTHESIS
            [0x2E55] = 0x2E56, // o # LEFT SQUARE BRACKET WITH STROKE
            [0x2E56] = 0x2E55, // c # RIGHT SQUARE BRACKET WITH STROKE
            [0x2E57] = 0x2E58, // o # LEFT SQUARE BRACKET WITH DOUBLE STROKE
            [0x2E58] = 0x2E57, // c # RIGHT SQUARE BRACKET WITH DOUBLE STROKE
            [0x2E59] = 0x2E5A, // o # TOP HALF LEFT PARENTHESIS
            [0x2E5A] = 0x2E59, // c # TOP HALF RIGHT PARENTHESIS
            [0x2E5B] = 0x2E5C, // o # BOTTOM HALF LEFT PARENTHESIS
            [0x2E5C] = 0x2E5B, // c # BOTTOM HALF RIGHT PARENTHESIS
            [0x3008] = 0x3009, // o # LEFT ANGLE BRACKET
            [0x3009] = 0x3008, // c # RIGHT ANGLE BRACKET
            [0x300A] = 0x300B, // o # LEFT DOUBLE ANGLE BRACKET
            [0x300B] = 0x300A, // c # RIGHT DOUBLE ANGLE BRACKET
            [0x300C] = 0x300D, // o # LEFT CORNER BRACKET
            [0x300D] = 0x300C, // c # RIGHT CORNER BRACKET
            [0x300E] = 0x300F, // o # LEFT WHITE CORNER BRACKET
            [0x300F] = 0x300E, // c # RIGHT WHITE CORNER BRACKET
            [0x3010] = 0x3011, // o # LEFT BLACK LENTICULAR BRACKET
            [0x3011] = 0x3010, // c # RIGHT BLACK LENTICULAR BRACKET
            [0x3014] = 0x3015, // o # LEFT TORTOISE SHELL BRACKET
            [0x3015] = 0x3014, // c # RIGHT TORTOISE SHELL BRACKET
            [0x3016] = 0x3017, // o # LEFT WHITE LENTICULAR BRACKET
            [0x3017] = 0x3016, // c # RIGHT WHITE LENTICULAR BRACKET
            [0x3018] = 0x3019, // o # LEFT WHITE TORTOISE SHELL BRACKET
            [0x3019] = 0x3018, // c # RIGHT WHITE TORTOISE SHELL BRACKET
            [0x301A] = 0x301B, // o # LEFT WHITE SQUARE BRACKET
            [0x301B] = 0x301A, // c # RIGHT WHITE SQUARE BRACKET
            [0xFE59] = 0xFE5A, // o # SMALL LEFT PARENTHESIS
            [0xFE5A] = 0xFE59, // c # SMALL RIGHT PARENTHESIS
            [0xFE5B] = 0xFE5C, // o # SMALL LEFT CURLY BRACKET
            [0xFE5C] = 0xFE5B, // c # SMALL RIGHT CURLY BRACKET
            [0xFE5D] = 0xFE5E, // o # SMALL LEFT TORTOISE SHELL BRACKET
            [0xFE5E] = 0xFE5D, // c # SMALL RIGHT TORTOISE SHELL BRACKET
            [0xFF08] = 0xFF09, // o # FULLWIDTH LEFT PARENTHESIS
            [0xFF09] = 0xFF08, // c # FULLWIDTH RIGHT PARENTHESIS
            [0xFF3B] = 0xFF3D, // o # FULLWIDTH LEFT SQUARE BRACKET
            [0xFF3D] = 0xFF3B, // c # FULLWIDTH RIGHT SQUARE BRACKET
            [0xFF5B] = 0xFF5D, // o # FULLWIDTH LEFT CURLY BRACKET
            [0xFF5D] = 0xFF5B, // c # FULLWIDTH RIGHT CURLY BRACKET
            [0xFF5F] = 0xFF60, // o # FULLWIDTH LEFT WHITE PARENTHESIS
            [0xFF60] = 0xFF5F, // c # FULLWIDTH RIGHT WHITE PARENTHESIS
            [0xFF62] = 0xFF63, // o # HALFWIDTH LEFT CORNER BRACKET
            [0xFF63] = 0xFF62, // c # HALFWIDTH RIGHT CORNER BRACKET
        };

        private static readonly HashSet<uint> VisualIgnoreSet = new HashSet<uint>(Enum.GetValues(typeof(SpecialCharacters)).Cast<uint>());

        // Entry point for algorithm to return at final correct display order
        public static void LogicalToVisual(FastStringBuilder buffer, int[] lineBreaks = null)
        {
            System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            sw.Start();
            try
            {
                InnerLogicalToVisual(buffer);
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogException(e);
            }

            sw.Stop();
            printf("===============================================\n");
            printf($"==== BIDI Cost: {sw.ElapsedMilliseconds,5} ms, {sw.ElapsedTicks,10} ticks ====\n");
            printf("===============================================\n");
            // UnityEngine.Debug.Log("===============================================\n");
            // UnityEngine.Debug.Log($"==== BIDI Cost: {sw.ElapsedMilliseconds,5} ms, {sw.ElapsedTicks,10} ticks ====\n");
            // UnityEngine.Debug.Log("===============================================\n");
        }
    }
}