namespace RTLTMPro
{
    /// <summary>
    /// Arabic Contextual forms General - Unicode
    /// </summary>
    public enum ArabicIsolatedLetters
    {
        Hamza = 0xFE80,
        AlefMaddaAbove = 0xFE81,
        AlefHamzaAbove = 0xFE83,
        WawHamzaAbove = 0xFE85,
        AlefHamzaBelow = 0xFE87,
        YehHamzaAbove = 0xFE89,
        Alef = 0xFE8D,
        Beh = 0xFE8F,
        TehMarbuta = 0xFE93,
        Teh = 0xFE95,
        Theh = 0xFE99,
        Jeem = 0xFE9D,
        Hah = 0xFEA1,
        Khah = 0xFEA5,
        Dal = 0xFEA9,
        Thal = 0xFEAB,
        Reh = 0xFEAD,
        Zain = 0xFEAF,
        Seen = 0xFEB1,
        Sheen = 0xFEB5,
        Sad = 0xFEB9,
        Dad = 0xFEBD,
        Tah = 0xFEC1,
        Zah = 0xFEC5,
        Ain = 0xFEC9,
        Ghain = 0xFECD,
        Feh = 0xFED1,
        Qaf = 0xFED5,
        Kaf = 0xFED9,
        Lam = 0xFEDD,
        Meem = 0xFEE1,
        Noon = 0xFEE5,
        Heh = 0xFEE9,
        Waw = 0xFEED,
        AlefMaksura = 0xFEEF,
        Yeh = 0xFEF1,
        
        HehGoal = 0xFBA6,
        FarsiYeh = 0xFBFC,
        Peh = 0xFB56,
        TCheh = 0xFB7A,
        Jeh = 0xFB8A,
        Keheh = 0xFB8E, // persian kaf
        Gaf = 0xFB92,
	
    }
}