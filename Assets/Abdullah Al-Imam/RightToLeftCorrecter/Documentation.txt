its very easy now to support arabic with unity UIs


what UIs now is working with Unity UI
1. [Text] -> [RText Prefab]
	a- Add [RText] Prefab to your canvance.
	b- Add some arabic Text in Text Field of [RTLCorrection] Component

2. [Input Field] -> [RInputField Prefab]
	a- Add [RInputField] Prefab to your canvance.
	b- Add some arabic Text in Text Field of [InputField] Component

3. [Toggle] -> [arToggle Prefab]
	a- Add [RToggle] Prefab to your canvance.
	b- Add some arabic Text in Text Field of [RTLCorrection] Component

4. [Button]
	a- Add [RButton] Prefab to your canvance.
	b- Add some arabic Text in Text Field of [RTLCorrection] Component


Notes:
1- you can Flip Brackets from Flip Bracket Property of [RTLCorrection] Component.
2- you can change Numberring style from Number Style Property of [RTLCorrection] Component.