using System;
using System.Collections.Generic;
using UnityEngine;

public static class HtmlTagExtract
{
    public class HtmlTag
    {
        public string tagStart; //标签开始符, 例如 <color=#FF0000>
        public string tagEnd; //标签结束符, 例如 </color>
        public int textStartIndex; //标签内容在纯文本中的起始索引
        public int textEndIndex; //标签内容在纯文本中的结束索引
    }
    
    /// <summary>
    /// 提取html标签，返回处理后的文本
    /// <param name="text">原文本</param>
    /// <param name="tags">提取的标签列表</param>
    /// </summary>
    public static string ExtractHtmlTags(string text, List<HtmlTag> tags)
    {
        if (string.IsNullOrEmpty(text))
            return text;

        tags.Clear();
        var result = new System.Text.StringBuilder();
        var tagStack = new Stack<HtmlTag>(); // 用于匹配开始和结束标签

        int i = 0;
        int textIndex = 0; // 在纯文本中的索引

        while (i < text.Length)
        {
            if (text[i] == '<')
            {
                // 查找标签结束位置
                int tagEnd = text.IndexOf('>', i);
                if (tagEnd != -1)
                {
                    string tagContent = text.Substring(i, tagEnd - i + 1);

                    // 判断是开始标签还是结束标签
                    if (tagContent.StartsWith("</"))
                    {
                        // 结束标签
                        if (tagStack.Count > 0)
                        {
                            var openTag = tagStack.Pop();
                            openTag.tagEnd = tagContent;
                            openTag.textEndIndex = textIndex;
                            tags.Add(openTag);
                        }
                    }
                    else if (!tagContent.EndsWith("/>"))
                    {
                        // 开始标签（非自闭合）
                        var htmlTag = new HtmlTag
                        {
                            tagStart = tagContent,
                            textStartIndex = textIndex
                        };
                        tagStack.Push(htmlTag);
                    }
                    // 自闭合标签暂时忽略，因为它们不包含文本内容

                    i = tagEnd + 1;
                }
                else
                {
                    // 没有找到标签结束，当作普通字符处理
                    result.Append(text[i]);
                    textIndex++;
                    i++;
                }
            }
            else
            {
                // 普通字符
                result.Append(text[i]);
                textIndex++;
                i++;
            }
        }

        return result.ToString();
    }
    
    /// <summary>
    /// 还原html标签，并返回处理后的文本
    /// <param name="text">处理后的纯文本</param>
    /// <param name="textStartIndex">在原纯文本中的开始索引</param>
    /// <param name="textEndIndex">在原纯文本中的结束索引</param>
    /// <param name="tags">标签列表</param>
    /// </summary>
    public static string RestoreHtmlTags(string text, int textStartIndex, int textEndIndex, List<HtmlTag> tags)
    {
        if (string.IsNullOrEmpty(text) || tags == null || tags.Count == 0)
            return text;

        // 创建一个包含位置信息的标签事件列表
        var tagEvents = new List<TagEvent>();

        foreach (var tag in tags)
        {
            // 检查标签是否与当前文本范围有交集
            if (tag.textEndIndex >= textStartIndex && tag.textStartIndex <= textEndIndex)
            {
                // 计算在当前文本中的相对位置
                int relativeStart = Math.Max(0, tag.textStartIndex - textStartIndex);
                int relativeEnd = Math.Min(text.Length, tag.textEndIndex - textStartIndex);

                if (relativeStart <= text.Length)
                {
                    tagEvents.Add(new TagEvent { Position = relativeStart, IsStart = true, Tag = tag });
                }
                if (relativeEnd >= 0 && relativeEnd <= text.Length)
                {
                    tagEvents.Add(new TagEvent { Position = relativeEnd, IsStart = false, Tag = tag });
                }
            }
        }

        // 按位置排序事件
        tagEvents.Sort((a, b) => {
            int posCompare = a.Position.CompareTo(b.Position);
            if (posCompare != 0) return posCompare;
            // 如果位置相同，结束标签优先于开始标签
            return a.IsStart.CompareTo(b.IsStart);
        });

        var result = new System.Text.StringBuilder();
        int currentPos = 0;

        foreach (var tagEvent in tagEvents)
        {
            // 添加到当前位置的文本
            if (tagEvent.Position > currentPos)
            {
                result.Append(text.Substring(currentPos, tagEvent.Position - currentPos));
                currentPos = tagEvent.Position;
            }

            // 添加标签
            if (tagEvent.IsStart)
            {
                result.Append(tagEvent.Tag.tagStart);
            }
            else if (!string.IsNullOrEmpty(tagEvent.Tag.tagEnd))
            {
                result.Append(tagEvent.Tag.tagEnd);
            }
        }

        // 添加剩余文本
        if (currentPos < text.Length)
        {
            result.Append(text.Substring(currentPos));
        }

        return result.ToString();
    }

    /// <summary>
    /// 还原html标签,
    /// </summary>
    /// <param name="text">无标签的文本，并且这时已经反向了，从右往左，这个时候需要在从textEnd->textStart处理</param>
    /// <param name="textEndIndex">左边的序号</param>
    /// <param name="textStartIndex">右边的序号</param>
    /// <param name="tags"></param>
    /// <returns></returns>
    public static string RestoreHtmlTagsFromReverseText(string text, int textEndIndex, int textStartIndex, List<HtmlTag> tags)
    {
        if (string.IsNullOrEmpty(text) || tags == null || tags.Count == 0)
            return text;

        // 创建一个包含位置信息的标签事件列表
        var tagEvents = new List<TagEvent>();

        foreach (var tag in tags)
        {
            // 检查标签是否与当前文本范围有交集
            // 闭区间交集判断：区间A[a1,a2] 与 区间B[b1,b2] 有交集的条件是 a1 <= b2 && b1 <= a2
            // 文本范围：[textEndIndex, textStartIndex]
            // 标签范围：[tag.textStartIndex, tag.textEndIndex]
            // 交集条件：textEndIndex <= tag.textEndIndex && tag.textStartIndex <= textStartIndex
            if (textEndIndex <= tag.textEndIndex && tag.textStartIndex <= textStartIndex)
            {
                // 在反向文本中重新计算标签位置
                // 原始文本长度
                int totalLength = textStartIndex;

                // 原始标签位置
                int originalStart = tag.textStartIndex;
                int originalEnd = tag.textEndIndex;

                // 在反向文本中的新位置计算：
                // 对于文本 "Hello World!" (长度12)，标签包围位置6-11的"World"
                // 反向后变成 "!dlroW olleH"，"World"变成"dlroW"应该在位置1-6
                // 反向位置公式：newPos = totalLength - originalPos
                int reverseStart = totalLength - originalEnd;     // 原来的结束位置变成反向的开始位置
                int reverseEnd = totalLength - originalStart;     // 原来的开始位置变成反向的结束位置

                // 计算相对于当前文本片段的位置
                int relativeStart = Math.Max(0, reverseStart - textEndIndex);
                int relativeEnd = Math.Min(text.Length, reverseEnd - textEndIndex);

                // 确保位置在有效范围内
                if (relativeStart >= 0 && relativeStart <= text.Length)
                {
                    tagEvents.Add(new TagEvent { Position = relativeStart, IsStart = true, Tag = tag });
                }
                if (relativeEnd >= 0 && relativeEnd <= text.Length && relativeEnd != relativeStart)
                {
                    tagEvents.Add(new TagEvent { Position = relativeEnd, IsStart = false, Tag = tag });
                }
            }
        }

        // 按位置排序事件
        tagEvents.Sort((a, b) => {
            int posCompare = a.Position.CompareTo(b.Position);
            if (posCompare != 0) return posCompare;
            // 如果位置相同，结束标签优先于开始标签
            return a.IsStart.CompareTo(b.IsStart);
        });

        var result = new System.Text.StringBuilder();
        int currentPos = 0;

        foreach (var tagEvent in tagEvents)
        {
            // 添加到当前位置的文本
            if (tagEvent.Position > currentPos)
            {
                result.Append(text.Substring(currentPos, tagEvent.Position - currentPos));
                currentPos = tagEvent.Position;
            }

            // 添加标签
            if (tagEvent.IsStart)
            {
                result.Append(tagEvent.Tag.tagStart);
            }
            else if (!string.IsNullOrEmpty(tagEvent.Tag.tagEnd))
            {
                result.Append(tagEvent.Tag.tagEnd);
            }
        }

        // 添加剩余文本
        if (currentPos < text.Length)
        {
            result.Append(text.Substring(currentPos));
        }

        return result.ToString();
    }
    private class TagEvent
    {
        public int Position;
        public bool IsStart;
        public HtmlTag Tag;
    }

    /// <summary>
    /// 测试HTML标签提取和还原功能的公共方法
    /// </summary>
    public static void TestHtmlTagMethods()
    {
        string testText = "Hello <color=green>Green <color=#FF0000>Red</color></color> and <b>Bold</b> Text!";
        Debug.Log("Original: " + testText);

        var tags = new List<HtmlTag>();
        string plainText = ExtractHtmlTags(testText, tags);
        Debug.Log("Plain text: " + plainText);
        Debug.Log("Extracted " + tags.Count + " tags");

        string restoredText = RestoreHtmlTags(plainText, 0, plainText.Length, tags);
        Debug.Log("Restored: " + restoredText);

        // 测试反向文本的标签还原
        TestReverseTextRestore();
    }

    /// <summary>
    /// 测试反向文本的HTML标签还原功能
    /// </summary>
    public static void TestReverseTextRestore()
    {
        Debug.Log("=== Testing Reverse Text Restore ===");

        string originalText = "Hello <color=red>World</color>!";
        Debug.Log("Original: " + originalText);

        var tags = new List<HtmlTag>();
        string plainText = ExtractHtmlTags(originalText, tags);
        Debug.Log("Plain text: " + plainText); // "Hello World!"
        Debug.Log("Plain text length: " + plainText.Length);

        // 打印标签信息
        for (int i = 0; i < tags.Count; i++)
        {
            Debug.Log($"Tag {i}: start={tags[i].textStartIndex}, end={tags[i].textEndIndex}, " +
                     $"tagStart='{tags[i].tagStart}', tagEnd='{tags[i].tagEnd}'");
        }

        // 模拟文本反向 (从右到左)
        char[] chars = plainText.ToCharArray();
        Array.Reverse(chars);
        string reversedText = new string(chars);
        Debug.Log("Reversed text: " + reversedText); // "!dlroW olleH"

        // 使用反向文本还原方法
        string restoredFromReverse = RestoreHtmlTagsFromReverseText(reversedText, 0, plainText.Length, tags);
        Debug.Log("Restored from reverse: " + restoredFromReverse);

        // 期望结果应该是: "!<color=red>dlroW</color> olleH"
        Debug.Log("Expected: !<color=red>dlroW</color> olleH");
    }
}
