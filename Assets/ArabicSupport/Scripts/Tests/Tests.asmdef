{"name": "ArabicSupport.Tests", "rootNamespace": "", "references": ["UnityEngine.TestRunner", "UnityEditor.TestRunner", "ArabicSupport", "UnityEditor.UI", "UnityEngine.UI"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}