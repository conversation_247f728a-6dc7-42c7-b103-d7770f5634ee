#ifndef UNIVERSAL_MYBRDFCORE_INCLUDED
#define UNIVERSAL_MYBRDFCORE_INCLUDED
//------------------------------------------------------------------
//Match
//------------------------------------------------------------------

//------------------------------------------------------------------
//BRDF Function
//------------------------------------------------------------------

// GGX / Trowbridge-<PERSON>itz
// [<PERSON> et al. 2007, "Microfacet models for refraction through rough surfaces"]
half D_GGX2( half Roughness, half NoH )
{
    half a = Roughness * Roughness;
    half a2 = a * a;
    half d = ( NoH * a2 - NoH ) * NoH + 1;	// 2 mad
    return a2 / ( 3.1415926f * d * d + 1e-5f);					// 4 mul, 1 rcp
    
}
//Mobile_GGX
half GGX_Mobile(half Roughness,float NoH)
{
    float OneMinusNoHSqr = 1.0-NoH*NoH;
    half a = Roughness * Roughness;
    half n = NoH *a;
    half p =a/(OneMinusNoHSqr + n*n);
    half d = p*p;
    return min(d,2048.0);
}

float D_UE_GGXAniso(float ax,float ay,float NoH,float XoH,float YoH)
{
    // float aspect = sqrt(1.0f-aniso*0.99f);
    // float ax =max(0.01f,Roughness/aspect);
    // float ay =max(0.01f,Roughness*aspect);

    #if 1
    float a2 = ax*ay;
    float3 V = float3(ay*XoH,ax*YoH,a2*NoH);
    float S = dot(V,V);
    return(1.0f/3.14159f)*a2*(a2/S)*(a2/S);
    #else

    float d = XoH*XoH / (ax*ax) + YoH*YoH / (ay*ay) + NoH*NoH;
    return 1.0f / ( 3.1415926 * ax*ay * d*d );
    #endif
}

float Vis_GGX(float Roughness, float NoV, float NoL)
{
    //https://graphicrants.blogspot.com/2013/08/specular-brdf-reference.html
    float a = Roughness * Roughness;
    float a2 = a * a;

    float d = a2 + (1 - a2) * NoV * NoV;
    //float G = (2 * NoV) / (NoV + sqrt(d));
    return 0.5 / ((NoV + sqrt(d)) * NoL);
}

float V_UE_SmithJointAniso(float ax,float ay,float NoV,float NoL,float XoV,float XoL,float YoV,float YoL)
{
    float Vis_SmithV = NoL *length(float3(ax*XoV,ay*YoV,NoV));
    float Vis_SmithL = NoV *length(float3(ax*XoL,ay*YoL,NoL));
    return 0.5f*rcp(Vis_SmithV+Vis_SmithL);
}

float Vis_Cloth(float NoV,float NoL)
{
    return rcp(4*(NoL+NoV-NoL*NoV));
}
//F0 =float3 SpecularColor = lerp(0.04f,albedo,metallic);//因为金属度和非金属(电解质物质)菲涅尔不一样
float3 F_UE_Schlick(float3 SpecularColor,float VoH)
{
    float Fc = Pow5(1-VoH);
    return saturate(50.0 * SpecularColor.g)*Fc+(1-Fc)*SpecularColor;
}

float D_UE_InvGGX(float a2,float NoH)
{
    float A=4;
    float d=(NoH-a2 *NoH)*NoH +a2;
    return rcp(3.14159f*(1+A*a2))*(1+4*a2*a2/(d*d));
}

#define SQR(x) ((x) * (x))
half My_fresnel(half x) 
{
    const half kf = 1.12;

    half p = 1.0 / SQR(kf);
    half num = 1.0 / SQR(x - kf) - p;
    half denom = 1.0 / SQR(1.0 - kf) - p;

    return num / denom;
}
half F_SebastienLagarde(half VoH)
{
    return exp2((-5.55473h * VoH - 6.98316h) * VoH);
}

#endif
