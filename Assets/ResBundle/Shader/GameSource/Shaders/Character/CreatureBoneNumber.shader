//用于DIY界面中生物骨骼上的数字显示
//by taecg
Shader "ONEMT/Character/CreatureBoneNumber"
{
    Properties
    {
        //: { "type": "texture2","prop":"_BaseMap","prop2":"_BaseColor", "label":"基础纹理"}
        _BaseColor("Base Color",color) = (1,1,1,1)
        [NoScaleOffset]_BaseMap("BaseMap", 2D) = "white" {}
        //: { "type": "int","prop":"_Index", "label":"Index","min":"0","max":"11"}
        [IntRange]_Index("Index",Range(0,11)) = 0
    }

    SubShader
    {
        Tags { "Queue"="Transparent+1" "RenderPipeline" = "UniversalPipeline"}
        Blend SrcAlpha OneMinusSrcAlpha
        ZTest Always

        Pass
        {
            Tags{"LightMode" = "BoneNumber"}
            HLSLPROGRAM
            // Required to compile gles 2.0 with standard srp library
            #pragma prefer_hlslcc gles
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 3.5 
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            struct Attributes
            {
                float4 positionOS       : POSITION;
                float2 uv               : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS       : SV_POSITION;
                float2 uv               : TEXCOORD0;
            };

            CBUFFER_START(UnityPerMaterial)
                float4 _BaseMap_ST;
                half4 _BaseColor;
                half _Index;
            CBUFFER_END
            TEXTURE2D (_BaseMap);SAMPLER(sampler_BaseMap);

            Varyings vert(Attributes v)
            {
                Varyings o = (Varyings)0;

                o.positionCS = TransformObjectToHClip(v.positionOS.xyz);
                half2 grid = half2(0.25,0.33333);
                half2 offset = half2(fmod(_Index,4),floor(_Index/4)) * grid;
                o.uv = v.uv * grid + offset;
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                half4 c;
                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv);
                c = baseMap * _BaseColor;
                return c;
            }
            ENDHLSL
        }
    }
    CustomEditor "taecg.tools.CustomShaderGUI"
}
