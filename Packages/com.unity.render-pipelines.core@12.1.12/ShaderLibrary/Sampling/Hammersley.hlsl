#ifndef UNITY_HAMMERSLEY_INCLUDED
#define UNITY_HAMMERSLEY_INCLUDED

#if SHADER_API_MOBILE || SHADER_API_GLES || SHADER_API_GLES3
#pragma warning (disable : 3205) // conversion of larger type to smaller
#endif

// Ref: http://holger.dammertz.org/stuff/notes_HammersleyOnHemisphere.html
uint ReverseBits32(uint bits)
{
#if (SHADER_TARGET >= 45)
    return reversebits(bits);
#else
    bits = (bits << 16) | (bits >> 16);
    bits = ((bits & 0x00ff00ff) << 8) | ((bits & 0xff00ff00) >> 8);
    bits = ((bits & 0x0f0f0f0f) << 4) | ((bits & 0xf0f0f0f0) >> 4);
    bits = ((bits & 0x33333333) << 2) | ((bits & 0xcccccccc) >> 2);
    bits = ((bits & 0x55555555) << 1) | ((bits & 0xaaaaaaaa) >> 1);
    return bits;
#endif
}

real VanDerCorputBase2(uint i)
{
    return ReverseBits32(i) * rcp(4294967296.0); // 2^-32
}

real2 Hammersley2dSeq(uint i, uint sequenceLength)
{
    return real2(real(i) / real(sequenceLength), VanDerCorputBase2(i));
}

#ifdef HAMMERSLEY_USE_CB
    #include "Packages/com.unity.render-pipelines.core/Runtime/ShaderLibrary/Sampling/Hammersley.cs.hlsl"
#else

static const real2 k_Hammersley2dSeq16[] = {
    real2(0.00000000, 0.00000000),
    real2(0.06250000, 0.50000000),
    real2(0.12500000, 0.25000000),
    real2(0.18750000, 0.75000000),
    real2(0.25000000, 0.12500000),
    real2(0.31250000, 0.62500000),
    real2(0.37500000, 0.37500000),
    real2(0.43750000, 0.87500000),
    real2(0.50000000, 0.06250000),
    real2(0.56250000, 0.56250000),
    real2(0.62500000, 0.31250000),
    real2(0.68750000, 0.81250000),
    real2(0.75000000, 0.18750000),
    real2(0.81250000, 0.68750000),
    real2(0.87500000, 0.43750000),
    real2(0.93750000, 0.93750000)
};

static const real2 k_Hammersley2dSeq32[] = {
    real2(0.00000000, 0.00000000),
    real2(0.03125000, 0.50000000),
    real2(0.06250000, 0.25000000),
    real2(0.09375000, 0.75000000),
    real2(0.12500000, 0.12500000),
    real2(0.15625000, 0.62500000),
    real2(0.18750000, 0.37500000),
    real2(0.21875000, 0.87500000),
    real2(0.25000000, 0.06250000),
    real2(0.28125000, 0.56250000),
    real2(0.31250000, 0.31250000),
    real2(0.34375000, 0.81250000),
    real2(0.37500000, 0.18750000),
    real2(0.40625000, 0.68750000),
    real2(0.43750000, 0.43750000),
    real2(0.46875000, 0.93750000),
    real2(0.50000000, 0.03125000),
    real2(0.53125000, 0.53125000),
    real2(0.56250000, 0.28125000),
    real2(0.59375000, 0.78125000),
    real2(0.62500000, 0.15625000),
    real2(0.65625000, 0.65625000),
    real2(0.68750000, 0.40625000),
    real2(0.71875000, 0.90625000),
    real2(0.75000000, 0.09375000),
    real2(0.78125000, 0.59375000),
    real2(0.81250000, 0.34375000),
    real2(0.84375000, 0.84375000),
    real2(0.87500000, 0.21875000),
    real2(0.90625000, 0.71875000),
    real2(0.93750000, 0.46875000),
    real2(0.96875000, 0.96875000)
};

static const real2 k_Hammersley2dSeq64[] = {
    real2(0.00000000, 0.00000000),
    real2(0.01562500, 0.50000000),
    real2(0.03125000, 0.25000000),
    real2(0.04687500, 0.75000000),
    real2(0.06250000, 0.12500000),
    real2(0.07812500, 0.62500000),
    real2(0.09375000, 0.37500000),
    real2(0.10937500, 0.87500000),
    real2(0.12500000, 0.06250000),
    real2(0.14062500, 0.56250000),
    real2(0.15625000, 0.31250000),
    real2(0.17187500, 0.81250000),
    real2(0.18750000, 0.18750000),
    real2(0.20312500, 0.68750000),
    real2(0.21875000, 0.43750000),
    real2(0.23437500, 0.93750000),
    real2(0.25000000, 0.03125000),
    real2(0.26562500, 0.53125000),
    real2(0.28125000, 0.28125000),
    real2(0.29687500, 0.78125000),
    real2(0.31250000, 0.15625000),
    real2(0.32812500, 0.65625000),
    real2(0.34375000, 0.40625000),
    real2(0.35937500, 0.90625000),
    real2(0.37500000, 0.09375000),
    real2(0.39062500, 0.59375000),
    real2(0.40625000, 0.34375000),
    real2(0.42187500, 0.84375000),
    real2(0.43750000, 0.21875000),
    real2(0.45312500, 0.71875000),
    real2(0.46875000, 0.46875000),
    real2(0.48437500, 0.96875000),
    real2(0.50000000, 0.01562500),
    real2(0.51562500, 0.51562500),
    real2(0.53125000, 0.26562500),
    real2(0.54687500, 0.76562500),
    real2(0.56250000, 0.14062500),
    real2(0.57812500, 0.64062500),
    real2(0.59375000, 0.39062500),
    real2(0.60937500, 0.89062500),
    real2(0.62500000, 0.07812500),
    real2(0.64062500, 0.57812500),
    real2(0.65625000, 0.32812500),
    real2(0.67187500, 0.82812500),
    real2(0.68750000, 0.20312500),
    real2(0.70312500, 0.70312500),
    real2(0.71875000, 0.45312500),
    real2(0.73437500, 0.95312500),
    real2(0.75000000, 0.04687500),
    real2(0.76562500, 0.54687500),
    real2(0.78125000, 0.29687500),
    real2(0.79687500, 0.79687500),
    real2(0.81250000, 0.17187500),
    real2(0.82812500, 0.67187500),
    real2(0.84375000, 0.42187500),
    real2(0.85937500, 0.92187500),
    real2(0.87500000, 0.10937500),
    real2(0.89062500, 0.60937500),
    real2(0.90625000, 0.35937500),
    real2(0.92187500, 0.85937500),
    real2(0.93750000, 0.23437500),
    real2(0.95312500, 0.73437500),
    real2(0.96875000, 0.48437500),
    real2(0.98437500, 0.98437500)
};

static const real2 k_Hammersley2dSeq256[] = {
    real2(0.00000000, 0.00000000),
    real2(0.00390625, 0.50000000),
    real2(0.00781250, 0.25000000),
    real2(0.01171875, 0.75000000),
    real2(0.01562500, 0.12500000),
    real2(0.01953125, 0.62500000),
    real2(0.02343750, 0.37500000),
    real2(0.02734375, 0.87500000),
    real2(0.03125000, 0.06250000),
    real2(0.03515625, 0.56250000),
    real2(0.03906250, 0.31250000),
    real2(0.04296875, 0.81250000),
    real2(0.04687500, 0.18750000),
    real2(0.05078125, 0.68750000),
    real2(0.05468750, 0.43750000),
    real2(0.05859375, 0.93750000),
    real2(0.06250000, 0.03125000),
    real2(0.06640625, 0.53125000),
    real2(0.07031250, 0.28125000),
    real2(0.07421875, 0.78125000),
    real2(0.07812500, 0.15625000),
    real2(0.08203125, 0.65625000),
    real2(0.08593750, 0.40625000),
    real2(0.08984375, 0.90625000),
    real2(0.09375000, 0.09375000),
    real2(0.09765625, 0.59375000),
    real2(0.10156250, 0.34375000),
    real2(0.10546875, 0.84375000),
    real2(0.10937500, 0.21875000),
    real2(0.11328125, 0.71875000),
    real2(0.11718750, 0.46875000),
    real2(0.12109375, 0.96875000),
    real2(0.12500000, 0.01562500),
    real2(0.12890625, 0.51562500),
    real2(0.13281250, 0.26562500),
    real2(0.13671875, 0.76562500),
    real2(0.14062500, 0.14062500),
    real2(0.14453125, 0.64062500),
    real2(0.14843750, 0.39062500),
    real2(0.15234375, 0.89062500),
    real2(0.15625000, 0.07812500),
    real2(0.16015625, 0.57812500),
    real2(0.16406250, 0.32812500),
    real2(0.16796875, 0.82812500),
    real2(0.17187500, 0.20312500),
    real2(0.17578125, 0.70312500),
    real2(0.17968750, 0.45312500),
    real2(0.18359375, 0.95312500),
    real2(0.18750000, 0.04687500),
    real2(0.19140625, 0.54687500),
    real2(0.19531250, 0.29687500),
    real2(0.19921875, 0.79687500),
    real2(0.20312500, 0.17187500),
    real2(0.20703125, 0.67187500),
    real2(0.21093750, 0.42187500),
    real2(0.21484375, 0.92187500),
    real2(0.21875000, 0.10937500),
    real2(0.22265625, 0.60937500),
    real2(0.22656250, 0.35937500),
    real2(0.23046875, 0.85937500),
    real2(0.23437500, 0.23437500),
    real2(0.23828125, 0.73437500),
    real2(0.24218750, 0.48437500),
    real2(0.24609375, 0.98437500),
    real2(0.25000000, 0.00781250),
    real2(0.25390625, 0.50781250),
    real2(0.25781250, 0.25781250),
    real2(0.26171875, 0.75781250),
    real2(0.26562500, 0.13281250),
    real2(0.26953125, 0.63281250),
    real2(0.27343750, 0.38281250),
    real2(0.27734375, 0.88281250),
    real2(0.28125000, 0.07031250),
    real2(0.28515625, 0.57031250),
    real2(0.28906250, 0.32031250),
    real2(0.29296875, 0.82031250),
    real2(0.29687500, 0.19531250),
    real2(0.30078125, 0.69531250),
    real2(0.30468750, 0.44531250),
    real2(0.30859375, 0.94531250),
    real2(0.31250000, 0.03906250),
    real2(0.31640625, 0.53906250),
    real2(0.32031250, 0.28906250),
    real2(0.32421875, 0.78906250),
    real2(0.32812500, 0.16406250),
    real2(0.33203125, 0.66406250),
    real2(0.33593750, 0.41406250),
    real2(0.33984375, 0.91406250),
    real2(0.34375000, 0.10156250),
    real2(0.34765625, 0.60156250),
    real2(0.35156250, 0.35156250),
    real2(0.35546875, 0.85156250),
    real2(0.35937500, 0.22656250),
    real2(0.36328125, 0.72656250),
    real2(0.36718750, 0.47656250),
    real2(0.37109375, 0.97656250),
    real2(0.37500000, 0.02343750),
    real2(0.37890625, 0.52343750),
    real2(0.38281250, 0.27343750),
    real2(0.38671875, 0.77343750),
    real2(0.39062500, 0.14843750),
    real2(0.39453125, 0.64843750),
    real2(0.39843750, 0.39843750),
    real2(0.40234375, 0.89843750),
    real2(0.40625000, 0.08593750),
    real2(0.41015625, 0.58593750),
    real2(0.41406250, 0.33593750),
    real2(0.41796875, 0.83593750),
    real2(0.42187500, 0.21093750),
    real2(0.42578125, 0.71093750),
    real2(0.42968750, 0.46093750),
    real2(0.43359375, 0.96093750),
    real2(0.43750000, 0.05468750),
    real2(0.44140625, 0.55468750),
    real2(0.44531250, 0.30468750),
    real2(0.44921875, 0.80468750),
    real2(0.45312500, 0.17968750),
    real2(0.45703125, 0.67968750),
    real2(0.46093750, 0.42968750),
    real2(0.46484375, 0.92968750),
    real2(0.46875000, 0.11718750),
    real2(0.47265625, 0.61718750),
    real2(0.47656250, 0.36718750),
    real2(0.48046875, 0.86718750),
    real2(0.48437500, 0.24218750),
    real2(0.48828125, 0.74218750),
    real2(0.49218750, 0.49218750),
    real2(0.49609375, 0.99218750),
    real2(0.50000000, 0.00390625),
    real2(0.50390625, 0.50390625),
    real2(0.50781250, 0.25390625),
    real2(0.51171875, 0.75390625),
    real2(0.51562500, 0.12890625),
    real2(0.51953125, 0.62890625),
    real2(0.52343750, 0.37890625),
    real2(0.52734375, 0.87890625),
    real2(0.53125000, 0.06640625),
    real2(0.53515625, 0.56640625),
    real2(0.53906250, 0.31640625),
    real2(0.54296875, 0.81640625),
    real2(0.54687500, 0.19140625),
    real2(0.55078125, 0.69140625),
    real2(0.55468750, 0.44140625),
    real2(0.55859375, 0.94140625),
    real2(0.56250000, 0.03515625),
    real2(0.56640625, 0.53515625),
    real2(0.57031250, 0.28515625),
    real2(0.57421875, 0.78515625),
    real2(0.57812500, 0.16015625),
    real2(0.58203125, 0.66015625),
    real2(0.58593750, 0.41015625),
    real2(0.58984375, 0.91015625),
    real2(0.59375000, 0.09765625),
    real2(0.59765625, 0.59765625),
    real2(0.60156250, 0.34765625),
    real2(0.60546875, 0.84765625),
    real2(0.60937500, 0.22265625),
    real2(0.61328125, 0.72265625),
    real2(0.61718750, 0.47265625),
    real2(0.62109375, 0.97265625),
    real2(0.62500000, 0.01953125),
    real2(0.62890625, 0.51953125),
    real2(0.63281250, 0.26953125),
    real2(0.63671875, 0.76953125),
    real2(0.64062500, 0.14453125),
    real2(0.64453125, 0.64453125),
    real2(0.64843750, 0.39453125),
    real2(0.65234375, 0.89453125),
    real2(0.65625000, 0.08203125),
    real2(0.66015625, 0.58203125),
    real2(0.66406250, 0.33203125),
    real2(0.66796875, 0.83203125),
    real2(0.67187500, 0.20703125),
    real2(0.67578125, 0.70703125),
    real2(0.67968750, 0.45703125),
    real2(0.68359375, 0.95703125),
    real2(0.68750000, 0.05078125),
    real2(0.69140625, 0.55078125),
    real2(0.69531250, 0.30078125),
    real2(0.69921875, 0.80078125),
    real2(0.70312500, 0.17578125),
    real2(0.70703125, 0.67578125),
    real2(0.71093750, 0.42578125),
    real2(0.71484375, 0.92578125),
    real2(0.71875000, 0.11328125),
    real2(0.72265625, 0.61328125),
    real2(0.72656250, 0.36328125),
    real2(0.73046875, 0.86328125),
    real2(0.73437500, 0.23828125),
    real2(0.73828125, 0.73828125),
    real2(0.74218750, 0.48828125),
    real2(0.74609375, 0.98828125),
    real2(0.75000000, 0.01171875),
    real2(0.75390625, 0.51171875),
    real2(0.75781250, 0.26171875),
    real2(0.76171875, 0.76171875),
    real2(0.76562500, 0.13671875),
    real2(0.76953125, 0.63671875),
    real2(0.77343750, 0.38671875),
    real2(0.77734375, 0.88671875),
    real2(0.78125000, 0.07421875),
    real2(0.78515625, 0.57421875),
    real2(0.78906250, 0.32421875),
    real2(0.79296875, 0.82421875),
    real2(0.79687500, 0.19921875),
    real2(0.80078125, 0.69921875),
    real2(0.80468750, 0.44921875),
    real2(0.80859375, 0.94921875),
    real2(0.81250000, 0.04296875),
    real2(0.81640625, 0.54296875),
    real2(0.82031250, 0.29296875),
    real2(0.82421875, 0.79296875),
    real2(0.82812500, 0.16796875),
    real2(0.83203125, 0.66796875),
    real2(0.83593750, 0.41796875),
    real2(0.83984375, 0.91796875),
    real2(0.84375000, 0.10546875),
    real2(0.84765625, 0.60546875),
    real2(0.85156250, 0.35546875),
    real2(0.85546875, 0.85546875),
    real2(0.85937500, 0.23046875),
    real2(0.86328125, 0.73046875),
    real2(0.86718750, 0.48046875),
    real2(0.87109375, 0.98046875),
    real2(0.87500000, 0.02734375),
    real2(0.87890625, 0.52734375),
    real2(0.88281250, 0.27734375),
    real2(0.88671875, 0.77734375),
    real2(0.89062500, 0.15234375),
    real2(0.89453125, 0.65234375),
    real2(0.89843750, 0.40234375),
    real2(0.90234375, 0.90234375),
    real2(0.90625000, 0.08984375),
    real2(0.91015625, 0.58984375),
    real2(0.91406250, 0.33984375),
    real2(0.91796875, 0.83984375),
    real2(0.92187500, 0.21484375),
    real2(0.92578125, 0.71484375),
    real2(0.92968750, 0.46484375),
    real2(0.93359375, 0.96484375),
    real2(0.93750000, 0.05859375),
    real2(0.94140625, 0.55859375),
    real2(0.94531250, 0.30859375),
    real2(0.94921875, 0.80859375),
    real2(0.95312500, 0.18359375),
    real2(0.95703125, 0.68359375),
    real2(0.96093750, 0.43359375),
    real2(0.96484375, 0.93359375),
    real2(0.96875000, 0.12109375),
    real2(0.97265625, 0.62109375),
    real2(0.97656250, 0.37109375),
    real2(0.98046875, 0.87109375),
    real2(0.98437500, 0.24609375),
    real2(0.98828125, 0.74609375),
    real2(0.99218750, 0.49609375),
    real2(0.99609375, 0.99609375)
};

#endif

// Loads elements from one of the precomputed tables for sample counts of 16, 32, 64, 256.
// Computes sample positions at runtime otherwise.
real2 Hammersley2d(uint i, uint sampleCount)
{
    switch (sampleCount)
    {
    #ifdef HAMMERSLEY_USE_CB
        case 16:  return hammersley2dSeq16[i].xy;
        case 32:  return hammersley2dSeq32[i].xy;
        case 64:  return hammersley2dSeq64[i].xy;
        case 256: return hammersley2dSeq256[i].xy;
    #else
        case 16:  return k_Hammersley2dSeq16[i];
        case 32:  return k_Hammersley2dSeq32[i];
        case 64:  return k_Hammersley2dSeq64[i];
        case 256: return k_Hammersley2dSeq256[i];
    #endif
        default:  return Hammersley2dSeq(i, sampleCount);
    }
}

#if SHADER_API_MOBILE || SHADER_API_GLES || SHADER_API_GLES3
#pragma warning (enable : 3205) // conversion of larger type to smaller
#endif

#endif // UNITY_HAMMERSLEY_INCLUDED
