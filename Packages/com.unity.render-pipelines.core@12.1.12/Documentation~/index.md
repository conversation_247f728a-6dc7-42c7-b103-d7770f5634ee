# SRP Core

![](https://blogs.unity3d.com/wp-content/uploads/2018/01/image5_rs.png)

The Scriptable Render Pipeline (SRP) is a Unity feature that allows you to write C# scripts to control the way Unity renders each frame. SRP Core is a package that makes it easier to create or customize an SRP.

SRP Core contains reusable code, including boilerplate code for working with platform-specific graphics APIs, utility functions for common rendering operations, and the shader libraries used in the High Definition Render Pipeline (HDRP) and Universal Render Pipeline (URP).

If you are creating a custom SRP from scratch or customizing a prebuilt SRP, using SRP Core will save you time.

For more information on SRP, including a guide to getting started with a custom SRP, see the [SRP documentation](https://docs.unity3d.com/Manual/ScriptableRenderPipeline.html). For more information on Unity's prebuilt SRPs, see the [Universal Render Pipeline (URP) documentation](https://docs.unity3d.com/Packages/com.unity.render-pipelines.universal@latest), or the [High Definition Render Pipeline (HDRP) documentation](https://docs.unity3d.com/Packages/com.unity.render-pipelines.high-definition@latest).
