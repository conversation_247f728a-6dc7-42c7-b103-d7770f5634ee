{"name": "com.code-philosophy.hybridclr", "version": "4.0.14", "displayName": "HybridCLR", "description": "HybridCLR is a fully featured, zero-cost, high-performance, low-memory solution for Unity's all-platform native c# hotupdate.", "category": "Runtime", "documentationUrl": "https://hybridclr.doc.code-philosophy.com/#/", "changelogUrl": "https://hybridclr.doc.code-philosophy.com/#/other/changelog", "licensesUrl": "https://github.com/focus-creative-games/hybridclr_unity/blob/main/LICENSE", "keywords": ["HybridCLR", "hotupdate", "hotfix", "focus-creative-games", "code-philosophy"], "author": {"name": "Code Philosophy", "email": "<EMAIL>", "url": "https://code-philosophy.com"}}